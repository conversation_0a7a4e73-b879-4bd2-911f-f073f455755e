
# aiTalk项目结构规则

## 开发目录
- 项目开发目录位于：`flutter/aitalk_app/`
- 所有代码开发和资源文件都应在此目录下进行管理

## 总体架构
- 采用Flutter跨平台开发框架
- 采用MVVM设计模式
- 采用离线优先的数据存储策略
- 采用模块化开发方式，每个功能模块单独维护

## 平台范围
- 当前仅针对 **iOS** 平台进行开发，暂不开发其他平台

## 目录结构
- `lib/`：主要源代码目录
  - `main.dart`：应用入口点
  - `app.dart`：应用根组件（待创建）
  - `routes.dart`：路由定义（待创建）
  - `core/`：核心功能，包括常量、主题等
    - `constants/`：常量定义
      - `colors.dart`：颜色常量
    - `themes/`：主题配置
  - `views/`：UI视图组件
    - `chats/`：聊天列表模块
      - `widgets/`：聊天模块专用组件
    - `calls/`：通话记录模块
      - `widgets/`：通话记录模块专用组件
    - `contacts/`：联系人模块
      - `widgets/`：联系人模块专用组件
    - `profile/`：我的个人页面模块
      - `widgets/`：个人页面模块专用组件
  - `widgets/`：可重用组件
    - `buttons/`：按钮组件
    - `cards/`：卡片组件
    - `dialogs/`：对话框组件
    - `lists/`：列表组件
    - `navigation/`：导航组件

## 数据流
- UI触发事件 -> Provider处理 -> 更新Models -> UI反映变化
- 蓝牙数据 -> 通过Service处理 -> 更新Models -> UI反映变化
# aiTalk项目结构规则

## 开发目录
- 项目开发目录位于：`flutter/aitalk_app/`
- 所有代码开发和资源文件都应在此目录下进行管理
## 总体架构
- 采用Flutter跨平台开发框架
- 采用MVVM设计模式
- 采用离线优先的数据存储策略
- 采用模块化开发方式，每个功能模块单独维护

- UI触发事件 -> Provider处理 -> 更新Models -> UI反映变化
- 蓝牙数据 -> 通过Service处理 -> 更新Models -> UI反映变化
