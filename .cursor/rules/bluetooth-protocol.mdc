---
alwaysApply: true
---

# aiTalk蓝牙通信协议规范

## 协议设计原则
- 可靠性：确保通信稳定且数据不丢失
- 低功耗：优化数据传输频率和数据包大小
- 安全性：本地数据加密保存

## 蓝牙服务架构
- 使用单例模式管理蓝牙连接
- 分层设计：
  - 硬件抽象层：直接与蓝牙API交互
  - 协议层：处理数据包的编解码
  - 服务层：提供业务功能接口

## 连接管理
- 自动重连机制：连接断开后自动尝试重连
- 连接状态监听：提供全局连接状态订阅
- 设备配对过程：需支持用户确认配对码

## 数据通信格式
- 数据包格式：
  ```
  [帧头2字节][命令1字节][数据长度1字节][数据内容][校验码1字节]
  ```
- 帧头固定为：`0xA5 0x5A`
- 校验码采用CRC-8算法

## TK8620芯片通信规则
- 芯片初始化流程：
  1. 发送唤醒指令
  2. 等待芯片响应
  3. 发送初始化参数
  4. 确认初始化完成
- 数据交互采用问答式，每个命令需等待响应或超时
- 命令集：
  - `0x01`：设备信息查询
  - `0x02`：聊天数据传输
  - `0x03`：通话控制
  - `0x04`：联系人同步
  - `0x05`：设备配置

## 异常处理
- 超时处理：命令发送后3秒无响应视为超时
- 重试机制：关键指令失败后自动重试，最多3次
- 错误日志：记录通信异常，方便调试

## 性能考量
- 大数据传输分包处理，每包不超过20字节
- 非关键数据使用异步发送，不阻塞UI线程
- 批量操作（如联系人同步）使用特殊的批处理命令
# aiTalk蓝牙通信协议规范

## 协议设计原则
- 可靠性：确保通信稳定且数据不丢失
- 低功耗：优化数据传输频率和数据包大小
- 安全性：本地数据加密保存

## 蓝牙服务架构
- 使用单例模式管理蓝牙连接
- 分层设计：
  - 硬件抽象层：直接与蓝牙API交互
  - 协议层：处理数据包的编解码
  - 服务层：提供业务功能接口

## 连接管理
- 自动重连机制：连接断开后自动尝试重连
- 连接状态监听：提供全局连接状态订阅
- 设备配对过程：需支持用户确认配对码

## 数据通信格式
- 数据包格式：
  ```
  [帧头2字节][命令1字节][数据长度1字节][数据内容][校验码1字节]
  ```
- 帧头固定为：`0xA5 0x5A`
- 校验码采用CRC-8算法

## TK8620芯片通信规则
- 芯片初始化流程：
  1. 发送唤醒指令
  2. 等待芯片响应
  3. 发送初始化参数
  4. 确认初始化完成
- 数据交互采用问答式，每个命令需等待响应或超时
- 命令集：
  - `0x01`：设备信息查询
  - `0x02`：聊天数据传输
  - `0x03`：通话控制
  - `0x04`：联系人同步
  - `0x05`：设备配置

## 异常处理
- 超时处理：命令发送后3秒无响应视为超时
- 重试机制：关键指令失败后自动重试，最多3次
- 错误日志：记录通信异常，方便调试

## 性能考量
- 大数据传输分包处理，每包不超过20字节
- 非关键数据使用异步发送，不阻塞UI线程
- 批量操作（如联系人同步）使用特殊的批处理命令
