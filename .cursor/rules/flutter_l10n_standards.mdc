---
alwaysApply: true
---

# Flutter国际化(l10n)开发规范

## 文件位置与结构

1. **国际化文件位置**：
   - ARB文件必须存放在 `lib/l10n/` 目录下
   - 生成的Dart国际化文件也位于 `lib/l10n/` 目录
   - 目录结构示例：
     ```
     lib/
     ├── l10n/
     │   ├── app_en.arb        # 英文资源
     │   ├── app_zh.arb        # 中文资源
     │   ├── app_localizations.dart        # 自动生成
     │   ├── app_localizations_en.dart     # 自动生成
     │   └── app_localizations_zh.dart     # 自动生成
     ```

## 命名规则

1. **ARB文件命名**：
   - 格式: `app_<locale>.arb` (例如: `app_zh.arb`, `app_en.arb`)

2. **国际化键命名**：
   - 使用下划线分隔命名法，不使用驼峰式
   - 格式: `<功能模块>_<描述>` (例如: `profile_language`, `chatList_searchHint`)
   - 带参数的字符串使用花括号占位符: `{参数名}` (例如: `"设备连接失败: {error}"`)

3. **禁止在代码中使用错误的命名格式**：
   - ❌ 错误: `loc.bottomNavProfile`, `loc.profileLanguage`
   - ✅ 正确: `loc.bottomNav_profile`, `loc.profile_language`

## ARB文件结构

```json
{
  "@@locale": "zh",
  "global_appTitle": "aiTalk",
  "simple_message": "简单消息",
  "message_with_param": "带参数消息: {param}"
}
```

## 在代码中使用国际化字符串

1. **简单字符串**：
   ```dart
   Text(AppLocalizations.of(context)!.simple_message)
   ```

2. **带参数字符串**：
   ```dart
   // ❌ 错误方式 - 不要使用replaceFirst
   Text(AppLocalizations.of(context)!.message_with_param.replaceFirst('{param}', value))
   
   // ✅ 正确方式 - 直接传递参数
   Text(AppLocalizations.of(context)!.message_with_param(value))
   ```

## 语言切换处理逻辑

1. **语言切换的实现**：
   - 在`main.dart`中使用ValueNotifier存储当前语言选择：
     ```dart
     final ValueNotifier<Locale?> localeNotifier = ValueNotifier(null);
     ```

   - MaterialApp应该监听此值的变化：
     ```dart
     ValueListenableBuilder<Locale?>(
       valueListenable: localeNotifier,
       builder: (context, locale, child) {
         return MaterialApp(
           locale: locale, // 如果为null则使用系统语言
           localizationsDelegates: AppLocalizations.localizationsDelegates,
           supportedLocales: AppLocalizations.supportedLocales,
           // ...
         );
       },
     )
     ```

2. **切换语言**：
   ```dart
   // 切换到中文
   localeNotifier.value = const Locale('zh');
   
   // 切换到英文
   localeNotifier.value = const Locale('en');
   
   // 跟随系统
   localeNotifier.value = null;
   ```

3. **处理UI重建**：
   - 语言切换会导致整个应用重建，确保状态适当保存
   - 避免在语言切换时执行复杂操作或重复API调用
   - 对于展示固定内容的小部件，考虑使用缓存避免重复加载

4. **注意事项**：
   - 所有显示文本必须使用国际化字符串，不得硬编码
   - 日期、数字、货币等格式化需使用intl包处理不同语言环境
   - 在包含大量文本的页面，预加载所需的国际化资源

## 生成国际化文件

1. **配置pubspec.yaml**：
   ```yaml
   dependencies:
     flutter_localizations:
       sdk: flutter
     intl: any
   
   flutter:
     generate: true
   
   flutter_intl:
     enabled: true
     main_locale: en
     arb_dir: lib/l10n
     output_dir: lib/l10n
   ```

2. **生成命令**：
   ```bash
   flutter gen-l10n
   ```
   
   或通过IDE的Flutter Intl插件自动生成

3. **生成文件**：
   - `app_localizations.dart`: 抽象基类
   - `app_localizations_<locale>.dart`: 各语言实现类

## 初始化配置

在`main.dart`中进行配置：
```dart
return MaterialApp(
  localizationsDelegates: AppLocalizations.localizationsDelegates,
  supportedLocales: AppLocalizations.supportedLocales,
  // ...
);
```

## 常见错误及解决方案

1. **错误**: `The getter 'xxx' isn't defined for the class 'AppLocalizations'`
   **解决方案**: 检查国际化键名是否正确，尤其注意使用下划线分隔而非驼峰式

2. **错误**: `The method 'replaceFirst' isn't defined for the class 'String Function(Object)'`
   **解决方案**: 对于带参数的国际化字符串，应直接传参而不是使用replaceFirst
# Flutter国际化(l10n)开发规范

## 文件位置与结构

1. **国际化文件位置**：
   - ARB文件必须存放在 `lib/l10n/` 目录下
   - 生成的Dart国际化文件也位于 `lib/l10n/` 目录
   - 目录结构示例：
     ```
     lib/
     ├── l10n/
     │   ├── app_en.arb        # 英文资源
     │   ├── app_zh.arb        # 中文资源
     │   ├── app_localizations.dart        # 自动生成
     │   ├── app_localizations_en.dart     # 自动生成
     │   └── app_localizations_zh.dart     # 自动生成
     ```

## 命名规则

1. **ARB文件命名**：
   - 格式: `app_<locale>.arb` (例如: `app_zh.arb`, `app_en.arb`)

2. **国际化键命名**：
   - 使用下划线分隔命名法，不使用驼峰式
   - 格式: `<功能模块>_<描述>` (例如: `profile_language`, `chatList_searchHint`)
   - 带参数的字符串使用花括号占位符: `{参数名}` (例如: `"设备连接失败: {error}"`)

3. **禁止在代码中使用错误的命名格式**：
   - ❌ 错误: `loc.bottomNavProfile`, `loc.profileLanguage`
   - ✅ 正确: `loc.bottomNav_profile`, `loc.profile_language`

## ARB文件结构

```json
{
  "@@locale": "zh",
  "global_appTitle": "aiTalk",
  "simple_message": "简单消息",
  "message_with_param": "带参数消息: {param}"
}
```

## 在代码中使用国际化字符串

1. **简单字符串**：
   ```dart
   Text(AppLocalizations.of(context)!.simple_message)
   ```

2. **带参数字符串**：
   ```dart
   // ❌ 错误方式 - 不要使用replaceFirst
   Text(AppLocalizations.of(context)!.message_with_param.replaceFirst('{param}', value))
   
   // ✅ 正确方式 - 直接传递参数
   Text(AppLocalizations.of(context)!.message_with_param(value))
   ```

## 语言切换处理逻辑

1. **语言切换的实现**：
   - 在`main.dart`中使用ValueNotifier存储当前语言选择：
     ```dart
     final ValueNotifier<Locale?> localeNotifier = ValueNotifier(null);
     ```

   - MaterialApp应该监听此值的变化：
     ```dart
     ValueListenableBuilder<Locale?>(
       valueListenable: localeNotifier,
       builder: (context, locale, child) {
         return MaterialApp(
           locale: locale, // 如果为null则使用系统语言
           localizationsDelegates: AppLocalizations.localizationsDelegates,
           supportedLocales: AppLocalizations.supportedLocales,
           // ...
         );
       },
     )
     ```

2. **切换语言**：
   ```dart
   // 切换到中文
   localeNotifier.value = const Locale('zh');
   
   // 切换到英文
   localeNotifier.value = const Locale('en');
   
   // 跟随系统
   localeNotifier.value = null;
   ```

3. **处理UI重建**：
   - 语言切换会导致整个应用重建，确保状态适当保存
   - 避免在语言切换时执行复杂操作或重复API调用
   - 对于展示固定内容的小部件，考虑使用缓存避免重复加载

4. **注意事项**：
   - 所有显示文本必须使用国际化字符串，不得硬编码
   - 日期、数字、货币等格式化需使用intl包处理不同语言环境
   - 在包含大量文本的页面，预加载所需的国际化资源

## 生成国际化文件

1. **配置pubspec.yaml**：
   ```yaml
   dependencies:
     flutter_localizations:
       sdk: flutter
     intl: any
   
   flutter:
     generate: true
   
   flutter_intl:
     enabled: true
     main_locale: en
     arb_dir: lib/l10n
     output_dir: lib/l10n
   ```

2. **生成命令**：
   ```bash
   flutter gen-l10n
   ```
   
   或通过IDE的Flutter Intl插件自动生成

3. **生成文件**：
   - `app_localizations.dart`: 抽象基类
   - `app_localizations_<locale>.dart`: 各语言实现类

## 初始化配置

在`main.dart`中进行配置：
```dart
return MaterialApp(
  localizationsDelegates: AppLocalizations.localizationsDelegates,
  supportedLocales: AppLocalizations.supportedLocales,
  // ...
);
```

## 常见错误及解决方案

1. **错误**: `The getter 'xxx' isn't defined for the class 'AppLocalizations'`
   **解决方案**: 检查国际化键名是否正确，尤其注意使用下划线分隔而非驼峰式

2. **错误**: `The method 'replaceFirst' isn't defined for the class 'String Function(Object)'`
   **解决方案**: 对于带参数的国际化字符串，应直接传参而不是使用replaceFirst
