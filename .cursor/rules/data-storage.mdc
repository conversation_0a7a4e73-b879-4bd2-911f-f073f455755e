---
alwaysApply: true
---

# aiTalk数据存储规范

## 基本原则
- 所有数据存储在本地，不进行网络传输
- 数据模型与存储逻辑分离
- 所有数据变更需通过服务层接口，不允许直接修改
- 敏感数据需要加密存储

## 数据模型
- 所有数据模型必须实现：
  - `toJson()`：将模型转换为JSON
  - `fromJson()`：从JSON创建模型
  - `copyWith()`：创建带有修改的新实例
- 使用不可变模型（Immutable Models）
- 所有模型属性默认为final，推荐使用构造函数初始化

## 本地存储技术选择
- 小型数据：SharedPreferences/Hive
- 结构化数据：SQLite
- 二进制数据（如图像）：文件系统

## 存储分类
- 应用配置：使用SharedPreferences
- 聊天记录：使用SQLite数据库
- 通话记录：使用SQLite数据库
- 联系人信息：使用SQLite数据库
- 用户偏好设置：使用SharedPreferences

## 数据库架构

### 联系人表（Contacts）
```sql
CREATE TABLE contacts (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  avatar_path TEXT,
  phone_number TEXT,
  created_at INTEGER NOT NULL,
  updated_at INTEGER NOT NULL
);
```

### 聊天消息表（Messages）
```sql
CREATE TABLE messages (
  id TEXT PRIMARY KEY,
  contact_id TEXT NOT NULL,
  content TEXT NOT NULL,
  timestamp INTEGER NOT NULL,
  is_read INTEGER NOT NULL DEFAULT 0,
  is_sent INTEGER NOT NULL DEFAULT 1,
  message_type INTEGER NOT NULL DEFAULT 0,
  FOREIGN KEY (contact_id) REFERENCES contacts (id)
);
```

### 通话记录表（Calls）
```sql
CREATE TABLE calls (
  id TEXT PRIMARY KEY,
  contact_id TEXT NOT NULL,
  call_type INTEGER NOT NULL,
  start_time INTEGER NOT NULL,
  duration INTEGER NOT NULL DEFAULT 0,
  is_missed INTEGER NOT NULL DEFAULT 0,
  FOREIGN KEY (contact_id) REFERENCES contacts (id)
);
```

## 数据迁移策略
- 使用版本号管理数据库架构
- 每次架构变更时提供明确的迁移脚本
- 版本升级时进行数据完整性校验

## 性能优化
- 批量操作使用事务
- 大量数据读取使用分页加载
- 频繁访问的数据加入内存缓存
- 长列表使用懒加载和虚拟滚动

## 数据备份与恢复
- 定期自动备份关键数据
- 提供手动备份/恢复功能
- 备份文件使用AES加密
- 记录备份元数据（时间、版本等）
# aiTalk数据存储规范

## 基本原则
- 所有数据存储在本地，不进行网络传输
- 数据模型与存储逻辑分离
- 所有数据变更需通过服务层接口，不允许直接修改
- 敏感数据需要加密存储

## 数据模型
- 所有数据模型必须实现：
  - `toJson()`：将模型转换为JSON
  - `fromJson()`：从JSON创建模型
  - `copyWith()`：创建带有修改的新实例
- 使用不可变模型（Immutable Models）
- 所有模型属性默认为final，推荐使用构造函数初始化

## 本地存储技术选择
- 小型数据：SharedPreferences/Hive
- 结构化数据：SQLite
- 二进制数据（如图像）：文件系统

## 存储分类
- 应用配置：使用SharedPreferences
- 聊天记录：使用SQLite数据库
- 通话记录：使用SQLite数据库
- 联系人信息：使用SQLite数据库
- 用户偏好设置：使用SharedPreferences

## 数据库架构

### 联系人表（Contacts）
```sql
CREATE TABLE contacts (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  avatar_path TEXT,
  phone_number TEXT,
  created_at INTEGER NOT NULL,
  updated_at INTEGER NOT NULL
);
```

### 聊天消息表（Messages）
```sql
CREATE TABLE messages (
  id TEXT PRIMARY KEY,
  contact_id TEXT NOT NULL,
  content TEXT NOT NULL,
  timestamp INTEGER NOT NULL,
  is_read INTEGER NOT NULL DEFAULT 0,
  is_sent INTEGER NOT NULL DEFAULT 1,
  message_type INTEGER NOT NULL DEFAULT 0,
  FOREIGN KEY (contact_id) REFERENCES contacts (id)
);
```

### 通话记录表（Calls）
```sql
CREATE TABLE calls (
  id TEXT PRIMARY KEY,
  contact_id TEXT NOT NULL,
  call_type INTEGER NOT NULL,
  start_time INTEGER NOT NULL,
  duration INTEGER NOT NULL DEFAULT 0,
  is_missed INTEGER NOT NULL DEFAULT 0,
  FOREIGN KEY (contact_id) REFERENCES contacts (id)
);
```

## 数据迁移策略
- 使用版本号管理数据库架构
- 每次架构变更时提供明确的迁移脚本
- 版本升级时进行数据完整性校验

## 性能优化
- 批量操作使用事务
- 大量数据读取使用分页加载
- 频繁访问的数据加入内存缓存
- 长列表使用懒加载和虚拟滚动

## 数据备份与恢复
- 定期自动备份关键数据
- 提供手动备份/恢复功能
- 备份文件使用AES加密
- 记录备份元数据（时间、版本等）
