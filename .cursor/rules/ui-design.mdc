---
alwaysApply: true
---

# aiTalk UI/UX设计规范

## 整体设计原则
- 遵循Material Design 3设计语言
- 支持深色/浅色主题切换
- 确保所有界面元素适配不同屏幕尺寸
- 优先考虑单手操作的便捷性

## 颜色规范
- 主色：`#2196F3`（蓝色）
- 强调色：`#FF4081`（粉色）
- 文本颜色：
  - 主要文本：`#212121`（深灰）
  - 次要文本：`#757575`（中灰）
  - 提示文本：`#9E9E9E`（浅灰）
- 背景色：
  - 主背景：`#FFFFFF`（白色）
  - 次背景：`#F5F5F5`（浅灰）
- 所有颜色必须通过主题系统引用，不得硬编码

## 字体规范
- 主字体：使用系统默认字体
- 字号定义：
  - 标题：20sp
  - 副标题：16sp
  - 正文：14sp
  - 说明文本：12sp

## 间距与布局
- 使用8dp作为基本间距单位
- 内边距统一为16dp
- 列表项高度为72dp
- 卡片圆角为8dp
- 使用Flexible和Expanded进行弹性布局

## 动画过渡
- 页面切换使用共享元素过渡
- 列表项动画时长：300ms
- 按钮反馈动画时长：150ms
- 使用Hero动画增强用户体验

## 特定功能模块UI规范

### 聊天列表
- 每个聊天项显示头像、名称、最新消息预览和时间
- 未读消息使用徽标显示数量
- 支持左滑操作（删除/置顶/标为已读）

### 通话记录
- 显示联系人头像、名称、通话类型图标、时间和时长
- 通话类型使用不同颜色区分：已接（绿色）、未接（红色）、已拨（蓝色）
- 支持左滑删除操作

### 联系人
- 按字母分组显示，右侧提供快速字母导航
- 联系人项显示头像和名称
- 支持搜索功能，搜索框固定在顶部

### 个人页面
- 顶部显示用户头像和名称
- 使用卡片式设计展示各项设置
- 确保所有设置项有明确的视觉反馈

## 可访问性
- 所有交互元素尺寸不小于48x48dp
- 确保颜色对比度符合WCAG 2.0 AA标准
- 支持系统字体大小调节
- 使用语义化组件，支持屏幕阅读器

## 加载状态和错误处理
- 使用骨架屏(Skeleton)作为内容加载占位符
- 加载操作超过300ms显示进度指示器
- 错误状态提供明确的视觉反馈和重试选项
- 空状态提供友好的提示和引导操作
# aiTalk UI/UX设计规范

## 整体设计原则
- 遵循Material Design 3设计语言
- 支持深色/浅色主题切换
- 确保所有界面元素适配不同屏幕尺寸
- 优先考虑单手操作的便捷性

## 颜色规范
- 主色：`#2196F3`（蓝色）
- 强调色：`#FF4081`（粉色）
- 文本颜色：
  - 主要文本：`#212121`（深灰）
  - 次要文本：`#757575`（中灰）
  - 提示文本：`#9E9E9E`（浅灰）
- 背景色：
  - 主背景：`#FFFFFF`（白色）
  - 次背景：`#F5F5F5`（浅灰）
- 所有颜色必须通过主题系统引用，不得硬编码

## 字体规范
- 主字体：使用系统默认字体
- 字号定义：
  - 标题：20sp
  - 副标题：16sp
  - 正文：14sp
  - 说明文本：12sp

## 间距与布局
- 使用8dp作为基本间距单位
- 内边距统一为16dp
- 列表项高度为72dp
- 卡片圆角为8dp
- 使用Flexible和Expanded进行弹性布局

## 动画过渡
- 页面切换使用共享元素过渡
- 列表项动画时长：300ms
- 按钮反馈动画时长：150ms
- 使用Hero动画增强用户体验

## 特定功能模块UI规范

### 聊天列表
- 每个聊天项显示头像、名称、最新消息预览和时间
- 未读消息使用徽标显示数量
- 支持左滑操作（删除/置顶/标为已读）

### 通话记录
- 显示联系人头像、名称、通话类型图标、时间和时长
- 通话类型使用不同颜色区分：已接（绿色）、未接（红色）、已拨（蓝色）
- 支持左滑删除操作

### 联系人
- 按字母分组显示，右侧提供快速字母导航
- 联系人项显示头像和名称
- 支持搜索功能，搜索框固定在顶部

### 个人页面
- 顶部显示用户头像和名称
- 使用卡片式设计展示各项设置
- 确保所有设置项有明确的视觉反馈

## 可访问性
- 所有交互元素尺寸不小于48x48dp
- 确保颜色对比度符合WCAG 2.0 AA标准
- 支持系统字体大小调节
- 使用语义化组件，支持屏幕阅读器

## 加载状态和错误处理
- 使用骨架屏(Skeleton)作为内容加载占位符
- 加载操作超过300ms显示进度指示器
- 错误状态提供明确的视觉反馈和重试选项
- 空状态提供友好的提示和引导操作
