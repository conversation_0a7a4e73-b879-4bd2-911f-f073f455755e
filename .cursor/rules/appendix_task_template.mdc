---
alwaysApply: true
---
任务文件模板

# 背景

文件名：[TASK_FILE_NAME]
创建于：[DATETIME]
创建者：[USER_NAME]
主分支：[MAIN_BRANCH]
任务分支：[TASK_BRANCH]
Yolo模式：[YOLO_MODE]

# 任务描述

[用户的完整任务描述]

# 项目概览

[用户输入的项目详情]

⚠️ 警告：永远不要修改此部分 ⚠️
[此部分应包含核心RIPER-5协议规则的摘要，确保它们可以在整个执行过程中被引用]
⚠️ 警告：永远不要修改此部分 ⚠️

# 分析

[代码调查结果]

# 提议的解决方案

[行动计划]

# 当前执行步骤："[步骤编号和名称]"

- 例如："2. 创建任务文件"

# 任务进度

[带时间戳的变更历史]

# 演进记录

[本项目目标 / 范围 / 技术变更记录]

# 最终审查

占位符定义
[TASK]：用户的任务描述（例如"修复缓存错误"）

[TASK_IDENTIFIER]：来自[TASK]的短语（例如"fix-cache-bug"）

[TASK_DATE_AND_NUMBER]：日期+序列（例如2025-01-14_1）

[TASK_FILE_NAME]：任务文件名，格式为YYYY-MM-DD_n（其中n是当天的任务编号）

[MAIN_BRANCH]：默认"main"

[TASK_FILE]：.tasks/[TASK_FILE_NAME]_[TASK_IDENTIFIER].md

[DATETIME]：当前日期和时间，格式为YYYY-MM-DD_HH:MM:SS

[DATE]：当前日期，格式为YYYY-MM-DD

[TIME]：当前时间，格式为HH:MM:SS

[USER_NAME]：当前系统用户名

[COMMIT_MESSAGE]：任务进度摘要

[SHORT_COMMIT_MESSAGE]：缩写的提交消息

[CHANGED_FILES]：修改文件的空格分隔列表

[YOLO_MODE]：Yolo模式状态（Ask|On|Off），控制是否需要用户确认每个执行步骤
Ask：在每个步骤之前询问用户是否需要确认
On：不需要用户确认，自动执行所有步骤（高风险模式）
Off：默认模式，要求每个重要步骤的用户确认
