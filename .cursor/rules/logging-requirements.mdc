
# 开发日志记录规范

**重要说明：所有日志文件必须命名为 `develop_x.md`，其中 x 是数字。数字越大表示日志越新，数字越小表示日志越旧。**

**当前项目日志文件：`develop_1.md` - 所有新的开发日志都应添加到此文件中，不要创建新文件。**

## 日志记录要求

在以下情况下，必须将重要信息记录到日志文件中：

- 完成新功能开发
- 修复 bug
- 重构代码
- API 变更
- 依赖库更新
- 性能优化
- 蓝牙协议实现或更改

## 日志文件位置

所有日志文件必须保存在项目根目录下的 `aitalk_app/log` 目录中。
路径：`/Users/<USER>/Desktop/paul.li/work/Code/flutter/aitalk_app/log`

## 日志文件命名格式

日志文件必须采用以下命名格式：
```
develop_x.md
```
其中 x 是递增的数字，较大的数字表示较新的日志。

例如：
```
develop_1.md  # 最早的日志
develop_2.md
develop_3.md  # 较新的日志
```

## 日志文件使用规则

**当前阶段规则：**
- 所有新的开发日志必须添加到 `develop_1.md` 文件中
- 新的日志条目应添加到文件顶部，保持最新的日志始终在最前面
- 每个日志条目之间用分隔线 `---` 隔开
- 不要创建新的日志文件，直到现有文件明确指示需要创建新文件

## 日志内容格式

每个日志条目应包含以下内容：

```markdown
# [功能/修复名称]

## 日期
YYYY-MM-DD

## 开发者
[开发者姓名]

## 变更摘要
[简要描述所做的变更]

## 详细说明
[详细描述变更内容、实现方法、影响范围等]

## 相关文件
- [文件路径1]
- [文件路径2]
...

## 注意事项
[需要特别注意的事项、潜在风险等]

---
```

## 注意事项

- 对于重大变更，日志应更加详细
- 应记录任何可能影响其他模块的变更
- 需记录性能指标变化
- 对于蓝牙通信相关变更，必须详细记录协议变更和兼容性信息
- **不要在日志文件中添加代码修改的详细细节**，仅需描述修改的目的、影响和结果
