/********************************************************************************************************
 * @file     OtaViewController.m
 *
 * @brief    A concise description.
 *
 * <AUTHOR> 梁家誌
 * @date     2020/7/14
 *
 * @par     Copyright (c) [2020], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#import "OtaViewController.h"
#import "ARShowTips.h"
#import "OTASettingsVC.h"
#import "UIColor+Telink.h"

#define kDefaultTimeout (10.0)

@interface OtaViewController ()<UITableViewDataSource, UITableViewDelegate>
/// Text layer used to set device name.
@property (strong, nonatomic) UILabel *nameLabel;
/// Text layer used to set state, disconnected or connected..
@property (strong, nonatomic) UILabel *stateLabel;
/// Text layer used to set device mtu.
@property (strong, nonatomic) UILabel *mtuLabel;
/// Button layer used to set connect title.
@property (strong, nonatomic) UIButton *connectButton;
/// UITableView layer used to show otaSettings.
@property (strong, nonatomic) UITableView *tableView;
/// UITextView layer used to show OTA log message.
@property (strong, nonatomic) UITextView *logTV;
/// Text layer used to set OTA progress.
@property (strong, nonatomic) UILabel *progressLabel;
/// UIProgressView layer used to set OTA progress.
@property (strong, nonatomic) UIProgressView *progressView;
/// Button layer used to show OTA start.
@property (strong, nonatomic) UIButton *startButton;
/// The otaSettings show on OtaViewController.
@property (strong, nonatomic) OTASettingsModel *otaSettings;
/// The countdown end time for connecting/disconnecting Bluetooth devices.
@property (strong, nonatomic) NSDate *endDate;
/// Countdown timer for connecting/disconnecting Bluetooth devices.
@property (strong, nonatomic) NSTimer *timer;
/// 切换到bootLoader模式后的蓝牙对象，也可能传进来的就是bootLoader模式的设备。
/// bootLoaderPeripheral断开才需要处理。
@property (nonatomic, strong) CBPeripheral *bootLoaderPeripheral;
@end

@implementation OtaViewController

/// Called after the view has been loaded.
/// For view controllers created in code, this is after -loadView.
/// For view controllers unarchived from a nib, this is after the view is set.
- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    //title
    self.title = @"OTA";
    //backgroundColor
    self.view.backgroundColor = UIColor.telinkTabBarBackgroundColor;
    //init UI
    [self addAndRefreshUI];
    //setBlock
    [self setBlock];
    //auto connect
    [self clickConnectButton:self.connectButton];
}

- (void)addAndRefreshUI {
    //init nameLabel
    _nameLabel = [[UILabel alloc] initWithFrame:CGRectMake(15, 5, SCREEN_WIDTH-30, 20)];
    _nameLabel.font = [UIFont systemFontOfSize:12];
    if (self.deviceName && self.deviceName.length > 0) {
        _nameLabel.text = [NSString stringWithFormat:@"Name: %@",self.deviceName];
    } else {
        _nameLabel.text = @"Name: unknown";
    }
    [self.view addSubview:self.nameLabel];

    //init stateLabel
    _stateLabel = [[UILabel alloc] initWithFrame:CGRectMake(15, 5 + 20 + 15, (SCREEN_WIDTH-30)/2, 20)];
    _stateLabel.font = [UIFont systemFontOfSize:12];
    [self.view addSubview:self.stateLabel];
    
    //init connectButton
    _connectButton = [UIButton buttonWithType:UIButtonTypeSystem];
    _connectButton.frame = CGRectMake(15+(SCREEN_WIDTH-30)/2, 5 + 20 + 10, (SCREEN_WIDTH-30)/2, 30);
    [_connectButton setTitle:@"CONNECT" forState:UIControlStateNormal];
    [_connectButton setTitleColor:[UIColor colorWithRed:0x4A/255.0 green:0x87/255.0 blue:0xEE/255.0 alpha:1] forState:UIControlStateNormal];
    _connectButton.titleLabel.font = [UIFont boldSystemFontOfSize: 16.0];
    _connectButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentRight;
    [_connectButton addTarget:self action:@selector(clickConnectButton:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.connectButton];
    
    //init mtuLabel
    _mtuLabel = [[UILabel alloc] initWithFrame:CGRectMake(15, 5 + 20 + 15 + 20 + 15, (SCREEN_WIDTH-30)/2, 20)];
    _mtuLabel.font = [UIFont systemFontOfSize:12];
    [self.view addSubview:self.mtuLabel];
    
    //refreshStateUI
    [self refreshStateUI];
    
    //load locationSettings or init default otaSettings
    OTASettingsModel *model = [[OTASettingsModel alloc] init];
    NSDictionary *locationSettings = [[NSUserDefaults standardUserDefaults] valueForKey:@"locationSettings"];
    if (locationSettings != nil) {
        [model inputSettingsDictionary:locationSettings];
    }
    //set current otaSettings
    _otaSettings = model;

    //init tableView
    UIFont *font = [UIFont systemFontOfSize:12];
    CGRect suggestedRect = [[self getSettingDetailString] boundingRectWithSize:CGSizeMake(SCREEN_WIDTH-17-16*2, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{ NSFontAttributeName : font } context:nil];
    _tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 5 + 20 + 5 + 20 + 15 + 20 + 15, SCREEN_WIDTH, 44+suggestedRect.size.height+30)];
    _tableView.delegate = self;
    _tableView.dataSource = self;
    _tableView.rowHeight = UITableViewAutomaticDimension;
    _tableView.backgroundColor = UIColor.telinkTabBarBackgroundColor;
    _tableView.sectionHeaderHeight = 15.0;
    _tableView.sectionFooterHeight = 15.0;
    _tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectZero];
    _tableView.scrollEnabled = NO;
    [self.view addSubview:_tableView];

    //init startButton
    _startButton = [UIButton buttonWithType:UIButtonTypeSystem];
    _startButton.frame = CGRectMake(0, SCREEN_HEIGHT - self.getRectNavAndStatusHightAndSafeDistanceBottom - 45, SCREEN_WIDTH, 45);
    [_startButton setTitle:@"START" forState:UIControlStateNormal];
    [_startButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [_startButton setBackgroundColor:UIColor.telinkButtonBlue];
    _startButton.titleLabel.font = [UIFont boldSystemFontOfSize: 16.0];
    [_startButton addTarget:self action:@selector(clickStartButton:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.startButton];

    //init progressView
    _progressView = [[UIProgressView alloc] initWithFrame:CGRectMake(0, SCREEN_HEIGHT - self.getRectNavAndStatusHightAndSafeDistanceBottom - 45 - 10 - 4, SCREEN_WIDTH, 4)];
    [self.view addSubview:self.progressView];

    //init progressLabel
    _progressLabel = [[UILabel alloc] initWithFrame:CGRectMake(15, SCREEN_HEIGHT - self.getRectNavAndStatusHightAndSafeDistanceBottom - 45 - 10 - 4 - 10 - 20, SCREEN_WIDTH - 30, 20)];
    _progressLabel.font = [UIFont systemFontOfSize:12];
    [self.view addSubview:self.progressLabel];
    //refreshProgressUIWithValue
    [self refreshProgressUIWithValue:0.0];

    //init logTV
    self.logTV = [[UITextView alloc] initWithFrame:CGRectMake(0, self.tableView.frame.origin.y + self.tableView.frame.size.height, SCREEN_WIDTH, self.progressLabel.frame.origin.y - (self.tableView.frame.origin.y + self.tableView.frame.size.height))];
    self.logTV.backgroundColor = UIColor.telinkTabBarshadowImageColor;
    self.logTV.layoutManager.allowsNonContiguousLayout= NO;//设置为 NO 后 UITextView 就不会再自己重置滑动了。
    self.logTV.layoutManager.usesFontLeading = NO;
    self.logTV.editable = NO;
    [self.view addSubview:self.logTV];
}

- (void)setBlock {
    __weak typeof(self) weakSelf = self;
    //set didDisconnectPeripheralResultBlock
    [TelinkBluetoothManager.shareCentralManager setDidDisconnectPeripheralResultBlock:^(CBPeripheral * _Nonnull peripheral, NSError * _Nullable error) {
        [weakSelf refreshStateUI];
    }];
}

- (void)refreshSettingsUI {
    [_tableView reloadData];
    UIFont *font = [UIFont systemFontOfSize:12];
    CGRect suggestedRect = [[self getSettingDetailString] boundingRectWithSize:CGSizeMake(SCREEN_WIDTH-17-16*2, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{ NSFontAttributeName : font } context:nil];
    _tableView.frame = CGRectMake(0, 5 + 20 + 5 + 20 + 15 + 20 + 15, SCREEN_WIDTH, 44+suggestedRect.size.height+30);
    _logTV.frame = CGRectMake(0, self.tableView.frame.origin.y + self.tableView.frame.size.height, SCREEN_WIDTH, self.progressLabel.frame.origin.y - (self.tableView.frame.origin.y + self.tableView.frame.size.height));
}

- (void)refreshStateUI {
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.peripheral.state == CBPeripheralStateConnected) {
            //set connectButton.title to DISCONNECT
            [self.connectButton setTitle:@"DISCONNECT" forState:UIControlStateNormal];
        } else {
            //set connectButton.title to CONNECT
            [self.connectButton setTitle:@"CONNECT" forState:UIControlStateNormal];
        }
        switch (self.peripheral.state) {
            case CBPeripheralStateDisconnected:
                //set stateLabel.text to disconnected
                self.stateLabel.text = @"State: disconnected";
                break;
            case CBPeripheralStateConnecting:
                //set stateLabel.text to connecting
                self.stateLabel.text = @"State: connecting";
                break;
            case CBPeripheralStateConnected:
                //set stateLabel.text to connected
                self.stateLabel.text = @"State: connected";
                break;
            case CBPeripheralStateDisconnecting:
                //set stateLabel.text to disconnecting
                self.stateLabel.text = @"State: disconnecting";
                break;
            default:
                break;
        }
    });
    [self refreshMtuUI];
}

- (void)refreshMtuUI {
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.peripheral.state == CBPeripheralStateConnected) {
            //show MTU number when the state of device is CBPeripheralStateConnected.
            self.mtuLabel.text = [NSString stringWithFormat:@"MTU: %lu",(unsigned long)[self.peripheral maximumWriteValueLengthForType:CBCharacteristicWriteWithoutResponse]];
        } else {
            //show MTU unknown when the state of device is not CBPeripheralStateConnected.
            self.mtuLabel.text = @"MTU: unknown";
        }
    });
}

/// Refresh Progress UI With Value
/// - Parameter value: progress float value
- (void)refreshProgressUIWithValue:(float)value {
    dispatch_async(dispatch_get_main_queue(), ^{
        self.progressView.progress = value;
        self.progressLabel.text = [NSString stringWithFormat:@"Progress: %d%%",(int)(value*100)];
    });
}

/// Add Log message To LogTF
/// - Parameter logString: log message
- (void)addLogToLogTF:(NSString *)logString {
    dispatch_async(dispatch_get_main_queue(), ^{
        self.logTV.text = [self.logTV.text stringByAppendingFormat:@"\n%@",logString];
        [self.logTV scrollRangeToVisible:NSMakeRange(self.logTV.text.length, 1)];
    });
}

/// Click Connect Button
/// - Parameter button: connect button
- (void)clickConnectButton:(UIButton *)button {
    if (self.timer) {
        return;
    }
    __weak typeof(self) weakSelf = self;
    if ([button.titleLabel.text isEqualToString:@"CONNECT"]) {
        //Connect the Bluetooth device
        [TelinkBluetoothManager.shareCentralManager connectPeripheral:self.peripheral timeout:kDefaultTimeout resultBlock:^(CBPeripheral * _Nonnull peripheral, NSError * _Nullable error) {
            TelinkDebugLog(@"uuid=%@ connect %@",peripheral.identifier.UUIDString,error == nil ? @"success" : @"fail");
            if (error) {
                [weakSelf.timer invalidate];
                weakSelf.timer = nil;
                dispatch_async(dispatch_get_main_queue(), ^{
                    [weakSelf refreshStateUI];
                });
            } else {
                [weakSelf discoverServices];
            }
        }];
    } else if ([button.titleLabel.text isEqualToString:@"DISCONNECT"]){
        //Disconnect Bluetooth device
        [TelinkBluetoothManager.shareCentralManager cancelConnectionPeripheral:self.peripheral timeout:kDefaultTimeout resultBlock:^(CBPeripheral * _Nonnull peripheral, NSError * _Nullable error) {
            TelinkDebugLog(@"uuid=%@ disconnect %@",peripheral.identifier.UUIDString,error == nil ? @"success" : @"fail");
            [weakSelf.timer invalidate];
            weakSelf.timer = nil;
            dispatch_async(dispatch_get_main_queue(), ^{
                [weakSelf refreshStateUI];
            });
        }];
    }

    //init endDate
    self.endDate = [NSDate dateWithTimeInterval:10 sinceDate:[NSDate date]];
    //add a timer for `refreshCountDownLabel`
    self.timer = [NSTimer timerWithTimeInterval:1.0 target:self selector:@selector(refreshCountDownLabel) userInfo:nil repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:self.timer forMode:NSRunLoopCommonModes];
    TelinkDebugLog(@"计时开始");
    [self.timer fire];
}

/// Click Start Button
/// - Parameter button: start button
- (void)clickStartButton:(UIButton *)button {
    //clean logTV
    self.logTV.text = @"";
    [self addLogToLogTF:@"start OTA"];
    //Check the connection status of Bluetooth devices
    if (TelinkBluetoothManager.shareCentralManager.currentPeripheral == nil || TelinkBluetoothManager.shareCentralManager.currentPeripheral.state != CBPeripheralStateConnected)  {
        [self addLogToLogTF:@"OTA fail: device not connected"];
        return;
    }
    //Check filePath of otaSettings.
    NSString *path = self.otaSettings.filePath;
    if (path == nil || path.length == 0) {
        [self addLogToLogTF:@"OTA fail: please select bin file"];
        return;
    }
    //Check binData of filePath.
    NSData *data = [FileDataSource.share getDataWithLastPathComponent:path];
    if (data == nil || data.length == 0) {
        [self addLogToLogTF:@"OTA fail: read bin file fail"];
        return;
    }
    //Check otaService.
    BOOL hasService = NO;
    CBService *otaService = nil;
    NSString *serviceUuid = self.otaSettings.serviceUuidString.uppercaseString;
    if (serviceUuid == nil || serviceUuid.length == 0) {
        serviceUuid = kOTAServiceUUID.uppercaseString;
    }
    for (CBService *service in self.peripheral.services) {
        if ([service.UUID.UUIDString.uppercaseString isEqualToString:serviceUuid]) {
            hasService = YES;
            otaService = service;
            break;
        }
    }
    if (hasService == NO) {
        if (serviceUuid == nil || serviceUuid.length == 0) {
            [self addLogToLogTF:@"OTA fail: can not find ota default service:1912"];
        } else {
            [self addLogToLogTF:[NSString stringWithFormat:@"OTA fail: can not find ota service:%@",serviceUuid]];
        }
        return;
    }
    //Check characteristicUuid.
    BOOL hasCharacteristic = NO;
    NSString *characteristicUuid = self.otaSettings.characteristicUuidString.uppercaseString;
    if (characteristicUuid == nil || characteristicUuid.length == 0) {
        characteristicUuid = kOTACharacteristicUUID.uppercaseString;
    }
    for (CBCharacteristic *chara in otaService.characteristics) {
        if ([chara.UUID.UUIDString.uppercaseString isEqualToString:characteristicUuid]) {
            hasCharacteristic = YES;
            break;
        }
    }
    if (hasCharacteristic == NO) {
        if (characteristicUuid == nil || characteristicUuid.length == 0) {
            [self addLogToLogTF:@"OTA fail: can not find ota default characteristic:2B12"];
        } else {
            [self addLogToLogTF:[NSString stringWithFormat:@"OTA fail: can not find ota service:%@",characteristicUuid]];
        }
        return;
    }
    //reset progress UI
    [self refreshProgressUIWithValue:0.0];
    //set start button to disable.
    [self setStartButtonEnable:NO];
    
    NSNumber *enable = [[NSUserDefaults standardUserDefaults] valueForKey:kBootloaderEnable];
    if (enable.boolValue) {
        //使能了BootLoader模式
        if ([self.deviceName isEqualToString:BootLoaderNameString]) {
            //已经是BootLoader模式
            [self addLogToLogTF:@"Device is in BootLoader"];
            [self otaActionWithPeripheral:self.peripheral data:data];
        } else {
            //需要切换到BootLoader模式
            [self addLogToLogTF:@"Switching to BootLoader"];
            __weak typeof(self) weakSelf = self;
            //将输入设备切换到BootLoader模式，并将BootLoader模式的设备返回APP。
            [TelinkOtaManager.share switchToBootLoaderPeripheralWithPeripheral:self.peripheral resultHandle:^(CBPeripheral * _Nullable backBootLoaderPeripheral) {
                if (backBootLoaderPeripheral) {
                    weakSelf.bootLoaderPeripheral = backBootLoaderPeripheral;
                    //Switch to BootLoader success
                    [weakSelf addLogToLogTF:@"Switch to BootLoader success"];
                    //connect
                    [weakSelf addLogToLogTF:@"Connecting to BootLoader"];
                    //Connect the Bluetooth device
                    [TelinkBluetoothManager.shareCentralManager connectPeripheral:backBootLoaderPeripheral timeout:kDefaultTimeout resultBlock:^(CBPeripheral * _Nonnull peripheral, NSError * _Nullable error) {
                        TelinkDebugLog(@"uuid=%@ connect %@",peripheral.identifier.UUIDString,error == nil ? @"success" : @"fail");
                        if (error) {
                            [weakSelf addLogToLogTF:@"Connect to BootLoader fail"];
                            [weakSelf setStartButtonEnable:YES];
                            [weakSelf addLogToLogTF:@"OTA fail"];
                        } else {
                            [weakSelf addLogToLogTF:@"Connect to BootLoader success"];
                            [weakSelf addLogToLogTF:@"BootLoader discovering services"];
                            //Discovering a list of all Bluetooth services in Peripheral
                            [TelinkBluetoothManager.shareCentralManager discoverServicesOfPeripheral:backBootLoaderPeripheral timeout:kDefaultTimeout resultBlock:^(CBPeripheral * _Nonnull peripheral, NSError * _Nullable error) {
                                if (error) {
                                    [weakSelf addLogToLogTF:@"BootLoader discover services fail"];
                                    [weakSelf setStartButtonEnable:YES];
                                    [weakSelf addLogToLogTF:@"OTA fail"];
                                } else {
                                    [weakSelf addLogToLogTF:@"BootLoader discover services success"];
                                    [weakSelf otaActionWithPeripheral:backBootLoaderPeripheral data:data];
                                }
                            }];
                        }
                    }];
                } else {
                    //Switch to BootLoader fail
                    [weakSelf addLogToLogTF:@"Switch to BootLoader fail"];
                    [weakSelf setStartButtonEnable:YES];
                    [weakSelf addLogToLogTF:@"OTA fail"];
                }
            }];
        }
    } else {
        //未使能BootLoader模式
        [self otaActionWithPeripheral:self.peripheral data:data];
    }
}

/// OTA action
/// - Parameters:
///   - peripheral: The CBPeripheral object of BLE device.
///   - data: bin data
- (void)otaActionWithPeripheral:(CBPeripheral *)peripheral data:(NSData *)data {
    //OTA started
    [self addLogToLogTF:@"OTA started"];
    NSDate *startDate = [NSDate date];
    TelinkOtaManager.share.settings = self.otaSettings;
    __weak typeof(self) weakSelf = self;
    //Start OTA
    [TelinkOtaManager.share startOTAWithOtaData:data peripheral:peripheral otaProgressAction:^(float progress) {
        [weakSelf refreshProgressUIWithValue:progress];
    } otaResultAction:^(CBPeripheral * _Nonnull peripheral, NSError * _Nullable error) {
        [weakSelf setStartButtonEnable:YES];
        [weakSelf refreshStateUI];
        [weakSelf setBlock];
        if (error) {
            [weakSelf addLogToLogTF:[NSString stringWithFormat:@"OTA fail: result code:0x%0lX - %@",(long)error.code,error.domain]];
        } else {
            [weakSelf addLogToLogTF:@"OTA success"];
        }
        [weakSelf addLogToLogTF:[NSString stringWithFormat:@"time: %.2fS",[[NSDate date] timeIntervalSinceDate:startDate]]];
    }];
}

/// Set Start Button Enable
/// - Parameter enable: state value
- (void)setStartButtonEnable:(BOOL)enable {
    __weak typeof(self) weakSelf = self;
    dispatch_async(dispatch_get_main_queue(), ^{
        //set startButton enabled
        // default is YES. if NO, ignores touch events and subclasses may draw differently.
        weakSelf.startButton.enabled = enable;
        if (enable) {
            //set BackgroundColor of enable
            [weakSelf.startButton setBackgroundColor:UIColor.telinkButtonBlue];
        } else {
            //set BackgroundColor of disable
            [weakSelf.startButton setBackgroundColor:UIColor.telinkButtonUnableBlue];
        }
    });
}

/// Discover Bluetooth Services
- (void)discoverServices {
    __weak typeof(self) weakSelf = self;
    //Discovering a list of all Bluetooth services in Peripheral
    [TelinkBluetoothManager.shareCentralManager discoverServicesOfPeripheral:self.peripheral timeout:kDefaultTimeout resultBlock:^(CBPeripheral * _Nonnull peripheral, NSError * _Nullable error) {
        //clean timer
        [weakSelf.timer invalidate];
        weakSelf.timer = nil;
        //refresh UI
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf refreshStateUI];
            [weakSelf.tableView reloadData];
        });
    }];
}

/// Refresh text of countdown
- (void)refreshCountDownLabel {
    NSString *title = self.connectButton.titleLabel.text;
    NSTimeInterval time = [_endDate timeIntervalSinceDate:[NSDate date]];
    TelinkDebugLog(@"计时一次,time=%f",time);
    int timeInt = ceil(time);
    if (timeInt <= 0) {
        //clean timer, not need to show countdown.
        [self.timer invalidate];
        self.timer = nil;
        TelinkDebugLog(@"计时结束");
        if ([title hasPrefix:@"CONNECT"]) {
            [self.connectButton setTitle:@"CONNECT" forState:UIControlStateNormal];
        } else if ([title hasPrefix:@"DISCONNECT"]) {
            [self.connectButton setTitle:@"DISCONNECT" forState:UIControlStateNormal];
        }
    } else {
        //timer is valid, need show countdown.
        if ([title hasPrefix:@"CONNECT"]) {
            [self.connectButton setTitle:[NSString stringWithFormat:@"CONNECT(%d)",timeInt] forState:UIControlStateNormal];
        } else if ([title hasPrefix:@"DISCONNECT"]) {
            [self.connectButton setTitle:[NSString stringWithFormat:@"DISCONNECT(%d)",timeInt] forState:UIControlStateNormal];
        }
    }
}

/// Called when the view is about to made visible, before it is added to the hierarchy.
/// Because the view is not yet in the hierarchy at the time this method is called, it
/// is too early in the appearance transition for many usages. Prefer -viewIsAppearing:
/// instead of this method when possible. Only use this method when its exact timing
/// before the appearance transition starts running is desired, such as to set up an
/// alongside animation with a transition coordinator, or as a counterpart for paired
/// code in a viewWillDisappear/viewDidDisappear callback that does not rely on the
/// view or view controller's trait collection or the view hierarchy.
- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    self.tabBarController.tabBar.hidden = YES;
    [self refreshSettingsUI];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    if([self isBeingDismissed] || [self isMovingFromParentViewController]) {
        // pop / dismiss
        if (BLE.currentPeripheral) {
            [BLE.centralManager cancelPeripheralConnection:BLE.currentPeripheral];
        }
        if (self.timer) {
            [self.timer invalidate];
            self.timer = nil;
        }
        TelinkDebugLog(@"发送断开请求");
        [BLE resetProperties];
    } else {
        // push /present from here
    }
}

/// add Bootloader state to setting detail string.
- (NSString *)getSettingDetailString {
    NSNumber *enable = [[NSUserDefaults standardUserDefaults] valueForKey:kBootloaderEnable];
    return [NSString stringWithFormat:@"%@\nBootloader:%@", self.otaSettings.getDetailString, enable.boolValue ? @"true" : @"false"];
}

-(void)dealloc{
    TelinkDebugLog(@"%s",__func__);
}

/// Row display. Implementers should *always* try to reuse cells by setting each cell's reuseIdentifier 
/// and querying for available reusable cells with dequeueReusableCellWithIdentifier:
/// Cell gets various attributes set automatically based on table (separators) 
/// and data source (accessory views, editing controls)
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSString * identifier= @"FileCell";
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:identifier];
    if (cell == nil) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:identifier];
    }
    cell.textLabel.text = @"OTA Settings";
    cell.detailTextLabel.text = [self getSettingDetailString];
    cell.detailTextLabel.font = [UIFont systemFontOfSize:12];
    cell.detailTextLabel.numberOfLines = 0;
    cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
    return cell;
}

/// numberOfRowsInSection
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 1;
}

/// didSelectRowAtIndexPath
/// Called after the user changes the selection.
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView cellForRowAtIndexPath:indexPath];
    cell.selected = NO;
    //init OTASettingsVC
    OTASettingsVC *vc = [[OTASettingsVC alloc] init];
    //set peripheral to OTASettingsVC
    vc.peripheral = self.peripheral;
    //set otaSettings to OTASettingsVC
    vc.otaSettings = [[OTASettingsModel alloc] initWithOTASettingsModel:self.otaSettings];
    __weak typeof(self) weakSelf = self;
    //set `backOtaSettings` to OTASettingsVC
    [vc setBackOtaSettings:^(OTASettingsModel * _Nonnull model) {
        weakSelf.otaSettings = model;
        NSDictionary *dict = [weakSelf.otaSettings outputSettingsDictionary];
        [[NSUserDefaults standardUserDefaults] setValue:dict forKey:@"locationSettings"];
        [[NSUserDefaults standardUserDefaults] synchronize];
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf.tableView reloadData];
            [ShowHandle alertShowTips:@"save success" doBlock:nil cancelBlock:nil];
        });
    }];
    //push to OTASettingsVC
    [self.navigationController pushViewController:vc animated:YES];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 10;
}

-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    UIFont *font = [UIFont systemFontOfSize:12];
    CGRect suggestedRect = [[self getSettingDetailString] boundingRectWithSize:CGSizeMake(SCREEN_WIDTH-17-16*2, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{ NSFontAttributeName : font } context:nil];
    return 44+suggestedRect.size.height;
}

@end
