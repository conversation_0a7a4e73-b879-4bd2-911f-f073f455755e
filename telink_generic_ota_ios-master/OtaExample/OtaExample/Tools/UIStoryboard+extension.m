/********************************************************************************************************
 * @file     UIStoryboard+extension.m 
 *
 * @brief    for TLSR chips
 *
 * <AUTHOR> 梁家誌
 * @date     2018/7/31
 *
 * @par     Copyright (c) [2021], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#import "UIStoryboard+extension.h"

@implementation UIStoryboard (extension)

+ (UIViewController *)initVC:(NSString *)name{
    return [self initVC:name storyboard:@"Main"];
}

+ (UIViewController *)initVC:(NSString *)name storyboard:(NSString *)storyboardName{
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:storyboardName bundle:nil];
    UIViewController *vc = [storyboard instantiateViewControllerWithIdentifier:name];
    return vc;
}

@end
