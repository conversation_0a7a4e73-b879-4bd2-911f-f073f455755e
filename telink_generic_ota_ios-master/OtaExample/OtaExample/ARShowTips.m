/********************************************************************************************************
 * @file     ShowTips.m
 *
 * @brief    A concise description.
 *
 * <AUTHOR> 梁家誌
 * @date     2020/7/14
 *
 * @par     Copyright (c) [2020], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#import "ARShowTips.h"

@interface ARShowTips ()
@property (nonatomic, strong) NSTimer *showTimer;

@end
@implementation ARShowTips
static ARShowTips *showTip = nil;
+ (instancetype)shareTips {
    static dispatch_once_t token;
    dispatch_once(&token, ^{
        showTip = [[ARShowTips alloc] init];
    });
    return showTip;
}

- (instancetype (^)(NSString *content))alertShowTips {
    return ^(NSString *content) {
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:nil message:content preferredStyle:UIAlertControllerStyleAlert];
        [alert addAction:[UIAlertAction actionWithTitle:@"back" style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
            [[UIApplication sharedApplication].keyWindow.rootViewController dismissViewControllerAnimated:YES completion:nil];
        }]];
        [[UIApplication sharedApplication].keyWindow.rootViewController presentViewController:alert animated:YES completion:nil];
        return self;
    };
}
- (instancetype (^)(void))alertDissmiss {
    return ^(){
        [[UIApplication sharedApplication].keyWindow.rootViewController dismissViewControllerAnimated:YES completion:nil];
        return self;
    };
}

- (void)alertShowTips:(NSString *)content doBlock:(void (^)(void))doBlock cancelBlock:(void (^)(void))cancelBlock {
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:nil message:content preferredStyle:UIAlertControllerStyleAlert];
    [alert addAction:[UIAlertAction actionWithTitle:@"Yes" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        if (doBlock) {
            doBlock();
        }
    }]];
    [alert addAction:[UIAlertAction actionWithTitle:@"cancel" style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        if (cancelBlock) {
            cancelBlock();
        }
    }]];
    [[UIApplication sharedApplication].keyWindow.rootViewController presentViewController:alert animated:YES completion:nil];
}
- (instancetype(^)(ShowType type, NSString *tip))showTip {
    return ^(ShowType type, NSString *tip) {
        kEndTimer(self.showTimer);
        UIWindow *window = [UIApplication sharedApplication].windows[0];
        if (!self.showTipsView.activity.isAnimating) {
            ARShowTipsView *st = [[ARShowTipsView alloc] initFrame:type];
            
            switch (type) {
                case ShowTypeNormal:
                case ShowTypeActivitySmall:
                    st.bounds = CGRectMake(0, 0, 240, 240);
                    st.center = window.center;
                    break;
                case ShowTypeActivityBig:
                    st.frame = CGRectMake(0, 66, kScreenWidth, kScreenHeight-66);
                    break;
                    break;
                default:    break;
            }
            self.showTipsView = st;
            [window addSubview:self.showTipsView];
            [self.showTipsView.activity startAnimating];
        }
        self.showTipsView.tipLab.text = tip;
        [self.showTipsView setNeedsDisplay];
        
        return self;
    };
}

- (instancetype (^)(void))hidden {
    return ^() {
        [self performSelectorOnMainThread:@selector(removeOnMain) withObject:nil waitUntilDone:YES];
        return self;
    };
}
- (void)removeOnMain {
    [self.showTipsView removeFromSuperview];
    UIWindow *window = [UIApplication sharedApplication].windows[0];
    [window setNeedsDisplay];
}
- (instancetype(^)(void))timeOut {
    return ^() {
        self.showTipsView.tipLab.text = @"Time out";
        [self.showTipsView.activity stopAnimating];
        [self.showTipsView setNeedsDisplay];
        self.delayHidden(1.);
        return self;
    };
}
- (instancetype (^)(int t))delayHidden {
    return ^ ARShowTips * (int t){
        self.showTimer = [NSTimer scheduledTimerWithTimeInterval:t target:self selector:@selector(hiddenOnMain) userInfo:nil repeats:NO];
        return self;
    };
}
- (void)hiddenOnMain {
    kEndTimer(self.showTimer);
    [self performSelectorOnMainThread:@selector(hiddenDo) withObject:nil waitUntilDone:YES];
}
- (void)hiddenDo {
    self.hidden();
}
@end
