/********************************************************************************************************
 * @file     BaseModel.m
 *
 * @brief    A concise description.
 *
 * <AUTHOR> 梁家誌
 * @date     2022/3/2
 *
 * @par     Copyright (c) [2022], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#import "BaseModel.h"

@implementation BaseModel

@end


@implementation Filter

- (instancetype)init {
    if (self = [super init]) {
        _RSSI = @(-100);
        _showName = nil;
    }
    return self;
}

- (instancetype)initWithFilter:(Filter *)filter {
    if (self = [super init]) {
        _RSSI = filter.RSSI;
        _showName = filter.showName;
    }
    return self;
}

- (NSDictionary *)getDictionaryOfFilter {
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    if (_RSSI) {
        dict[@"RSSI"] = _RSSI;
    }
    if (_showName) {
        dict[@"showName"] = _showName;
    }
    return dict;
}

- (void)setDictionaryToFilter:(NSDictionary *)dictionary {
    if (dictionary == nil || dictionary.allKeys.count == 0) {
        return;
    }
    NSArray *allKeys = dictionary.allKeys;
    if ([allKeys containsObject:@"RSSI"]) {
        _RSSI = dictionary[@"RSSI"];
    }
    if ([allKeys containsObject:@"showName"]) {
        _showName = dictionary[@"showName"];
    }
}

@end
