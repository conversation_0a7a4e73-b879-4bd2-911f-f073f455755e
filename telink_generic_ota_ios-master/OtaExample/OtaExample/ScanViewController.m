/********************************************************************************************************
 * @file     ScanViewController.m
 *
 * @brief    A concise description.
 *
 * <AUTHOR> 梁家誌
 * @date     2020/7/14
 *
 * @par     Copyright (c) [2020], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#import "ScanViewController.h"
#import "DeviceCell.h"
#import "ARShowTips.h"
#import "OtaViewController.h"
#import "BaseModel.h"
//#import "CustomAlertView.h"
#import "UIStoryboard+extension.h"
#import "UIView+Toast.h"

@interface ScanViewController ()<UITableViewDelegate, UITableViewDataSource>
/// Display device list
@property (weak, nonatomic) IBOutlet UITableView *tableView;
/// Display filter
@property (weak, nonatomic) IBOutlet UILabel *filterLabel;
/// Display RefreshControl
@property (strong, nonatomic) UIRefreshControl *control;
/// Display leftItem
@property (strong, nonatomic) UIBarButtonItem *leftItem;
/// Identify whether the device is being scanned
@property (assign, nonatomic) BOOL scanning;
/// Identify whether delayed UI refresh is required
@property (assign, nonatomic) BOOL needDelayReloadData;
/// Identify whether the delay period is in progress
@property (assign, nonatomic) BOOL isDelaying;
/// Cache scanned Bluetooth devices
@property (strong, nonatomic) NSMutableArray <TelinkDeviceModel *>*scanList;
/// Bluetooth devices displayed in UI
@property (strong, nonatomic) NSMutableArray <TelinkDeviceModel *>*showList;
/// Retrieve Bluetooth devices displayed in UI
@property (strong, nonatomic) NSMutableArray <CBPeripheral *>*retrievePeripheralList;
/// Filter conditions for Bluetooth devices
@property (strong, nonatomic) Filter *filter;
@end

@implementation ScanViewController

/// Called after the view has been loaded.
/// For view controllers created in code, this is after -loadView.
/// For view controllers unarchived from a nib, this is after the view is set.
- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    //needDelayReloadData default is NO.
    self.needDelayReloadData = NO;
    //isDelaying default is NO.
    self.isDelaying = NO;
    //title
    self.title = @"Device List";
    //scanList
    self.scanList = [NSMutableArray array];
    //showList
    self.showList = [NSMutableArray array];
    //retrievePeripheralList
    self.retrievePeripheralList = [NSMutableArray array];
    //Remove excess underline of tableView.
    self.tableView.tableFooterView = [[UIView alloc] init];
    //Integrated dropdown refresh
    [self setupRefresh];
    //Filter UIBarButtonItem
    UIBarButtonItem *item = [[UIBarButtonItem alloc] initWithTitle:@"Filter" style:UIBarButtonItemStylePlain target:self action:@selector(clickFilter)];
    //Refresh UIBarButtonItem
    self.leftItem = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemRefresh target:self action:@selector(clickScanButton)];
    //leftBarButtonItem
    self.navigationItem.leftBarButtonItem = self.leftItem;
    //rightBarButtonItem
    self.navigationItem.rightBarButtonItem = item;
}

/// Called when the view is about to made visible, before it is added to the hierarchy.
/// Because the view is not yet in the hierarchy at the time this method is called, it
/// is too early in the appearance transition for many usages. Prefer -viewIsAppearing:
/// instead of this method when possible. Only use this method when its exact timing
/// before the appearance transition starts running is desired, such as to set up an
/// alongside animation with a transition coordinator, or as a counterpart for paired
/// code in a viewWillDisappear/viewDidDisappear callback that does not rely on the
/// view or view controller's trait collection or the view hierarchy.
- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    //show tabBar
    self.tabBarController.tabBar.hidden = NO;
    //set block
    [self blockState];
    //load `MyFilter` data.
    NSDictionary *dict = [[NSUserDefaults standardUserDefaults] valueForKey:@"MyFilter"];
    //init Filter
    self.filter = [[Filter alloc] init];
    if (dict != nil) {
        //load data to Filter
        [self.filter setDictionaryToFilter:dict];
    }
    //refresh UI
    [self refreshFilterUI];
    //reload tableView UI
    [self delayReloadTableView];
}

/// refreshFilterUI
- (void)refreshFilterUI {
    NSString *tem = [NSString stringWithFormat:@"filter: rssi >= %@dBm", self.filter.RSSI];
    if (self.filter.showName && self.filter.showName.length) {
        tem = [tem stringByAppendingFormat:@", name contains: \"%@\"", self.filter.showName];
    }
    self.filterLabel.text = tem;
}

/// Push to FilterVC
- (void)clickFilter {
    //init FilterVC
    UIViewController *vc = [UIStoryboard initVC:@"FilterVC"];
    //pushViewController
    [self.navigationController pushViewController:vc animated:YES];
}

/// Click Scan Button
- (void)clickScanButton {
    if (self.scanning) {
        //停止扫描
        [self scanFinishAction];
    } else {
        //开始扫描
        if (@available(iOS 13.0, *)) {
            if ([CBCentralManager supportsFeatures:CBCentralManagerFeatureExtendedScanAndConnect]) {
                [self.navigationController.view makeToast:@"The iPhone supports ExtendedScanAndConnect."];
            } else {
                [self.navigationController.view makeToast:@"The iPhone does not support ExtendedScanAndConnect."];
            }
        } else {
            // Make toast
            [self.navigationController.view makeToast:@"The iOS version of the iPhone is less than iOS13, and it cannot be determined whether it supports ExtendedScanAndConnect."];
        }

        [self.control beginRefreshing];
        [self refreshStateChange:self.control];
    }
}

/// Scan Action
- (void)scanAction {
    __weak typeof(self) weakSelf = self;
    //set discoverPeripheralBlock
    [BLE startScanWithDiscoverPeripheralBlock:^(TelinkDeviceModel * _Nonnull deviceModel) {
        //Bluetooth devices with RSSI equal to 127 are invalid devices.
        if (deviceModel.RSSI.intValue == 127 || !weakSelf.scanning) {
            return;
        }
        //init tem scen list
        NSArray *scanList = [NSArray arrayWithArray:weakSelf.scanList];
        if ([scanList containsObject:deviceModel]) {
            //get oldModel
            TelinkDeviceModel *oldModel = [weakSelf.scanList objectAtIndex:[scanList indexOfObject:deviceModel]];
            //update RSSI of oldModel
            if (oldModel.RSSI.intValue < deviceModel.RSSI.intValue) {
                //replace
                [weakSelf.scanList replaceObjectAtIndex:[scanList indexOfObject:deviceModel] withObject:deviceModel];
                NSArray *showList = [NSArray arrayWithArray:weakSelf.showList];
                if ([showList containsObject:deviceModel]) {
                    //delayReloadTableView
                    [weakSelf delayReloadTableView];
                }
            }
        } else {
            //add new deviceModel
            [weakSelf.scanList addObject:deviceModel];
            //delayReloadTableView
            [weakSelf delayReloadTableView];
        }
    }];
}

/// Scan Finish Action
- (void)scanFinishAction {
    //Set scanning to NO.
    self.scanning = NO;
    [BLE resetProperties];
    //cancel delay timer of `scanFinishAction`
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(scanFinishAction) object:nil];
    });
    //init UIBarButtonItem
    self.leftItem = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemRefresh target:self action:@selector(clickScanButton)];
    //leftBarButtonItem
    self.navigationItem.leftBarButtonItem = self.leftItem;
    //endRefreshing
    [self.control endRefreshing];
}

/**
 *  集成下拉刷新
 */
- (void)setupRefresh {
    //1.添加刷新控件
    self.control = [[UIRefreshControl alloc] init];
    //backgroundColor
    self.control.backgroundColor = [UIColor clearColor];
    //attributedTitle
    self.control.attributedTitle = [[NSAttributedString alloc] initWithString:@"scanning..."];
    //addTarget
    [self.control addTarget:self action:@selector(refreshStateChange:) forControlEvents:UIControlEventValueChanged];
    //add UIRefreshControl
    [self.tableView addSubview:self.control];
}

/**
 *  UIRefreshControl进入刷新状态：加载最新的数据
 */
- (void)refreshStateChange:(UIRefreshControl *)control {
    //set scanning to YES.
    self.scanning = YES;
    //remove all objects of showList
    [self.showList removeAllObjects];
    //remove all objects of scanList
    [self.scanList removeAllObjects];
    //reloadData
    [self.tableView reloadData];
    //init UIBarButtonItem
    self.leftItem = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemPause target:self action:@selector(clickScanButton)];
    //scanAction
    [self scanAction];
    //beginRefreshing
    [self.control beginRefreshing];
    //add a new delay timer of `scanFinishAction`
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(scanFinishAction) object:nil];
        [self performSelector:@selector(scanFinishAction) withObject:nil afterDelay:10.0];
    });
    //leftBarButtonItem
    self.navigationItem.leftBarButtonItem = self.leftItem;
}

/// Refreshing the UI requires an interval of 0.1 seconds to prevent interface lag when there are 100 devices.
- (void)delayReloadTableView {
    @synchronized (self) {
        if (!self.needDelayReloadData) {
            //need delay is NO.
            //set needDelayReloadData to YES.
            self.needDelayReloadData = YES;
            //set isDelaying to NO.
            self.isDelaying = NO;
            //remove all objects of showList.
            [_showList removeAllObjects];
            //init all
            NSArray *all = [NSArray arrayWithArray:_scanList];
            for (TelinkDeviceModel *model in all) {
                //Check if it will be filtered out by RSSI filtering rules.
                if (model.RSSI.intValue >= self.filter.RSSI.intValue) {
                    BOOL add = NO;
                    //Check if it will be filtered out by name filtering rules.
                    if (self.filter.showName && self.filter.showName.length > 0) {
                        if ([model.showName containsString:self.filter.showName]) {
                            add = YES;
                        }
                    } else {
                        add = YES;
                    }
                    //Unfiltered devices
                    if (add) {
                        [self.showList addObject:model];
                    }
                }
            }
            //init retrieveConnectedPeripherals
            _retrievePeripheralList = [NSMutableArray arrayWithArray:BLE.retrieveConnectedPeripherals];
            
            __weak typeof(self) weakSelf = self;
            //add a new delay timer of `delayFinish`
            dispatch_async(dispatch_get_main_queue(), ^{
                [weakSelf.tableView reloadData];
                [NSObject cancelPreviousPerformRequestsWithTarget:weakSelf selector:@selector(delayFinish) object:nil];
                [weakSelf performSelector:@selector(delayFinish) withObject:nil afterDelay:0.1];
            });
        } else {
            //need delay is YES.
            if (!self.isDelaying) {
                self.isDelaying = YES;
            }
        }
    }
}

/// delay finish action
- (void)delayFinish {
    //set needDelayReloadData to NO.
    self.needDelayReloadData = NO;
    if (self.isDelaying) {
        [self delayReloadTableView];
    }
}

- (void)blockState {
    __weak typeof(self) weakSelf = self;
    //set updateCentralStateBlock
    [BLE setUpdateCentralStateBlock:^(CBManagerState state){
        //CBCentralManagerStatePoweredOn
        if (state==CBCentralManagerStatePoweredOn) {
            [weakSelf delayReloadTableView];
            //Determine if Bluetooth scanning action needs to be initiated
            dispatch_async(dispatch_get_main_queue(), ^{
                if (weakSelf.scanning) {
                    [weakSelf clickScanButton];
                }
            });
            //CBCentralManagerStatePoweredOff
        } else if (state==CBCentralManagerStatePoweredOff){
            //show alert
            dispatch_async(dispatch_get_main_queue(), ^{
                [ShowHandle alertShowTips:@"请到设置界面打开蓝牙！" doBlock:nil cancelBlock:nil];
            });
            //CBCentralManagerStateUnauthorized
        } else if (state == CBCentralManagerStateUnauthorized){
            //show alert
            dispatch_async(dispatch_get_main_queue(), ^{
                [ShowHandle alertShowTips:@"请到设置界面给TelinkOTA授权蓝牙使用权限！" doBlock:nil cancelBlock:nil];
            });
        }
    }];
    //set didDisconnectPeripheralResultBlock
    [TelinkBluetoothManager.shareCentralManager setDidDisconnectPeripheralResultBlock:^(CBPeripheral * _Nonnull peripheral, NSError * _Nullable error) {
        [weakSelf delayReloadTableView];
    }];
}

/// numberOfSectionsInTableView
/// Default is 1 if not implemented
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    //showList+retrievePeripheralList
    return 2;
}

/// numberOfRowsInSection
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section) return self.showList.count;
    return self.retrievePeripheralList.count;
}

/// Row display. Implementers should *always* try to reuse cells by setting each cell's reuseIdentifier 
/// and querying for available reusable cells with dequeueReusableCellWithIdentifier:
/// Cell gets various attributes set automatically based on table (separators) 
/// and data source (accessory views, editing controls)
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    DeviceCell *cell = [tableView dequeueReusableCellWithIdentifier:@"DeviceCell"];
    if (indexPath.section) {
        //showList
        if (self.showList.count > indexPath.row) {
            TelinkDeviceModel *device = self.showList[indexPath.row];
            //update rssi text
            cell.rssi.text = [NSString stringWithFormat:@"RSSI: %@",device.RSSI];
            if (device.showName == nil || device.showName.length == 0) {
                //deviceName is not exist.
                cell.deviceName.text = @"N/A";
            } else {
                //deviceName is exist.
                cell.deviceName.text = device.showName;
            }
        }
    } else {
        //retrievePeripheralList
        if (self.retrievePeripheralList.count > indexPath.row) {
            CBPeripheral *per = self.retrievePeripheralList[indexPath.row];
            //update rssi text
            cell.rssi.text = [NSString stringWithFormat:@"RSSI: Unknown"];
            if (per.name == nil || per.name.length == 0) {
                //deviceName is not exist.
                cell.deviceName.text = @"N/A";
            } else {
                //deviceName is exist.
                cell.deviceName.text = per.name;
            }
        }
    }
    return cell;
}

/// titleForHeaderInSection
/// fixed font style. use custom view (UILabel) if you want something different
- (NSString *)tableView:(UITableView *)tableView titleForHeaderInSection:(NSInteger)section {
    if (section) {
        //header title of showList
        return @"Devices nearby";
    }else{
        //header title of retrievePeripheralList
        return @"Devices has been connected";
    }
}

/// didSelectRowAtIndexPath
/// Called after the user changes the selection.
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    //scanFinishAction
    [self scanFinishAction];
    //get DeviceCell
    DeviceCell *cell = [tableView cellForRowAtIndexPath:indexPath];
    //set selected to NO.
    cell.selected = NO;
    NSString *deviceName = nil;
    CBPeripheral *per;
    if (indexPath.section) {
        //device of showList
        TelinkDeviceModel *deviceModel = self.showList[indexPath.row];
        per = [deviceModel peripheral];
        //show deviceModel.showName
        deviceName = deviceModel.showName;
    }else{
        //device of retrievePeripheralList
        per = self.retrievePeripheralList[indexPath.row];
        //show per.name
        deviceName = per.name;
    }
    //init OtaViewController
    OtaViewController *vc = [[OtaViewController alloc] init];
    //set peripheral of OtaViewController.
    vc.peripheral = per;
    //set deviceName of OtaViewController.
    vc.deviceName = deviceName;
    //pushViewController
    [self.navigationController pushViewController:vc animated:YES];
}

/// editActionsForRowAtIndexPath
/// This method supersedes -tableView:titleForDeleteConfirmationButtonForRowAtIndexPath: if return value is non-nil
- (nullable NSArray<UITableViewRowAction *> *)tableView:(UITableView *)tableView editActionsForRowAtIndexPath:(NSIndexPath *)indexPath {
    __weak typeof(self) weakSelf = self;
    //init UITableViewRowAction for delete showList
    UITableViewRowAction *delete = [UITableViewRowAction rowActionWithStyle:UITableViewRowActionStyleDefault title:@"Del" handler:^(UITableViewRowAction * _Nonnull action, NSIndexPath * _Nonnull indexPath) {
        if (indexPath.section) {
            //app can delete device of retrievePeripheralList
            TelinkDeviceModel *model = self.showList[indexPath.row];
            //When the Bluetooth device is in a connected state,
            //the APP will call the method of disconnecting the Bluetooth connection.
            if (model.peripheral.state == CBPeripheralStateConnected) {
                //cancelPeripheralConnection
                [BLE.centralManager cancelPeripheralConnection:model.peripheral];
            }
            //remove device from showList.
            [self.showList removeObjectAtIndex:indexPath.row];
            //remove device from scanList.
            [self.scanList removeObjectAtIndex:indexPath.row];
        } else {
            //app can not delete device of retrievePeripheralList
            return;
        }
        [weakSelf.tableView reloadData];
    }];
    return @[delete];
}

@end
