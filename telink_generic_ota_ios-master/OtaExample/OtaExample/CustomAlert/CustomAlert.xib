<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="19529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19519"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="CustomAlert">
            <rect key="frame" x="0.0" y="0.0" width="280" height="180"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4hL-y4-unJ">
                    <rect key="frame" x="0.0" y="0.0" width="280" height="130.5"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Set Default TTL" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="uZC-49-ucm">
                            <rect key="frame" x="15" y="15" width="250" height="20.5"/>
                            <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                            <color key="textColor" red="0.20784313730000001" green="0.20784313730000001" blue="0.20784313730000001" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="rss" translatesAutoresizingMaskIntoConstraints="NO" id="Xvz-nd-9q1">
                            <rect key="frame" x="10" y="60.5" width="25" height="25"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="25" id="UBO-4g-90F"/>
                                <constraint firstAttribute="height" constant="25" id="zlu-zZ-7rm"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="RSSI:" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="KZv-Xt-5bu">
                            <rect key="frame" x="45" y="64.5" width="34.5" height="17"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="34.5" id="QKD-0j-MSz"/>
                                <constraint firstAttribute="height" constant="17" id="i0w-8t-h98"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <color key="textColor" red="0.20784313730000001" green="0.20784313730000001" blue="0.20784313730000001" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <slider opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" value="100" minValue="40" maxValue="100" translatesAutoresizingMaskIntoConstraints="NO" id="adj-7J-Phv">
                            <rect key="frame" x="82.5" y="58" width="124.5" height="31"/>
                            <connections>
                                <action selector="changeRSSI:" destination="iN0-l3-epB" eventType="valueChanged" id="gsx-Le-F0W"/>
                            </connections>
                        </slider>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="-100dBm" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="t8r-tp-Ufq">
                            <rect key="frame" x="210" y="64.5" width="60" height="17"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="17" id="0aZ-c0-Dam"/>
                                <constraint firstAttribute="width" constant="60" id="cf5-NN-yg0"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <color key="textColor" red="0.20784313730000001" green="0.20784313730000001" blue="0.20784313730000001" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="sousuo" translatesAutoresizingMaskIntoConstraints="NO" id="gAC-qT-7kx">
                            <rect key="frame" x="10" y="95.5" width="25" height="25"/>
                        </imageView>
                        <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="line" placeholder="Filter by name" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="uhS-Pb-7t3">
                            <rect key="frame" x="45" y="95" width="225" height="26.5"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <textInputTraits key="textInputTraits"/>
                        </textField>
                    </subviews>
                    <constraints>
                        <constraint firstAttribute="bottom" secondItem="gAC-qT-7kx" secondAttribute="bottom" constant="10" id="0Ni-x0-jGW"/>
                        <constraint firstAttribute="trailing" secondItem="t8r-tp-Ufq" secondAttribute="trailing" constant="10" id="1bV-iR-QEg"/>
                        <constraint firstItem="uhS-Pb-7t3" firstAttribute="centerY" secondItem="gAC-qT-7kx" secondAttribute="centerY" id="3hD-1e-Hdn"/>
                        <constraint firstItem="adj-7J-Phv" firstAttribute="centerY" secondItem="Xvz-nd-9q1" secondAttribute="centerY" id="6dD-1K-8aG"/>
                        <constraint firstItem="adj-7J-Phv" firstAttribute="leading" secondItem="KZv-Xt-5bu" secondAttribute="trailing" constant="5" id="73T-3Q-pzF"/>
                        <constraint firstItem="KZv-Xt-5bu" firstAttribute="centerY" secondItem="Xvz-nd-9q1" secondAttribute="centerY" id="Bn2-Q4-Gp7"/>
                        <constraint firstItem="Xvz-nd-9q1" firstAttribute="leading" secondItem="4hL-y4-unJ" secondAttribute="leading" constant="10" id="EX3-d1-9KU"/>
                        <constraint firstItem="uZC-49-ucm" firstAttribute="leading" secondItem="4hL-y4-unJ" secondAttribute="leading" constant="15" id="Fqk-Cc-EpW"/>
                        <constraint firstItem="gAC-qT-7kx" firstAttribute="top" secondItem="Xvz-nd-9q1" secondAttribute="bottom" constant="10" id="Hg9-mz-U93"/>
                        <constraint firstItem="Xvz-nd-9q1" firstAttribute="top" secondItem="uZC-49-ucm" secondAttribute="bottom" constant="25" id="PRk-XN-HR0"/>
                        <constraint firstItem="gAC-qT-7kx" firstAttribute="width" secondItem="Xvz-nd-9q1" secondAttribute="width" id="dGg-rA-u1d"/>
                        <constraint firstAttribute="trailing" secondItem="uZC-49-ucm" secondAttribute="trailing" constant="15" id="dQn-24-7LM"/>
                        <constraint firstItem="t8r-tp-Ufq" firstAttribute="leading" secondItem="adj-7J-Phv" secondAttribute="trailing" constant="5" id="fHg-Ze-89T"/>
                        <constraint firstItem="uhS-Pb-7t3" firstAttribute="leading" secondItem="KZv-Xt-5bu" secondAttribute="leading" id="lA1-pJ-Fj0"/>
                        <constraint firstItem="gAC-qT-7kx" firstAttribute="leading" secondItem="Xvz-nd-9q1" secondAttribute="leading" id="lvb-5J-Kut"/>
                        <constraint firstItem="gAC-qT-7kx" firstAttribute="height" secondItem="Xvz-nd-9q1" secondAttribute="height" id="mAX-Sh-Eo2"/>
                        <constraint firstItem="uZC-49-ucm" firstAttribute="top" secondItem="4hL-y4-unJ" secondAttribute="top" constant="15" id="rFs-1K-s6c"/>
                        <constraint firstItem="KZv-Xt-5bu" firstAttribute="leading" secondItem="Xvz-nd-9q1" secondAttribute="trailing" constant="10" id="rUj-Q6-I2t"/>
                        <constraint firstItem="t8r-tp-Ufq" firstAttribute="centerY" secondItem="Xvz-nd-9q1" secondAttribute="centerY" id="rbS-iw-7x9"/>
                        <constraint firstItem="t8r-tp-Ufq" firstAttribute="trailing" secondItem="uhS-Pb-7t3" secondAttribute="trailing" id="sF9-LT-m87"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rak-jr-3bA" userLabel="line">
                    <rect key="frame" x="0.0" y="129" width="280" height="1"/>
                    <color key="backgroundColor" red="0.83529411760000005" green="0.83529411760000005" blue="0.83529411760000005" alpha="1" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="T72-Ob-YOv"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="WsB-Wq-67Q">
                    <rect key="frame" x="0.0" y="130" width="280" height="50"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="N5V-5A-uEU">
                            <rect key="frame" x="0.0" y="0.0" width="139.5" height="50"/>
                            <fontDescription key="fontDescription" type="system" pointSize="15"/>
                            <state key="normal" title="CANCEL">
                                <color key="titleColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="calibratedRGB"/>
                            </state>
                            <state key="selected">
                                <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                            </state>
                        </button>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="DfB-pK-OcX" userLabel="line">
                            <rect key="frame" x="139.5" y="0.0" width="1" height="50"/>
                            <color key="backgroundColor" red="0.83529411760000005" green="0.83529411760000005" blue="0.83529411760000005" alpha="1" colorSpace="calibratedRGB"/>
                        </view>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="X3M-EX-NxT">
                            <rect key="frame" x="140.5" y="0.0" width="139.5" height="50"/>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <fontDescription key="fontDescription" type="system" pointSize="15"/>
                            <state key="normal" title="CONFIRM">
                                <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </state>
                            <state key="selected">
                                <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                            </state>
                        </button>
                    </subviews>
                    <constraints>
                        <constraint firstItem="N5V-5A-uEU" firstAttribute="top" secondItem="WsB-Wq-67Q" secondAttribute="top" id="0Ow-3p-l0g"/>
                        <constraint firstAttribute="bottom" secondItem="N5V-5A-uEU" secondAttribute="bottom" id="5vA-de-daA"/>
                        <constraint firstItem="DfB-pK-OcX" firstAttribute="top" secondItem="N5V-5A-uEU" secondAttribute="top" id="EwY-23-p4c"/>
                        <constraint firstItem="X3M-EX-NxT" firstAttribute="top" secondItem="WsB-Wq-67Q" secondAttribute="top" id="GQg-v3-PVb"/>
                        <constraint firstAttribute="trailing" secondItem="X3M-EX-NxT" secondAttribute="trailing" id="JYx-zv-j9l"/>
                        <constraint firstItem="X3M-EX-NxT" firstAttribute="leading" secondItem="N5V-5A-uEU" secondAttribute="trailing" constant="1" id="KK8-WV-0pM"/>
                        <constraint firstItem="N5V-5A-uEU" firstAttribute="leading" secondItem="WsB-Wq-67Q" secondAttribute="leading" id="aPH-ld-Cee"/>
                        <constraint firstAttribute="bottom" secondItem="X3M-EX-NxT" secondAttribute="bottom" id="g8r-ox-1hn"/>
                        <constraint firstItem="N5V-5A-uEU" firstAttribute="width" secondItem="X3M-EX-NxT" secondAttribute="width" id="gOv-mv-q1h"/>
                        <constraint firstItem="DfB-pK-OcX" firstAttribute="leading" secondItem="N5V-5A-uEU" secondAttribute="trailing" id="grh-F8-2qZ"/>
                        <constraint firstAttribute="height" constant="50" id="not-H1-NbM"/>
                        <constraint firstItem="DfB-pK-OcX" firstAttribute="bottom" secondItem="N5V-5A-uEU" secondAttribute="bottom" id="wGn-Pu-6Gb"/>
                        <constraint firstItem="X3M-EX-NxT" firstAttribute="leading" secondItem="DfB-pK-OcX" secondAttribute="trailing" id="wId-3K-Qnj"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="rak-jr-3bA" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="CQa-iy-XGb"/>
                <constraint firstItem="4hL-y4-unJ" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="Hkt-E9-4oS"/>
                <constraint firstItem="vUN-kp-3ea" firstAttribute="bottom" secondItem="WsB-Wq-67Q" secondAttribute="bottom" id="JwE-ff-t0W"/>
                <constraint firstAttribute="trailing" secondItem="rak-jr-3bA" secondAttribute="trailing" id="TJA-8n-YeH"/>
                <constraint firstItem="WsB-Wq-67Q" firstAttribute="trailing" secondItem="vUN-kp-3ea" secondAttribute="trailing" id="YKf-tK-FHZ"/>
                <constraint firstItem="WsB-Wq-67Q" firstAttribute="leading" secondItem="vUN-kp-3ea" secondAttribute="leading" id="YjI-ad-wNk"/>
                <constraint firstItem="4hL-y4-unJ" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="eHW-RW-XtZ"/>
                <constraint firstItem="WsB-Wq-67Q" firstAttribute="top" secondItem="rak-jr-3bA" secondAttribute="bottom" id="jBo-tC-1ZT"/>
                <constraint firstAttribute="trailing" secondItem="4hL-y4-unJ" secondAttribute="trailing" id="pb0-WB-7OK"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="filterNameTF" destination="uhS-Pb-7t3" id="WF7-8P-SCl"/>
                <outlet property="leftBtn" destination="N5V-5A-uEU" id="Sba-Wb-gdV"/>
                <outlet property="rightBtn" destination="X3M-EX-NxT" id="zT0-s6-Bjg"/>
                <outlet property="rssiSlider" destination="adj-7J-Phv" id="ynb-N0-9ff"/>
                <outlet property="rssiValueLabel" destination="t8r-tp-Ufq" id="Hy5-hz-NNi"/>
                <outlet property="titleLabel" destination="uZC-49-ucm" id="CoL-dT-cK0"/>
            </connections>
            <point key="canvasLocation" x="50.724637681159422" y="92.075892857142847"/>
        </view>
    </objects>
    <resources>
        <image name="rss" width="64" height="64"/>
        <image name="sousuo" width="64" height="64"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
