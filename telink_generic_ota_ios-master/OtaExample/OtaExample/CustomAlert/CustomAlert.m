/********************************************************************************************************
 * @file     CustomAlert.m
 *
 * @brief    A concise description.
 *
 * <AUTHOR> 梁家誌
 * @date     2021/9/16
 *
 * @par     Copyright (c) [2021], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#import "CustomAlert.h"

@interface CustomAlert ()

@end

@implementation CustomAlert

- (void)awakeFromNib {
    [super awakeFromNib];
    self.layer.cornerRadius = 7;
    self.layer.masksToBounds = YES;
}

- (void)setTitle:(NSString *)title {
    _title = title;
    self.titleLabel.text = title;
}

- (void)setLeftBtnTitle:(NSString *)leftBtnTitle {
    _leftBtnTitle = leftBtnTitle;
    [self.leftBtn setTitle:_leftBtnTitle forState:UIControlStateNormal];
}

- (void)setRightBtnTitle:(NSString *)rightBtnTitle {
    _rightBtnTitle = rightBtnTitle;
    [self.rightBtn setTitle:_rightBtnTitle forState:UIControlStateNormal];
}

- (void)setFilter:(Filter *)filter {
    _filter = filter;
    int value = filter.RSSI.intValue;
    _rssiSlider.value = abs(value);
    _rssiValueLabel.text = [NSString stringWithFormat:@"%ddBm",value];
    _filterNameTF.text = filter.showName;
}

- (IBAction)changeRSSI:(UISlider *)sender {
    int value = -sender.value;
    _filter.RSSI = @(value);
    _rssiValueLabel.text = [NSString stringWithFormat:@"%ddBm",value];
}

@end
