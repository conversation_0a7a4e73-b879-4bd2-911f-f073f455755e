/********************************************************************************************************
 * @file     SettingsVC.m
 *
 * @brief    A concise description.
 *
 * <AUTHOR> 梁家誌
 * @date     2022/10/19
 *
 * @par     Copyright (c) [2022], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#import "SettingsVC.h"
#import <StoreKit/StoreKit.h>
#import "UIStoryboard+extension.h"
#import "SwitchCell.h"
#import "StringTipsVC.h"

@interface SettingsVC ()<SKStoreProductViewControllerDelegate>
/// Display setting list
@property (weak, nonatomic) IBOutlet UITableView *tableView;
/// data source of UITableView.
@property (nonatomic,strong) NSMutableArray <NSString *>*source;

@end

@implementation SettingsVC

/// Called after the view has been loaded.
/// For view controllers created in code, this is after -loadView.
/// For view controllers unarchived from a nib, this is after the view is set.
- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    //title
    self.title = @"Settings";
    //init data source
    self.source = [NSMutableArray arrayWithArray:@[@"Bootloader mode", @"Filter", @"How to import bin file?", @"Get More Telink Apps"]];
    //Remove excess underline of tableView.
    self.tableView.tableFooterView = [[UIView alloc] init];
    //registerNib
    [self.tableView registerNib:[UINib nibWithNibName:NSStringFromClass(SwitchCell.class) bundle:nil] forCellReuseIdentifier:NSStringFromClass(SwitchCell.class)];
    //config UIBarButtonItem
    [self configUIBarButtonItem];
}

/// Called when the view is about to made visible, before it is added to the hierarchy.
/// Because the view is not yet in the hierarchy at the time this method is called, it
/// is too early in the appearance transition for many usages. Prefer -viewIsAppearing:
/// instead of this method when possible. Only use this method when its exact timing
/// before the appearance transition starts running is desired, such as to set up an
/// alongside animation with a transition coordinator, or as a counterpart for paired
/// code in a viewWillDisappear/viewDidDisappear callback that does not rely on the
/// view or view controller's trait collection or the view hierarchy.
- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    //show tabBar
    self.tabBarController.tabBar.hidden = NO;
}

/// config UIBarButtonItem
- (void)configUIBarButtonItem {
    //get CFBundleShortVersionString
    NSString *appVersion = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    //init version UIBarButtonItem
    UIBarButtonItem *item = [[UIBarButtonItem alloc] initWithTitle:[NSString stringWithFormat:@"v%@", appVersion] style:UIBarButtonItemStylePlain target:nil action:nil];
    //rightBarButtonItem
    self.navigationItem.rightBarButtonItem = item;
}

/// pushToFilterVC
- (void)pushToFilterVC {
    //init FilterVC
    UIViewController *vc = [UIStoryboard initVC:@"FilterVC"];
    //pushViewController
    [self.navigationController pushViewController:vc animated:YES];
}

/// pushToTipsVC
- (void)pushToTipsVC {
    //init TipsVC
    UIViewController *vc = [UIStoryboard initVC:@"TipsVC"];
    //pushViewController
    [self.navigationController pushViewController:vc animated:YES];
}

/// pushToTelinkApps
- (void)pushToTelinkApps {
    //实现代理SKStoreProductViewControllerDelegate
    SKStoreProductViewController *storeProductViewContorller = [[SKStoreProductViewController alloc] init];
    //delegate
    storeProductViewContorller.delegate = self;
    //加载一个新的视图展示
    [storeProductViewContorller loadProductWithParameters: @{SKStoreProductParameterITunesItemIdentifier : @"1637594591"} completionBlock:^(BOOL result, NSError *error) {
        //回调
        if(error){
            //show error
            if (error.localizedDescription) {
                //error.localizedDescription
                [self showTips:error.localizedDescription];
            } else {
                //error
                [self showTips:[NSString stringWithFormat:@"%@", error]];
            }
        }else{
            //AS应用界面
            if (@available(iOS 10.0, *)) {
            } else {
                [[UINavigationBar appearance] setTintColor:[UIColor blueColor]];
            }
            //presentViewController
            [self presentViewController:storeProductViewContorller animated:YES completion:nil];
        }
    }];
}

/// config Bootloader
- (void)clickSwitchButton:(UISwitch *)switchButton {
    //setValue
    [[NSUserDefaults standardUserDefaults] setValue:[NSNumber numberWithBool:switchButton.on] forKey:kBootloaderEnable];
    //synchronize
    [[NSUserDefaults standardUserDefaults] synchronize];
}

/// pushTipsVC
- (void)pushTipsVC {
    //init StringTipsVC
    StringTipsVC *vc = (StringTipsVC *)[UIStoryboard initVC:NSStringFromClass(StringTipsVC.class)];
    //set title
    vc.title = @"Bootloader mode tips";
    //set tipsMessage
    vc.tipsMessage = @"The Bluetooth device broadcast name in Bootloader mode is \"OTA-Bootloader\". When the APP enables Bootloader mode, upgrading a Bluetooth device in a non Bootloader mode requires sending a command to switch the device to Bootloader mode.";
    //pushViewController
    [self.navigationController pushViewController:vc animated:YES];
}

#pragma mark - UITableViewDataSource,UITableViewDelegate

/// Row display. Implementers should *always* try to reuse cells by setting each cell's reuseIdentifier 
/// and querying for available reusable cells with dequeueReusableCellWithIdentifier:
/// Cell gets various attributes set automatically based on table (separators) 
/// and data source (accessory views, editing controls)
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    //Bootloader
    if (indexPath.row == 0) {
        //init SwitchCell
        SwitchCell *cell = (SwitchCell *)[tableView dequeueReusableCellWithIdentifier:NSStringFromClass(SwitchCell.class) forIndexPath:indexPath];
        //accessoryType
        cell.accessoryType = UITableViewCellAccessoryNone;
        //selectionStyle
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        //showLabel
        cell.showLabel.text = self.source[indexPath.row];
        //valueForKey
        NSNumber *enable = [[NSUserDefaults standardUserDefaults] valueForKey:kBootloaderEnable];
        //set showSwitch
        cell.showSwitch.on = enable.boolValue;
        //addTarget for showSwitch
        [cell.showSwitch addTarget:self action:@selector(clickSwitchButton:) forControlEvents:UIControlEventValueChanged];
        //addTarget for tipsButton
        [cell.tipsButton addTarget:self action:@selector(pushTipsVC) forControlEvents:UIControlEventTouchUpInside];
        return cell;
    }
    //other
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"UITableViewCell"];
    if (!cell) {
        //init UITableViewCell
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"UITableViewCell"];
    }
    //textLabel
    cell.textLabel.text = self.source[indexPath.row];
    //accessoryType
    cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
    return cell;
}

/// didSelectRowAtIndexPath
/// Called after the user changes the selection.
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    UITableViewCell *cell = [tableView cellForRowAtIndexPath:indexPath];
    cell.selected = NO;
    if (indexPath.row == 1) {
        [self pushToFilterVC];
    } else if (indexPath.row == 2) {
        [self pushToTipsVC];
    } else if (indexPath.row == 3) {
        [self pushToTelinkApps];
    }
}

/// numberOfRowsInSection
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return self.source.count;
}

#pragma mark - SKStoreProductViewControllerDelegate

//取消按钮监听
- (void)productViewControllerDidFinish:(SKStoreProductViewController *)viewController {
    if (@available(iOS 10.0, *)) {
    } else {
        [[UINavigationBar appearance] setTintColor:[UIColor whiteColor]];
    }
    [self dismissViewControllerAnimated:YES completion:nil];
}

@end
