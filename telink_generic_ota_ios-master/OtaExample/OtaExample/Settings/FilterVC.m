/********************************************************************************************************
 * @file     FilterVC.m
 *
 * @brief    A concise description.
 *
 * <AUTHOR> 梁家誌
 * @date     2022/8/5
 *
 * @par     Copyright (c) [2022], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#import "FilterVC.h"
#import "BaseModel.h"

@interface FilterVC ()<UITextFieldDelegate>
@property (weak, nonatomic) IBOutlet UILabel *rssiLabel;
@property (weak, nonatomic) IBOutlet UISlider *rsslSlider;
@property (weak, nonatomic) IBOutlet UITextField *nameTF;

@property (nonatomic, strong) Filter *filter;
@property (nonatomic, strong) Filter *oldFilter;

@end

@implementation FilterVC

- (void)normalSetting{
    [super normalSetting];
    self.title = @"Set Filter";
    self.view.backgroundColor = [UIColor groupTableViewBackgroundColor];
    self.navigationItem.leftBarButtonItem = [[UIBarButtonItem alloc] initWithImage:[UIImage imageNamed:@"back"] style:UIBarButtonItemStylePlain target:self action:@selector(clickBack)];

    NSDictionary *dict = [[NSUserDefaults standardUserDefaults] valueForKey:@"MyFilter"];
    self.filter = [[Filter alloc] init];
    self.oldFilter = [[Filter alloc] init];
    if (dict != nil) {
        [self.filter setDictionaryToFilter:dict];
        [self.oldFilter setDictionaryToFilter:dict];
    }
    
    self.rssiLabel.text = [NSString stringWithFormat:@"Filter RSSI:(%@dBm)", self.filter.RSSI];
    self.rsslSlider.value = abs(self.filter.RSSI.intValue);
    self.nameTF.text = self.filter.showName;
    self.nameTF.returnKeyType = UIReturnKeyDone;
    self.nameTF.delegate = self;
}

-(void)clickBack {
    [self.view endEditing:YES];
    if ([self compareOneFilter:self.oldFilter otherFilter:self.filter]) {
        //未修改
        [self.navigationController popViewControllerAnimated:YES];
    } else {
        //已经修改
        __weak typeof(self) weakSelf = self;
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:nil message:@"The configure of filter had changed but not save, are you make sure to save?" preferredStyle:UIAlertControllerStyleAlert];
        [alert addAction:[UIAlertAction actionWithTitle:@"YES" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [weakSelf clickSaveButton:nil];
        }]];
        [alert addAction:[UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
            [weakSelf.navigationController popViewControllerAnimated:YES];
        }]];
        [[UIApplication sharedApplication].keyWindow.rootViewController presentViewController:alert animated:YES completion:nil];
    }
}

- (BOOL)compareOneFilter:(Filter *)oneFilter otherFilter:(Filter *)otherFilter {
    BOOL tem = YES;
    if (![oneFilter.RSSI isEqualToNumber:otherFilter.RSSI]) {
        return NO;
    }
    if (!oneFilter.showName && !otherFilter.showName) {
        return YES;
    }
    if (![oneFilter.showName isEqualToString:otherFilter.showName]) {
        return NO;
    }
    return tem;
}

//点击键盘右下角按钮回收键盘
- (BOOL)textFieldShouldReturn:(UITextField *)textField {
    [textField resignFirstResponder];
    return YES;
}

//点击空白处回收键盘
- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [self.nameTF resignFirstResponder];
}

- (void)textFieldDidEndEditing:(UITextField *)textField {
    self.filter.showName = self.nameTF.text;
}

/// Called when the view is about to made visible, before it is added to the hierarchy.
/// Because the view is not yet in the hierarchy at the time this method is called, it
/// is too early in the appearance transition for many usages. Prefer -viewIsAppearing:
/// instead of this method when possible. Only use this method when its exact timing
/// before the appearance transition starts running is desired, such as to set up an
/// alongside animation with a transition coordinator, or as a counterpart for paired
/// code in a viewWillDisappear/viewDidDisappear callback that does not rely on the
/// view or view controller's trait collection or the view hierarchy.
- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    self.tabBarController.tabBar.hidden = YES;
}

- (IBAction)changeRssiValue:(UISlider *)sender {
    self.filter.RSSI = @(-(int)sender.value);
    self.rssiLabel.text = [NSString stringWithFormat:@"Filter RSSI:(%@dBm)", self.filter.RSSI];
}

- (IBAction)clickSaveButton:(UIButton *)sender {
    NSDictionary *dict = [self.filter getDictionaryOfFilter];
    [[NSUserDefaults standardUserDefaults] setValue:dict forKey:@"MyFilter"];
    [[NSUserDefaults standardUserDefaults] synchronize];
    [self.navigationController popViewControllerAnimated:YES];
}

@end
