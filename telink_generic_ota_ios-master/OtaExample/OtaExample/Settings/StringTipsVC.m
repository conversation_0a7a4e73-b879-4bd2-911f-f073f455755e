/********************************************************************************************************
 * @file     StringTipsVC.m
 *
 * @brief    A concise description.
 *
 * <AUTHOR> 梁家誌
 * @date     2023/11/10
 *
 * @par     Copyright (c) [2023], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#import "StringTipsVC.h"

@interface StringTipsVC ()
@property (weak, nonatomic) IBOutlet UITextView *tipTextView;

@end

@implementation StringTipsVC

/// Called after the view has been loaded.
/// For view controllers created in code, this is after -loadView.
/// For view controllers unarchived from a nib, this is after the view is set.
- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.tipTextView.text = self.tipsMessage;
    self.tipTextView.layoutManager.allowsNonContiguousLayout= NO;//设置为 NO 后 UITextView 就不会再自己重置滑动了。
    self.tipTextView.layoutManager.usesFontLeading = NO;
}

/// Called when the view is about to made visible, before it is added to the hierarchy.
/// Because the view is not yet in the hierarchy at the time this method is called, it
/// is too early in the appearance transition for many usages. Prefer -viewIsAppearing:
/// instead of this method when possible. Only use this method when its exact timing
/// before the appearance transition starts running is desired, such as to set up an
/// alongside animation with a transition coordinator, or as a counterpart for paired
/// code in a viewWillDisappear/viewDidDisappear callback that does not rely on the
/// view or view controller's trait collection or the view hierarchy.
- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    self.tabBarController.tabBar.hidden = YES;
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
