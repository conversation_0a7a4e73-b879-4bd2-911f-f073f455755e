/********************************************************************************************************
 * @file     ShowTips.h
 *
 * @brief    A concise description.
 *
 * <AUTHOR> 梁家誌
 * @date     2020/7/14
 *
 * @par     Copyright (c) [2020], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#define kScreenWidth ([UIScreen mainScreen].bounds.size.width)
#define kScreenHeight ([UIScreen mainScreen].bounds.size.height)

#define kEndTimer(timer) \
if (timer) {    \
[timer invalidate]; \
timer = nil;    \
}

#import "ARShowTipsView.h"

#define ShowHandle ([ARShowTips shareTips])
@interface ARShowTips : NSObject
@property (nonatomic, weak) ARShowTipsView *showTipsView;


+ (instancetype)shareTips;
- (instancetype(^)(ShowType type, NSString *tip))showTip;
- (instancetype(^)(int t))delayHidden;
- (instancetype(^)(void))hidden;
- (instancetype(^)(void))timeOut;
- (instancetype(^)(NSString *content))alertShowTips;
- (instancetype(^)(void))alertDissmiss;
- (void)alertShowTips:(NSString *)content doBlock:(void (^)(void))doBlock cancelBlock:(void (^)(void))cancelBlock;
@end
