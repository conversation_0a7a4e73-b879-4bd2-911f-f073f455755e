/********************************************************************************************************
 * @file     ChooseFileVC.m
 *
 * @brief    A concise description.
 *
 * <AUTHOR> 梁家誌
 * @date     2021/11/15
 *
 * @par     Copyright (c) [2021], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#import "ChooseFileVC.h"
#import "ARShowTips.h"
#import "UIColor+Telink.h"
#import "UIDevice+Extension.h"
#import "UIStoryboard+extension.h"

@interface ChooseFileVC ()<UITableViewDataSource, UITableViewDelegate>
@property (strong, nonatomic) UILabel *selectFile;
@property (strong, nonatomic) UITableView *tableView;
@property (strong, nonatomic) NSMutableArray <NSString *>*source;
@end

@implementation ChooseFileVC

/// Called after the view has been loaded.
/// For view controllers created in code, this is after -loadView.
/// For view controllers unarchived from a nib, this is after the view is set.
- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.title = @"Choose Bin File";
    UIBarButtonItem *item = [[UIBarButtonItem alloc] initWithImage:[UIImage imageNamed:@"tishi"] style:UIBarButtonItemStylePlain target:self action:@selector(clickPushToTipsVC)];
    self.navigationItem.rightBarButtonItem = item;
    
    self.view.backgroundColor = UIColor.telinkTabBarBackgroundColor;
    _selectFile = [[UILabel alloc] initWithFrame:CGRectMake(15, 5, SCREEN_WIDTH-30, 43)];
    _selectFile.font = [UIFont systemFontOfSize:12];
    _selectFile.numberOfLines = 0;
    
    _tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 5 + 43 + 5, SCREEN_WIDTH, SCREEN_HEIGHT - self.getRectNavAndStatusHightAndSafeDistanceBottom - (5 + 43 + 5))];
    _tableView.delegate = self;
    _tableView.dataSource = self;
    _tableView.rowHeight = UITableViewAutomaticDimension;
    _tableView.backgroundColor = UIColor.telinkTabBarBackgroundColor;
    _tableView.sectionHeaderHeight = 15.0;
    _tableView.sectionFooterHeight = 15.0;
    _tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectZero];
    [self.view addSubview:_selectFile];
    [self.view addSubview:_tableView];

    _source = [NSMutableArray arrayWithArray:FileDataSource.share.getAllBinFilePath];
    
    if (self.filePath && self.filePath.length > 0) {
        self.selectFile.text = [NSString stringWithFormat:@"Last Chose: %@",[self.filePath lastPathComponent]];
    } else {
        self.selectFile.text = @"Last Chose: NULL";
    }
}

- (void)clickPushToTipsVC {
    UIViewController *vc = [UIStoryboard initVC:@"TipsVC"];
    [self.navigationController pushViewController:vc animated:YES];
}

/// Row display. Implementers should *always* try to reuse cells by setting each cell's reuseIdentifier 
/// and querying for available reusable cells with dequeueReusableCellWithIdentifier:
/// Cell gets various attributes set automatically based on table (separators) 
/// and data source (accessory views, editing controls)
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSString * identifier= @"FileCell";
    UITableViewCell * cell = [tableView dequeueReusableCellWithIdentifier:identifier];
    if (cell == nil) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:identifier];
    }
    cell.textLabel.numberOfLines = 0;
    NSString *p = _source[indexPath.row];
    NSData *data = [FileDataSource.share getDataWithLastPathComponent:p];
    NSString *readString = @", read bin fail!";//bin文件读取失败。
    if (data && data.length) {//bin文件读取成功。
        readString = [NSString stringWithFormat:@", pid:0x%04X vid:0x%04X",[FileDataSource.share getPidWithOTAData:data],[FileDataSource.share getVidWithOTAData:data]];
    }
    if ([p containsString:@"Documents"]) {
        cell.textLabel.text = [NSString stringWithFormat:@"%@ (iTunes)%@",[p lastPathComponent],readString];
    } else {
        cell.textLabel.text = [NSString stringWithFormat:@"%@ (local)%@",[p lastPathComponent],readString];
    }
    
    cell.textLabel.font = [UIFont systemFontOfSize:11];
    return cell;
}

/// numberOfRowsInSection
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return _source.count;
}

/// didSelectRowAtIndexPath
/// Called after the user changes the selection.
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView cellForRowAtIndexPath:indexPath];
    NSString *tips = [NSString stringWithFormat:@"it will select this file %@ for OTA", [_source[indexPath.row] lastPathComponent]];
    __weak typeof(self) weakSelf = self;
    [ShowHandle alertShowTips:tips doBlock:^{
        weakSelf.selectFile.text = [NSString stringWithFormat:@"Last Chose: %@",[weakSelf.source[indexPath.row] lastPathComponent]];
        [weakSelf.view setNeedsDisplay];
        ShowHandle.alertDissmiss();
        if (weakSelf.backFilePath) {
            weakSelf.backFilePath([weakSelf.source[indexPath.row] lastPathComponent]);
        }
        [weakSelf.navigationController popViewControllerAnimated:YES];
    } cancelBlock:^{
    }];
    cell.selected = NO;
}

/// editActionsForRowAtIndexPath
/// This method supersedes -tableView:titleForDeleteConfirmationButtonForRowAtIndexPath: if return value is non-nil
- (nullable NSArray<UITableViewRowAction *> *)tableView:(UITableView *)tableView editActionsForRowAtIndexPath:(NSIndexPath *)indexPath {
    __weak typeof(self) weakSelf = self;
    UITableViewRowAction *actionDelete = [UITableViewRowAction rowActionWithStyle:UITableViewRowActionStyleNormal title:@"Delete" handler:^(UITableViewRowAction * _Nonnull action, NSIndexPath * _Nonnull indexPath) {
        NSFileManager *manager = [NSFileManager defaultManager];
        if ([manager fileExistsAtPath:weakSelf.source[indexPath.row]]) {
            BOOL ret = [manager removeItemAtPath:weakSelf.source[indexPath.row] error:nil];
            if (ret) {
                [ARShowTips shareTips].showTip(ShowTypeNormal, @"delete success");
                if ([weakSelf.filePath isEqualToString:weakSelf.source[indexPath.row]]) {
                    weakSelf.selectFile.text = @"Last Chose: NULL";
                    if (weakSelf.backFilePath) {
                        weakSelf.backFilePath(nil);
                    }
                }
            }else{
                [ARShowTips shareTips].showTip(ShowTypeNormal, @"delete fail");
            }
            [ARShowTips shareTips].delayHidden(1.5);
        }
        weakSelf.source = [NSMutableArray arrayWithArray:FileDataSource.share.getAllBinFilePath];
        [weakSelf.tableView reloadData];
    }];
    actionDelete.backgroundColor = [UIColor redColor];
    return @[actionDelete];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 10;
}

-(void)dealloc{
    TelinkDebugLog(@"%s",__func__);
}

@end
