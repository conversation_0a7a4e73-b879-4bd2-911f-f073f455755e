<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="12118" systemVersion="16B2657" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="12086"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" userLabel="ARShow Tips View" customClass="ARShowTipsView">
            <rect key="frame" x="0.0" y="0.0" width="100" height="100"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <activityIndicatorView opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" style="whiteLarge" translatesAutoresizingMaskIntoConstraints="NO" id="Ty6-ZL-XDL">
                    <rect key="frame" x="32" y="32" width="37" height="37"/>
                    <color key="color" red="0.2588235438" green="0.75686275960000005" blue="0.96862745289999996" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                </activityIndicatorView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xZ8-o8-KE0">
                    <rect key="frame" x="37.5" y="75" width="26.5" height="12"/>
                    <fontDescription key="fontDescription" type="system" pointSize="10"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="xZ8-o8-KE0" secondAttribute="trailing" constant="10" id="K8a-mB-mRx"/>
                <constraint firstItem="Ty6-ZL-XDL" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" id="L8J-6p-TJm"/>
                <constraint firstItem="Ty6-ZL-XDL" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="bOF-CY-sAC"/>
                <constraint firstItem="xZ8-o8-KE0" firstAttribute="centerX" secondItem="Ty6-ZL-XDL" secondAttribute="centerX" id="dMr-i2-kXS"/>
                <constraint firstItem="xZ8-o8-KE0" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="iN0-l3-epB" secondAttribute="leading" constant="10" id="mmr-69-rbK"/>
                <constraint firstItem="xZ8-o8-KE0" firstAttribute="top" secondItem="Ty6-ZL-XDL" secondAttribute="bottom" constant="6" id="zvJ-04-pI9"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="activity" destination="Ty6-ZL-XDL" id="gpi-gZ-lVN"/>
                <outlet property="tipLab" destination="xZ8-o8-KE0" id="mnH-lo-GIZ"/>
            </connections>
            <point key="canvasLocation" x="127" y="181"/>
        </view>
    </objects>
</document>
