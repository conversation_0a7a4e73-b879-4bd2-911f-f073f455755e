<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="KGk-i7-Jjw" customClass="SwitchCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Bootloader" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="MIC-ZM-fgO">
                        <rect key="frame" x="16" y="12" width="83.666666666666671" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="f9C-Tn-DCO"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" on="YES" translatesAutoresizingMaskIntoConstraints="NO" id="EfJ-JA-KXV">
                        <rect key="frame" x="261" y="6.6666666666666679" width="51" height="31.000000000000004"/>
                    </switch>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="sXv-te-JbO">
                        <rect key="frame" x="104.66666666666667" y="7" width="30.000000000000014" height="30"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="30" id="9ud-Qu-CVC"/>
                            <constraint firstAttribute="height" constant="30" id="Wq7-qE-fDK"/>
                        </constraints>
                        <state key="normal" title="Any" image="tishi"/>
                    </button>
                </subviews>
                <constraints>
                    <constraint firstItem="sXv-te-JbO" firstAttribute="leading" secondItem="MIC-ZM-fgO" secondAttribute="trailing" constant="5" id="1IM-4S-rkE"/>
                    <constraint firstItem="MIC-ZM-fgO" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="DqG-at-7Or"/>
                    <constraint firstItem="MIC-ZM-fgO" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="16" id="PyT-dm-Xcl"/>
                    <constraint firstAttribute="trailing" secondItem="EfJ-JA-KXV" secondAttribute="trailing" constant="10" id="fjK-yP-Twl"/>
                    <constraint firstItem="sXv-te-JbO" firstAttribute="centerY" secondItem="MIC-ZM-fgO" secondAttribute="centerY" id="gux-lR-JIg"/>
                    <constraint firstItem="EfJ-JA-KXV" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="sXv-te-JbO" secondAttribute="trailing" constant="5" id="k1O-PZ-v9h"/>
                    <constraint firstItem="EfJ-JA-KXV" firstAttribute="centerY" secondItem="MIC-ZM-fgO" secondAttribute="centerY" id="use-ek-YgM"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="showLabel" destination="MIC-ZM-fgO" id="mfV-g5-qms"/>
                <outlet property="showSwitch" destination="EfJ-JA-KXV" id="a7x-Zq-1nX"/>
                <outlet property="tipsButton" destination="sXv-te-JbO" id="dru-kO-aRu"/>
            </connections>
            <point key="canvasLocation" x="136" y="20"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="tishi" width="35" height="35"/>
    </resources>
</document>
