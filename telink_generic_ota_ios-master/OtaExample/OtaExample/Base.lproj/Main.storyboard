<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="22154" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="6po-Kk-Pdz">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22130"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--设备列表-->
        <scene sceneID="ovb-G4-gek">
            <objects>
                <viewController storyboardIdentifier="ScanViewController" automaticallyAdjustsScrollViewInsets="NO" id="hFo-fY-9i7" customClass="ScanViewController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="9av-7m-JeA"/>
                        <viewControllerLayoutGuide type="bottom" id="PzL-5t-rvi"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="eUz-E2-Kaj">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" rowHeight="49" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="fAQ-qN-aVr">
                                <rect key="frame" x="0.0" y="132" width="414" height="681"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <prototypes>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="default" accessoryType="disclosureIndicator" indentationWidth="10" reuseIdentifier="DeviceCell" rowHeight="56" id="YYR-nr-IOb" customClass="DeviceCell">
                                        <rect key="frame" x="0.0" y="50" width="414" height="56"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="YYR-nr-IOb" id="BIy-V9-0NQ">
                                            <rect key="frame" x="0.0" y="0.0" width="383.5" height="56"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bluetooth" translatesAutoresizingMaskIntoConstraints="NO" id="n9u-9h-qBK">
                                                    <rect key="frame" x="8" y="8" width="40" height="40"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" secondItem="n9u-9h-qBK" secondAttribute="height" id="tSZ-Hs-Sij"/>
                                                    </constraints>
                                                </imageView>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0.0" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1Ul-8W-Pr3">
                                                    <rect key="frame" x="56" y="21" width="18" height="14.5"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <nil key="textColor"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="---" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rZh-vM-nkJ">
                                                    <rect key="frame" x="360" y="19.5" width="19.5" height="17"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <nil key="textColor"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="rZh-vM-nkJ" firstAttribute="centerY" secondItem="1Ul-8W-Pr3" secondAttribute="centerY" id="1Va-uH-ql7"/>
                                                <constraint firstItem="n9u-9h-qBK" firstAttribute="leading" secondItem="BIy-V9-0NQ" secondAttribute="leading" constant="8" id="GU3-ht-bzV"/>
                                                <constraint firstItem="n9u-9h-qBK" firstAttribute="top" secondItem="BIy-V9-0NQ" secondAttribute="top" constant="8" id="IUT-VE-yBu"/>
                                                <constraint firstItem="rZh-vM-nkJ" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="1Ul-8W-Pr3" secondAttribute="trailing" constant="5" id="Vss-M1-LNR"/>
                                                <constraint firstItem="1Ul-8W-Pr3" firstAttribute="centerY" secondItem="n9u-9h-qBK" secondAttribute="centerY" id="etv-7d-jFH"/>
                                                <constraint firstAttribute="bottom" secondItem="n9u-9h-qBK" secondAttribute="bottom" constant="8" id="jk6-sy-Bwy"/>
                                                <constraint firstItem="1Ul-8W-Pr3" firstAttribute="leading" secondItem="n9u-9h-qBK" secondAttribute="trailing" constant="8" id="sVB-7M-mfj"/>
                                                <constraint firstAttribute="trailing" secondItem="rZh-vM-nkJ" secondAttribute="trailing" constant="4" id="x9n-cl-s4z"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <connections>
                                            <outlet property="deviceName" destination="1Ul-8W-Pr3" id="hfh-or-KlB"/>
                                            <outlet property="icon" destination="n9u-9h-qBK" id="Tfj-a7-4a0"/>
                                            <outlet property="rssi" destination="rZh-vM-nkJ" id="vmZ-cc-JY4"/>
                                        </connections>
                                    </tableViewCell>
                                </prototypes>
                                <connections>
                                    <outlet property="dataSource" destination="hFo-fY-9i7" id="YHC-UN-rxm"/>
                                    <outlet property="delegate" destination="hFo-fY-9i7" id="AbW-cG-0Bk"/>
                                </connections>
                            </tableView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5ae-FT-ner">
                                <rect key="frame" x="10" y="92" width="394" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="MUM-wS-apQ"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="fAQ-qN-aVr" firstAttribute="leading" secondItem="eUz-E2-Kaj" secondAttribute="leading" id="8ZZ-ou-LbN"/>
                            <constraint firstItem="5ae-FT-ner" firstAttribute="leading" secondItem="eUz-E2-Kaj" secondAttribute="leading" constant="10" id="V8k-0r-lzu"/>
                            <constraint firstAttribute="trailing" secondItem="fAQ-qN-aVr" secondAttribute="trailing" id="Vbb-Nm-ckm"/>
                            <constraint firstItem="PzL-5t-rvi" firstAttribute="top" secondItem="fAQ-qN-aVr" secondAttribute="bottom" id="kns-TK-6vg"/>
                            <constraint firstItem="5ae-FT-ner" firstAttribute="top" secondItem="9av-7m-JeA" secondAttribute="bottom" id="pD3-ru-d18"/>
                            <constraint firstAttribute="trailing" secondItem="5ae-FT-ner" secondAttribute="trailing" constant="10" id="tcA-N7-U4o"/>
                            <constraint firstItem="fAQ-qN-aVr" firstAttribute="top" secondItem="5ae-FT-ner" secondAttribute="bottom" id="ugW-f2-zee"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" title="设备列表" id="3DE-S0-lW5"/>
                    <connections>
                        <outlet property="filterLabel" destination="5ae-FT-ner" id="qXb-Ay-uGU"/>
                        <outlet property="tableView" destination="fAQ-qN-aVr" id="TXM-YZ-ycQ"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="CVb-TU-e4d" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1056.8" y="-242.42878560719643"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="fKl-FW-QGI">
            <objects>
                <navigationController automaticallyAdjustsScrollViewInsets="NO" id="GeR-eg-3gj" sceneMemberID="viewController">
                    <tabBarItem key="tabBarItem" systemItem="search" id="GUB-Iz-6aZ"/>
                    <toolbarItems/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" id="60n-gv-MsW">
                        <rect key="frame" x="0.0" y="48" width="414" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <nil name="viewControllers"/>
                    <connections>
                        <segue destination="hFo-fY-9i7" kind="relationship" relationship="rootViewController" id="suZ-6y-E1v"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="9DS-qc-ayU" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="413.60000000000002" y="-242.42878560719643"/>
        </scene>
        <!--SettingsVC-->
        <scene sceneID="598-4x-GZB">
            <objects>
                <viewController storyboardIdentifier="SettingsVC" id="vkN-mf-GSO" customClass="SettingsVC" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="pAj-nC-0EE"/>
                        <viewControllerLayoutGuide type="bottom" id="ela-2y-NWa"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="iga-zK-uVG">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="44" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="Iyt-HC-eH2">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="813"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <connections>
                                    <outlet property="dataSource" destination="vkN-mf-GSO" id="8v0-ah-dPS"/>
                                    <outlet property="delegate" destination="vkN-mf-GSO" id="3Pv-5t-6Bz"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                        <constraints>
                            <constraint firstItem="Iyt-HC-eH2" firstAttribute="top" secondItem="iga-zK-uVG" secondAttribute="top" id="2HT-Fl-ZBo"/>
                            <constraint firstItem="Iyt-HC-eH2" firstAttribute="leading" secondItem="iga-zK-uVG" secondAttribute="leading" id="8nD-Ub-yGJ"/>
                            <constraint firstAttribute="trailing" secondItem="Iyt-HC-eH2" secondAttribute="trailing" id="xxC-K8-R3M"/>
                            <constraint firstItem="ela-2y-NWa" firstAttribute="top" secondItem="Iyt-HC-eH2" secondAttribute="bottom" id="zBr-BD-XMV"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="TpM-ls-UMx"/>
                    <connections>
                        <outlet property="tableView" destination="Iyt-HC-eH2" id="YfF-QK-9fe"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="cVc-MX-g8u" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1057" y="504"/>
        </scene>
        <!--Tab Bar Controller-->
        <scene sceneID="2n8-Pp-LeL">
            <objects>
                <tabBarController id="6po-Kk-Pdz" sceneMemberID="viewController">
                    <tabBar key="tabBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="vvT-Qf-VGM">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="49"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </tabBar>
                    <connections>
                        <segue destination="GeR-eg-3gj" kind="relationship" relationship="viewControllers" id="rGR-Dk-PiG"/>
                        <segue destination="ZBk-x3-9UJ" kind="relationship" relationship="viewControllers" id="sFs-nU-T8O"/>
                    </connections>
                </tabBarController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="4Rp-hn-nI2" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-542" y="123"/>
        </scene>
        <!--Settings-->
        <scene sceneID="MF8-4H-Iad">
            <objects>
                <navigationController automaticallyAdjustsScrollViewInsets="NO" id="ZBk-x3-9UJ" sceneMemberID="viewController">
                    <tabBarItem key="tabBarItem" title="Settings" image="tabbar_settings" id="xO5-nq-fTy"/>
                    <toolbarItems/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" id="5lB-Mv-cb8">
                        <rect key="frame" x="0.0" y="48" width="414" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <nil name="viewControllers"/>
                    <connections>
                        <segue destination="vkN-mf-GSO" kind="relationship" relationship="rootViewController" id="BSg-Xf-o0c"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="c9H-7G-imG" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="413" y="504"/>
        </scene>
        <!--FilterVC-->
        <scene sceneID="Dc2-dG-Ar8">
            <objects>
                <viewController storyboardIdentifier="FilterVC" id="shB-tD-JmZ" customClass="FilterVC" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="07f-ar-xqo">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Filter RSSI:(-100dBm)" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ci9-4C-N6J">
                                <rect key="frame" x="20" y="112" width="374" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="iW2-1x-lue"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <slider opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" value="100" minValue="40" maxValue="100" translatesAutoresizingMaskIntoConstraints="NO" id="nMA-vb-YlS">
                                <rect key="frame" x="18" y="137" width="378" height="31"/>
                                <connections>
                                    <action selector="changeRssiValue:" destination="shB-tD-JmZ" eventType="valueChanged" id="Dgw-o1-Egc"/>
                                </connections>
                            </slider>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Filter Name:" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pOP-1W-OHB">
                                <rect key="frame" x="20" y="187" width="374" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="psk-FD-xYD"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="HwI-PM-tDE">
                                <rect key="frame" x="20" y="227" width="374" height="34"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="34" id="aWX-dI-WqL"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits"/>
                            </textField>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qmN-Qv-yp9">
                                <rect key="frame" x="0.0" y="812" width="414" height="50"/>
                                <color key="backgroundColor" red="0.2901960784" green="0.52941176469999995" blue="0.93333333330000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="cGP-bi-kqb"/>
                                </constraints>
                                <state key="normal" title="SAVE">
                                    <color key="titleColor" red="0.94117647059999998" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                                </state>
                                <connections>
                                    <action selector="clickSaveButton:" destination="shB-tD-JmZ" eventType="touchUpInside" id="5g7-r7-rzA"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="KCa-7p-DIG"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="HwI-PM-tDE" firstAttribute="top" secondItem="pOP-1W-OHB" secondAttribute="bottom" constant="20" id="724-Db-Wan"/>
                            <constraint firstItem="KCa-7p-DIG" firstAttribute="trailing" secondItem="pOP-1W-OHB" secondAttribute="trailing" constant="20" id="F8F-58-bEL"/>
                            <constraint firstItem="qmN-Qv-yp9" firstAttribute="leading" secondItem="KCa-7p-DIG" secondAttribute="leading" id="Nax-ah-lrh"/>
                            <constraint firstItem="ci9-4C-N6J" firstAttribute="leading" secondItem="KCa-7p-DIG" secondAttribute="leading" constant="20" id="P7n-0d-0UD"/>
                            <constraint firstItem="pOP-1W-OHB" firstAttribute="leading" secondItem="KCa-7p-DIG" secondAttribute="leading" constant="20" id="RV0-MR-152"/>
                            <constraint firstItem="KCa-7p-DIG" firstAttribute="bottom" secondItem="qmN-Qv-yp9" secondAttribute="bottom" id="TDN-SZ-Kpu"/>
                            <constraint firstItem="KCa-7p-DIG" firstAttribute="trailing" secondItem="ci9-4C-N6J" secondAttribute="trailing" constant="20" id="VEB-23-eyq"/>
                            <constraint firstItem="KCa-7p-DIG" firstAttribute="trailing" secondItem="nMA-vb-YlS" secondAttribute="trailing" constant="20" id="X9q-NZ-fqy"/>
                            <constraint firstItem="nMA-vb-YlS" firstAttribute="top" secondItem="ci9-4C-N6J" secondAttribute="bottom" constant="5" id="hwb-SU-MN0"/>
                            <constraint firstItem="ci9-4C-N6J" firstAttribute="top" secondItem="KCa-7p-DIG" secondAttribute="top" constant="20" id="jXC-BO-1oS"/>
                            <constraint firstItem="pOP-1W-OHB" firstAttribute="top" secondItem="nMA-vb-YlS" secondAttribute="bottom" constant="20" id="k3l-OF-SAm"/>
                            <constraint firstItem="KCa-7p-DIG" firstAttribute="trailing" secondItem="qmN-Qv-yp9" secondAttribute="trailing" id="pe1-hZ-b8b"/>
                            <constraint firstItem="HwI-PM-tDE" firstAttribute="leading" secondItem="KCa-7p-DIG" secondAttribute="leading" constant="20" id="twp-5y-aE7"/>
                            <constraint firstItem="KCa-7p-DIG" firstAttribute="trailing" secondItem="HwI-PM-tDE" secondAttribute="trailing" constant="20" id="uo8-R2-KQa"/>
                            <constraint firstItem="nMA-vb-YlS" firstAttribute="leading" secondItem="KCa-7p-DIG" secondAttribute="leading" constant="20" id="wKC-L1-V0N"/>
                        </constraints>
                    </view>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" prompted="NO"/>
                    <connections>
                        <outlet property="nameTF" destination="HwI-PM-tDE" id="Qpu-1j-Sou"/>
                        <outlet property="rssiLabel" destination="ci9-4C-N6J" id="Hhg-eM-GE6"/>
                        <outlet property="rsslSlider" destination="nMA-vb-YlS" id="tQs-GM-QZ3"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="fBh-uE-nzd" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1733" y="-243"/>
        </scene>
        <!--TipsVC-->
        <scene sceneID="BpD-CQ-vL2">
            <objects>
                <viewController storyboardIdentifier="TipsVC" id="4g1-Wz-TfB" customClass="TipsVC" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="IoN-H6-Qvb"/>
                        <viewControllerLayoutGuide type="bottom" id="ysm-nC-9Zo"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="6vg-wL-9an">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TpJ-di-H82">
                                <rect key="frame" x="20" y="112" width="374" height="304.5"/>
                                <string key="text">The steps to import the . bin file into the APP are as follows:
1.Send . bin files to mobile phones through WeChat, QQ and other chat tools.
2.Click the . bin file to enter the following screenshot interface. Follow the screenshot steps ①②③④ to store . bin in the "BootloaderOTA" folder of "Files".

将.bin文件导入APP的步骤如下：
1.通过微信、QQ等聊天工具将.bin文件发送到手机上。
2.点击.bin文件进入以下截图界面，按照截图的步骤①②③④将.bin存储到“文件”的“BootloaderOTA”文件夹里面即可。</string>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="TelinkOTA_tips" translatesAutoresizingMaskIntoConstraints="NO" id="p6R-tN-hVq">
                                <rect key="frame" x="0.0" y="436.5" width="414" height="245.5"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="p6R-tN-hVq" secondAttribute="height" multiplier="2250:1334" id="wph-X0-BnQ"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="p6R-tN-hVq" secondAttribute="trailing" id="0rt-5Z-Rdu"/>
                            <constraint firstItem="TpJ-di-H82" firstAttribute="top" secondItem="IoN-H6-Qvb" secondAttribute="bottom" constant="20" id="588-jJ-QgJ"/>
                            <constraint firstItem="TpJ-di-H82" firstAttribute="leading" secondItem="6vg-wL-9an" secondAttribute="leading" constant="20" id="Pzy-Ae-nGq"/>
                            <constraint firstAttribute="trailing" secondItem="TpJ-di-H82" secondAttribute="trailing" constant="20" id="SaQ-Kz-H8D"/>
                            <constraint firstItem="p6R-tN-hVq" firstAttribute="leading" secondItem="6vg-wL-9an" secondAttribute="leading" id="XS5-0A-WMS"/>
                            <constraint firstItem="p6R-tN-hVq" firstAttribute="top" secondItem="TpJ-di-H82" secondAttribute="bottom" constant="20" id="yvD-rA-KQC"/>
                        </constraints>
                    </view>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" prompted="NO"/>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="XBc-y8-ugi" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2424.6376811594205" y="-243.08035714285714"/>
        </scene>
        <!--String TipsVC-->
        <scene sceneID="tA3-kv-lPn">
            <objects>
                <viewController storyboardIdentifier="StringTipsVC" id="BnQ-6e-wEW" customClass="StringTipsVC" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="8Kn-NS-kky"/>
                        <viewControllerLayoutGuide type="bottom" id="5gs-wb-FYN"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="T2s-69-4Cd">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" editable="NO" textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="u2E-IV-Hbu">
                                <rect key="frame" x="0.0" y="48" width="414" height="814"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <string key="text">Lorem ipsum dolor sit er elit lamet, consectetaur cillium adipisicing pecu, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Nam liber te conscient to factor tum poen legum odioque civiuda.</string>
                                <color key="textColor" systemColor="labelColor"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                            </textView>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="5gs-wb-FYN" firstAttribute="top" secondItem="u2E-IV-Hbu" secondAttribute="bottom" id="8aM-Tx-Xqn"/>
                            <constraint firstItem="u2E-IV-Hbu" firstAttribute="top" secondItem="8Kn-NS-kky" secondAttribute="bottom" id="Ecj-jV-81M"/>
                            <constraint firstItem="u2E-IV-Hbu" firstAttribute="leading" secondItem="T2s-69-4Cd" secondAttribute="leading" id="iCX-dg-alK"/>
                            <constraint firstAttribute="trailing" secondItem="u2E-IV-Hbu" secondAttribute="trailing" id="ouM-tc-xMZ"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="tipTextView" destination="u2E-IV-Hbu" id="qJV-O2-6FT"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="PVD-KB-O7W" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="3131.884057971015" y="-243.08035714285714"/>
        </scene>
    </scenes>
    <resources>
        <image name="TelinkOTA_tips" width="1125" height="667"/>
        <image name="bluetooth" width="64" height="64"/>
        <image name="tabbar_settings" width="25" height="25"/>
        <systemColor name="labelColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
