<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="21225" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="xhX-gX-UcM">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21207"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="Z4A-eZ-Ufq">
            <objects>
                <viewController id="xhX-gX-UcM" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="WBK-Sw-yMo"/>
                        <viewControllerLayoutGuide type="bottom" id="YWw-gF-0uZ"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="265-EJ-nPT">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="primary_lg" translatesAutoresizingMaskIntoConstraints="NO" id="tOO-sx-Jbk">
                                <rect key="frame" x="60" y="390" width="294" height="116"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="tOO-sx-Jbk" secondAttribute="height" multiplier="1193:470" id="Zpi-gR-hfc"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="tOO-sx-Jbk" firstAttribute="centerY" secondItem="265-EJ-nPT" secondAttribute="centerY" id="Pr0-Ao-dEt"/>
                            <constraint firstItem="tOO-sx-Jbk" firstAttribute="leading" secondItem="265-EJ-nPT" secondAttribute="leading" constant="60" id="QsO-7G-Mov"/>
                            <constraint firstAttribute="trailing" secondItem="tOO-sx-Jbk" secondAttribute="trailing" constant="60" id="rzA-eO-dCI"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="NFz-5a-M5T" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="52.173913043478265" y="375"/>
        </scene>
    </scenes>
    <resources>
        <image name="primary_lg" width="596.5" height="235"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
