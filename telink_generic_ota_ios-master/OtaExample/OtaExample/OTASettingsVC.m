/********************************************************************************************************
 * @file     OTASettingsVC.m
 *
 * @brief    A concise description.
 *
 * <AUTHOR> 梁家誌
 * @date     2021/11/15
 *
 * @par     Copyright (c) [2021], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#import "OTASettingsVC.h"
#import "ARShowTips.h"
#import "ChooseFileVC.h"
#import "UIColor+Telink.h"

@interface OTASettingsVC ()
@property (strong, nonatomic) UIScrollView *scrollView;
@property (strong, nonatomic) UIButton *serviceButton;
@property (strong, nonatomic) UIButton *characteristicButton;
@property (strong, nonatomic) UILabel *readIntervalLabel;
@property (strong, nonatomic) UISlider *readIntervalSlider;
@property (strong, nonatomic) UILabel *writeIntervalLabel;
@property (strong, nonatomic) UISlider *writeIntervalSlider;
@property (strong, nonatomic) UIButton *filePathButton;
@property (strong, nonatomic) UIButton *needSetFirmwareIndexButton;
@property (strong, nonatomic) UILabel *firmwareIndexLabel;
@property (strong, nonatomic) UITextField *firmwareIndexTF;
@property (strong, nonatomic) UIButton *protocolLegacyButton;
@property (strong, nonatomic) UIButton *protocolExtendButton;
@property (strong, nonatomic) UIButton *versionCompareButton;
@property (strong, nonatomic) UILabel *binVersionLabel;
@property (strong, nonatomic) UITextField *binVersionTF;
@property (strong, nonatomic) UILabel *pduLengthLabel;
@property (strong, nonatomic) UITextField *pduLengthTF;
@property (strong, nonatomic) UIButton *securityBootEnableButton;
@property (strong, nonatomic) UIButton *securityBootFilePathButton;
@property (strong, nonatomic) UIButton *saveButton;
@property (strong, nonatomic) OTASettingsModel *oldOTASettings;

@end

@implementation OTASettingsVC

/// Called after the view has been loaded.
/// For view controllers created in code, this is after -loadView.
/// For view controllers unarchived from a nib, this is after the view is set.
- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.title = @"OTA Settings";
    self.view.backgroundColor = UIColor.telinkTabBarBackgroundColor;
    self.oldOTASettings = [[OTASettingsModel alloc] initWithOTASettingsModel:self.otaSettings];
    UIBarButtonItem *item = [[UIBarButtonItem alloc] initWithTitle:@"Reset" style:UIBarButtonItemStylePlain target:self action:@selector(clickReset)];
    self.navigationItem.rightBarButtonItem = item;
    
   self.navigationItem.leftBarButtonItem = [[UIBarButtonItem alloc] initWithImage:[UIImage imageNamed:@"back"] style:UIBarButtonItemStylePlain target:self action:@selector(clickBack)];

    self.scrollView = [[UIScrollView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT - self.getRectNavAndStatusHightAndSafeDistanceBottom - 45)];
    // 控制器设置
    self.automaticallyAdjustsScrollViewInsets = NO;

    [self.view addSubview:self.scrollView];
    
    //5+20+5+40+5+20+5+40+5+20+5+slider+5+20+5+40+5+20+5+25+5
    [self addServiceUI];
    [self addCharacteristicUI];
    [self addReadIntervalUI];
    [self addWriteIntervalUI];
    [self addFilePathUI];
    [self addSecurityBootEnableUI];
    [self addSecurityBootFilePathUI];
    [self addNeedSetFirmwareUI];
    [self addFirmwareIndexUI];
    [self addProtocolUI];
    [self addVersionCompareUI];
    [self addBinVersionUI];
    [self addPduLengthUI];

    self.saveButton = [UIButton buttonWithType:UIButtonTypeSystem];
    self.saveButton.frame = CGRectMake(0, SCREEN_HEIGHT - self.getRectNavAndStatusHightAndSafeDistanceBottom - 45, SCREEN_WIDTH, 45);
    [self.saveButton setTitle:@"SAVE" forState:UIControlStateNormal];
    [self.saveButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [self.saveButton setBackgroundColor:UIColor.telinkButtonBlue];
    self.saveButton.titleLabel.font = [UIFont boldSystemFontOfSize: 16.0];
    [self.saveButton addTarget:self action:@selector(clickSave) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.saveButton];
    
    [self refreshUIWithOtaSettingsModel];
}

- (void)addServiceUI {
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(15, 2, SCREEN_WIDTH-30, 15)];
    titleLabel.font = [UIFont systemFontOfSize:12];
    titleLabel.text = @"Service:";
    [self.scrollView addSubview:titleLabel];

    UIImageView *imageView = [[UIImageView alloc] initWithFrame:CGRectMake(SCREEN_WIDTH-30-20, titleLabel.frame.origin.y + titleLabel.frame.size.height + 2 + 5, 20, 20)];
    imageView.image = [UIImage imageNamed:@"up"];
    [self.scrollView addSubview:imageView];

    _serviceButton = [UIButton buttonWithType:UIButtonTypeSystem];
    _serviceButton.frame = CGRectMake(15, titleLabel.frame.origin.y + titleLabel.frame.size.height + 2, SCREEN_WIDTH-30, 30);
    [_serviceButton setTitleColor:UIColor.telinkTitleBlue forState:UIControlStateNormal];
    [_serviceButton setBackgroundColor:[UIColor colorWithWhite:0.9 alpha:0.5]];
    _serviceButton.titleLabel.font = [UIFont systemFontOfSize: 12.0];
    [_serviceButton addTarget:self action:@selector(clickServiceButton) forControlEvents:UIControlEventTouchUpInside];
    [self.scrollView addSubview:_serviceButton];
}

- (void)addCharacteristicUI {
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(15, _serviceButton.frame.origin.y + _serviceButton.frame.size.height + 2, SCREEN_WIDTH-30, 15)];
    titleLabel.font = [UIFont systemFontOfSize:12];
    titleLabel.text = @"Characteristic:";
    [self.scrollView addSubview:titleLabel];

    UIImageView *imageView = [[UIImageView alloc] initWithFrame:CGRectMake(SCREEN_WIDTH-30-20, titleLabel.frame.origin.y + titleLabel.frame.size.height + 2 + 5, 20, 20)];
    imageView.image = [UIImage imageNamed:@"up"];
    [self.scrollView addSubview:imageView];

    _characteristicButton = [UIButton buttonWithType:UIButtonTypeSystem];
    _characteristicButton.frame = CGRectMake(15, titleLabel.frame.origin.y + titleLabel.frame.size.height + 2, SCREEN_WIDTH-30, 30);
    [_characteristicButton setTitleColor:UIColor.telinkTitleBlue forState:UIControlStateNormal];
    [_characteristicButton setBackgroundColor:[UIColor colorWithWhite:0.9 alpha:0.5]];
    _characteristicButton.titleLabel.font = [UIFont systemFontOfSize: 12.0];
    [_characteristicButton addTarget:self action:@selector(clickCharacteristicButton) forControlEvents:UIControlEventTouchUpInside];
    [self.scrollView addSubview:_characteristicButton];
}

- (void)addReadIntervalUI {
    _readIntervalLabel = [[UILabel alloc] initWithFrame:CGRectMake(15, _characteristicButton.frame.origin.y + _characteristicButton.frame.size.height + 2, SCREEN_WIDTH-30, 15)];
    _readIntervalLabel.font = [UIFont systemFontOfSize:12];
    _readIntervalLabel.text = [NSString stringWithFormat:@"Read Interval(0 as no-read): %d(packet)",self.otaSettings.readInterval];
    [self.scrollView addSubview:_readIntervalLabel];

    _readIntervalSlider = [[UISlider alloc] initWithFrame:CGRectMake(15, _readIntervalLabel.frame.origin.y + _readIntervalLabel.frame.size.height + 2, SCREEN_WIDTH-30, 30)];
    _readIntervalSlider.minimumValue = 0;
    _readIntervalSlider.maximumValue = 16;
    _readIntervalSlider.continuous = NO;
    [_readIntervalSlider addTarget:self action:@selector(clickReadIntervalSlider) forControlEvents:UIControlEventValueChanged];
    [self.scrollView addSubview:_readIntervalSlider];
}

- (void)addWriteIntervalUI {
    _writeIntervalLabel = [[UILabel alloc] initWithFrame:CGRectMake(15, _readIntervalSlider.frame.origin.y + _readIntervalSlider.frame.size.height + 2, SCREEN_WIDTH-30, 15)];
    _writeIntervalLabel.font = [UIFont systemFontOfSize:12];
    [self.scrollView addSubview:_writeIntervalLabel];

    _writeIntervalSlider = [[UISlider alloc] initWithFrame:CGRectMake(15, _writeIntervalLabel.frame.origin.y + _writeIntervalLabel.frame.size.height + 2, SCREEN_WIDTH-30, 30)];
    _writeIntervalSlider.minimumValue = 0;
    _writeIntervalSlider.maximumValue = 50;
    _writeIntervalSlider.continuous = NO;
    _writeIntervalSlider.value = self.otaSettings.writeInterval;
    [_writeIntervalSlider addTarget:self action:@selector(clickWriteIntervalSlider) forControlEvents:UIControlEventValueChanged];
    [self.scrollView addSubview:_writeIntervalSlider];
}

- (void)addFilePathUI {
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(15, _writeIntervalSlider.frame.origin.y + _writeIntervalSlider.frame.size.height + 2, SCREEN_WIDTH-30, 15)];
    titleLabel.font = [UIFont systemFontOfSize:12];
    titleLabel.text = @"Bin file:";
    [self.scrollView addSubview:titleLabel];

    UIImageView *imageView = [[UIImageView alloc] initWithFrame:CGRectMake(SCREEN_WIDTH-30-20, titleLabel.frame.origin.y + titleLabel.frame.size.height + 2 + 5, 20, 20)];
    UIImage *landscapeImage = [UIImage imageNamed:@"up"];
    UIImage *portraitImage = [[UIImage alloc] initWithCGImage: landscapeImage.CGImage scale: 1.0 orientation: UIImageOrientationLeft];
    imageView.image = portraitImage;
    [self.scrollView addSubview:imageView];

    _filePathButton = [UIButton buttonWithType:UIButtonTypeSystem];
    _filePathButton.frame = CGRectMake(15, titleLabel.frame.origin.y + titleLabel.frame.size.height + 2, SCREEN_WIDTH-30, 30);
    [_filePathButton setTitleColor:UIColor.telinkTitleBlue forState:UIControlStateNormal];
    [_filePathButton setBackgroundColor:[UIColor colorWithWhite:0.9 alpha:0.5]];
    _filePathButton.titleLabel.font = [UIFont systemFontOfSize: 13.0];
    _filePathButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
    [_filePathButton addTarget:self action:@selector(clickFilePathButton) forControlEvents:UIControlEventTouchUpInside];
    [self.scrollView addSubview:_filePathButton];
}

- (void)addSecurityBootEnableUI {
    _securityBootEnableButton = [UIButton buttonWithType:UIButtonTypeCustom];
    _securityBootEnableButton.frame = CGRectMake(15, _filePathButton.frame.origin.y + _filePathButton.frame.size.height + 2, 280, 30);
    [_securityBootEnableButton setTitleColor:UIColor.telinkTitleBlack forState:UIControlStateNormal];
    _securityBootEnableButton.titleLabel.font = [UIFont boldSystemFontOfSize: 15.0];
    _securityBootEnableButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
    [_securityBootEnableButton setImage:[UIImage imageNamed:@"duoxuanweixuanzhong"] forState:UIControlStateNormal];
    [_securityBootEnableButton setImage:[UIImage imageNamed:@"duoxuanxuanzhong"] forState:UIControlStateSelected];
    [_securityBootEnableButton setTitle:@"Security Boot Enable" forState:UIControlStateNormal];
    [_securityBootEnableButton setImageEdgeInsets:UIEdgeInsetsMake(0, 0, 0, 0)];
    _securityBootEnableButton.imageView.contentMode = UIViewContentModeScaleAspectFit;
    [_securityBootEnableButton addTarget:self action:@selector(clickSecurityBootEnableButton) forControlEvents:UIControlEventTouchUpInside];
    [self.scrollView addSubview:_securityBootEnableButton];
}

- (void)addSecurityBootFilePathUI {
    UILabel *securityBootFilePathLabel = [[UILabel alloc] initWithFrame:CGRectMake(15, _securityBootEnableButton.frame.origin.y + _securityBootEnableButton.frame.size.height + 2, SCREEN_WIDTH-30, 15)];
    securityBootFilePathLabel.font = [UIFont systemFontOfSize:12];
    securityBootFilePathLabel.text = @"Run Code Description file:";
    [self.scrollView addSubview:securityBootFilePathLabel];

    UIImageView *securityBootFilePathUpImageView = [[UIImageView alloc] initWithFrame:CGRectMake(SCREEN_WIDTH-30-20, securityBootFilePathLabel.frame.origin.y + securityBootFilePathLabel.frame.size.height + 2 + 5, 20, 20)];
    UIImage *landscapeImage = [UIImage imageNamed:@"up"];
    UIImage *portraitImage = [[UIImage alloc] initWithCGImage: landscapeImage.CGImage scale: 1.0 orientation: UIImageOrientationLeft];
    securityBootFilePathUpImageView.image = portraitImage;
    [self.scrollView addSubview:securityBootFilePathUpImageView];

    _securityBootFilePathButton = [UIButton buttonWithType:UIButtonTypeSystem];
    _securityBootFilePathButton.frame = CGRectMake(15, securityBootFilePathLabel.frame.origin.y + securityBootFilePathLabel.frame.size.height + 2, SCREEN_WIDTH-30, 30);
    [_securityBootFilePathButton setTitleColor:UIColor.telinkTitleBlue forState:UIControlStateNormal];
    [_securityBootFilePathButton setBackgroundColor:[UIColor colorWithWhite:0.9 alpha:0.5]];
    _securityBootFilePathButton.titleLabel.font = [UIFont systemFontOfSize: 13.0];
    _securityBootFilePathButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
    [_securityBootFilePathButton addTarget:self action:@selector(clickSecurityBootFilePathButton) forControlEvents:UIControlEventTouchUpInside];
    [self.scrollView addSubview:_securityBootFilePathButton];
}

- (void)addNeedSetFirmwareUI {
    _needSetFirmwareIndexButton = [UIButton buttonWithType:UIButtonTypeCustom];
    _needSetFirmwareIndexButton.frame = CGRectMake(15, _securityBootFilePathButton.frame.origin.y + _securityBootFilePathButton.frame.size.height + 2, 280, 30);
    [_needSetFirmwareIndexButton setTitleColor:UIColor.telinkTitleBlack forState:UIControlStateNormal];
    _needSetFirmwareIndexButton.titleLabel.font = [UIFont boldSystemFontOfSize: 15.0];
    _needSetFirmwareIndexButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
    [_needSetFirmwareIndexButton setImage:[UIImage imageNamed:@"duoxuanweixuanzhong"] forState:UIControlStateNormal];
    [_needSetFirmwareIndexButton setImage:[UIImage imageNamed:@"duoxuanxuanzhong"] forState:UIControlStateSelected];
    [_needSetFirmwareIndexButton setTitle:@"Set Firmware Index" forState:UIControlStateNormal];
    [_needSetFirmwareIndexButton setImageEdgeInsets:UIEdgeInsetsMake(0, 0, 0, 0)];
    _needSetFirmwareIndexButton.imageView.contentMode = UIViewContentModeScaleAspectFit;
    [_needSetFirmwareIndexButton addTarget:self action:@selector(clickNeedSetFirmwareIndexButton) forControlEvents:UIControlEventTouchUpInside];
    [self.scrollView addSubview:_needSetFirmwareIndexButton];
}

- (void)addFirmwareIndexUI {
    _firmwareIndexLabel = [[UILabel alloc] initWithFrame:CGRectMake(15, _needSetFirmwareIndexButton.frame.origin.y + _needSetFirmwareIndexButton.frame.size.height + 2, SCREEN_WIDTH-30, 15)];
    _firmwareIndexLabel.font = [UIFont systemFontOfSize:12];
    _firmwareIndexLabel.text = @"Firmware Index(HEX):";
    [self.scrollView addSubview:_firmwareIndexLabel];

    _firmwareIndexTF = [[UITextField alloc] initWithFrame:CGRectMake(15, _firmwareIndexLabel.frame.origin.y + _firmwareIndexLabel.frame.size.height + 2, SCREEN_WIDTH-30, 30)];
    _firmwareIndexTF.borderStyle = UITextBorderStyleLine;
    _firmwareIndexTF.text = [NSString stringWithFormat:@"%02X",self.otaSettings.firmwareIndex];
    [self.scrollView addSubview:_firmwareIndexTF];
}

- (void)addProtocolUI {
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(15, _firmwareIndexTF.frame.origin.y + _firmwareIndexTF.frame.size.height + 2, SCREEN_WIDTH-30, 15)];
    titleLabel.font = [UIFont systemFontOfSize:12];
    titleLabel.text = @"Protocol:";
    [self.scrollView addSubview:titleLabel];

    _protocolLegacyButton = [UIButton buttonWithType:UIButtonTypeCustom];
    _protocolLegacyButton.frame = CGRectMake(15, titleLabel.frame.origin.y + titleLabel.frame.size.height + 2, (SCREEN_WIDTH-30)/2, 30);
    [_protocolLegacyButton setTitleColor:UIColor.telinkTitleBlack forState:UIControlStateNormal];
    _protocolLegacyButton.titleLabel.font = [UIFont boldSystemFontOfSize: 15.0];
    _protocolLegacyButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
    [_protocolLegacyButton setImage:[UIImage imageNamed:@"danxuanweixuanzhong"] forState:UIControlStateNormal];
    [_protocolLegacyButton setImage:[UIImage imageNamed:@"danxuanxuanzhong"] forState:UIControlStateSelected];
    [_protocolLegacyButton setTitle:@"Legacy" forState:UIControlStateNormal];
    [_protocolLegacyButton setImageEdgeInsets:UIEdgeInsetsMake(5, 0, 5, 0)];
    _protocolLegacyButton.imageView.contentMode = UIViewContentModeScaleAspectFit;
    [_protocolLegacyButton addTarget:self action:@selector(clickProtocolLegacyButton) forControlEvents:UIControlEventTouchUpInside];
    [self.scrollView addSubview:_protocolLegacyButton];

    _protocolExtendButton = [UIButton buttonWithType:UIButtonTypeCustom];
    _protocolExtendButton.frame = CGRectMake(SCREEN_WIDTH/2, titleLabel.frame.origin.y + titleLabel.frame.size.height + 2, (SCREEN_WIDTH-30)/2, 30);
    [_protocolExtendButton setTitleColor:UIColor.telinkTitleBlack forState:UIControlStateNormal];
    _protocolExtendButton.titleLabel.font = [UIFont boldSystemFontOfSize: 15.0];
    _protocolExtendButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
    [_protocolExtendButton setImage:[UIImage imageNamed:@"danxuanweixuanzhong"] forState:UIControlStateNormal];
    [_protocolExtendButton setImage:[UIImage imageNamed:@"danxuanxuanzhong"] forState:UIControlStateSelected];
    [_protocolExtendButton setTitle:@"Extend" forState:UIControlStateNormal];
    [_protocolExtendButton setImageEdgeInsets:UIEdgeInsetsMake(5, 0, 5, 0)];
    _protocolExtendButton.imageView.contentMode = UIViewContentModeScaleAspectFit;
    [_protocolExtendButton addTarget:self action:@selector(clickProtocolExtendButton) forControlEvents:UIControlEventTouchUpInside];
    [self.scrollView addSubview:_protocolExtendButton];
}

- (void)addVersionCompareUI {
    _versionCompareButton = [UIButton buttonWithType:UIButtonTypeCustom];
    _versionCompareButton.frame = CGRectMake(15, _protocolLegacyButton.frame.origin.y + _protocolLegacyButton.frame.size.height + 2, 180, 30);
    [_versionCompareButton setTitleColor:UIColor.telinkTitleBlack forState:UIControlStateNormal];
    _versionCompareButton.titleLabel.font = [UIFont boldSystemFontOfSize: 15.0];
    _versionCompareButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
    [_versionCompareButton setImage:[UIImage imageNamed:@"duoxuanweixuanzhong"] forState:UIControlStateNormal];
    [_versionCompareButton setImage:[UIImage imageNamed:@"duoxuanxuanzhong"] forState:UIControlStateSelected];
    [_versionCompareButton setTitle:@"Version Compare" forState:UIControlStateNormal];
    [_versionCompareButton setImageEdgeInsets:UIEdgeInsetsMake(0, 0, 0, 0)];
    _versionCompareButton.imageView.contentMode = UIViewContentModeScaleAspectFit;
    [_versionCompareButton addTarget:self action:@selector(clickVersionCompareButton) forControlEvents:UIControlEventTouchUpInside];
    [self.scrollView addSubview:_versionCompareButton];
}

- (void)addBinVersionUI {
    _binVersionLabel = [[UILabel alloc] initWithFrame:CGRectMake(15, _versionCompareButton.frame.origin.y + _versionCompareButton.frame.size.height + 2, SCREEN_WIDTH-30, 15)];
    _binVersionLabel.font = [UIFont systemFontOfSize:12];
    _binVersionLabel.text = @"Bin version(HEX):";
    [self.scrollView addSubview:_binVersionLabel];

    _binVersionTF = [[UITextField alloc] initWithFrame:CGRectMake(15, _binVersionLabel.frame.origin.y + _binVersionLabel.frame.size.height + 2, SCREEN_WIDTH-30, 30)];
    _binVersionTF.borderStyle = UITextBorderStyleLine;
    [self.scrollView addSubview:_binVersionTF];
}

- (void)addPduLengthUI {
    _pduLengthLabel = [[UILabel alloc] initWithFrame:CGRectMake(15, _binVersionTF.frame.origin.y + _binVersionTF.frame.size.height + 2, SCREEN_WIDTH-30, 15)];
    _pduLengthLabel.font = [UIFont systemFontOfSize:12];
    _pduLengthLabel.text = @"PDU Length(16*n, 16 ~ 240):";
    [self.scrollView addSubview:_pduLengthLabel];

    _pduLengthTF = [[UITextField alloc] initWithFrame:CGRectMake(15, _pduLengthLabel.frame.origin.y + _pduLengthLabel.frame.size.height + 2, SCREEN_WIDTH-30, 30)];
    _pduLengthTF.borderStyle = UITextBorderStyleLine;
    _pduLengthTF.keyboardType = UIKeyboardTypeNumberPad;
    [self.scrollView addSubview:_pduLengthTF];
}

- (void)refreshUIWithOtaSettingsModel {
    [self refreshServiceUI];
    [self refreshCharacteristicUI];
    [self refreshReadIntervalUI];
    [self refreshWriteIntervalUI];
    [self refreshBinFilePathUI];
    [self refreshFirmwareIndexUI];
    [self refreshSecurityBootUI];
    if (self.otaSettings.protocol == TelinkOtaProtocol_legacy) {
        [self clickProtocolLegacyButton];
    } else if (self.otaSettings.protocol == TelinkOtaProtocol_extend) {
        [self clickProtocolExtendButton];
    }
}

- (void)refreshServiceUI {
    if (self.otaSettings.serviceUuidString) {
        [_serviceButton setTitle:self.otaSettings.serviceUuidString forState:UIControlStateNormal];
    } else {
        [_serviceButton setTitle:@"service uuid(Default: 1912)" forState:UIControlStateNormal];
    }
}

- (void)refreshCharacteristicUI {
    if (self.otaSettings.characteristicUuidString) {
        [_characteristicButton setTitle:self.otaSettings.characteristicUuidString forState:UIControlStateNormal];
    } else {
        [_characteristicButton setTitle:@"characteristic uuid(Default: 2B12)" forState:UIControlStateNormal];
    }
}

- (void)refreshReadIntervalUI {
    _readIntervalLabel.text = [NSString stringWithFormat:@"Read Interval(0 as no-read): %d(packet)",self.otaSettings.readInterval];
    _readIntervalSlider.value = self.otaSettings.readInterval;
}

- (void)refreshWriteIntervalUI {
    _writeIntervalLabel.text = [NSString stringWithFormat:@"Write Interval(0 as no-delay): %d(ms)",self.otaSettings.writeInterval];
    _writeIntervalSlider.value = self.otaSettings.writeInterval;
}

- (void)refreshBinFilePathUI {
    if (self.otaSettings.filePath) {
        [_filePathButton setTitle:self.otaSettings.filePath forState:UIControlStateNormal];
    } else {
        [_filePathButton setTitle:@"[NULL]" forState:UIControlStateNormal];
    }
}

- (void)refreshFirmwareIndexUI {
    _needSetFirmwareIndexButton.selected = self.otaSettings.needSetFirmwareIndex;
}

- (void)refreshSecurityBootUI {
    _securityBootEnableButton.selected = self.otaSettings.securityBootEnable;
    if (self.otaSettings.securityBootFilePath) {
        [_securityBootFilePathButton setTitle:self.otaSettings.securityBootFilePath forState:UIControlStateNormal];
    } else {
        [_securityBootFilePathButton setTitle:@"[NULL]" forState:UIControlStateNormal];
    }
}

- (void)refreshProtocolUI {
    if (self.otaSettings.protocol == TelinkOtaProtocol_legacy) {
        [self setExtendUIEnable:NO];
    } else if (self.otaSettings.protocol == TelinkOtaProtocol_extend) {
        [self setExtendUIEnable:YES];
        _versionCompareButton.selected = self.otaSettings.versionCompare;
        _binVersionTF.text = [NSString stringWithFormat:@"%04X",self.otaSettings.binVersion];
        _pduLengthTF.text = [NSString stringWithFormat:@"%d",self.otaSettings.pduLength];
    }
}

- (void)setExtendUIEnable:(BOOL)enable {
    _versionCompareButton.hidden = !enable;
    _binVersionLabel.hidden = !enable;
    _binVersionTF.hidden = !enable;
    _pduLengthLabel.hidden = !enable;
    _pduLengthTF.hidden = !enable;
    if (enable) {
        self.scrollView.contentSize = CGSizeMake(SCREEN_WIDTH, _pduLengthTF.frame.origin.y + _pduLengthTF.frame.size.height + 10);
    } else {
        self.scrollView.contentSize = CGSizeMake(SCREEN_WIDTH, _protocolLegacyButton.frame.origin.y + _protocolLegacyButton.frame.size.height + 10);
    }
}

-(void)clickBack {
    TelinkDebugLog(@"clickBack");
    if ([self compareOneSettings:self.oldOTASettings otherSettings:self.otaSettings]) {
        //未修改
        [self.navigationController popViewControllerAnimated:YES];
    } else {
        //已经修改
        __weak typeof(self) weakSelf = self;
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:nil message:@"The configure of OTA had changed but not save, are you make sure to save?" preferredStyle:UIAlertControllerStyleAlert];
        [alert addAction:[UIAlertAction actionWithTitle:@"YES" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [weakSelf clickSave];
        }]];
        [alert addAction:[UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
            [weakSelf.navigationController popViewControllerAnimated:YES];
        }]];
        [[UIApplication sharedApplication].keyWindow.rootViewController presentViewController:alert animated:YES completion:nil];
    }
}

- (void)clickReset {
    TelinkDebugLog(@"clickReset");
    [self.view endEditing:YES];
    
    __weak typeof(self) weakSelf = self;
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:nil message:@"Are you make sure to reset all the configure of OTA?" preferredStyle:UIAlertControllerStyleAlert];
    [alert addAction:[UIAlertAction actionWithTitle:@"Reset" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        OTASettingsModel *model = [[OTASettingsModel alloc] init];
        weakSelf.otaSettings = model;
        [weakSelf refreshUIWithOtaSettingsModel];
    }]];
    [alert addAction:[UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {

    }]];
    [[UIApplication sharedApplication].keyWindow.rootViewController presentViewController:alert animated:YES completion:nil];
}

- (void)clickSave {
    TelinkDebugLog(@"clickSave");
    [self.view endEditing:YES];
    if (self.otaSettings.serviceUuidString && self.otaSettings.serviceUuidString.length > 0 && (self.otaSettings.characteristicUuidString == nil || self.otaSettings.characteristicUuidString.length == 0)) {
        [ShowHandle alertShowTips:@"Please select characteristic" doBlock:nil cancelBlock:nil];
        return;
    }
    if (self.otaSettings.filePath == nil || self.otaSettings.filePath.length == 0) {
        [ShowHandle alertShowTips:@"Please select bin file" doBlock:nil cancelBlock:nil];
        return;
    }
    NSData *data = [FileDataSource.share getDataWithLastPathComponent:self.otaSettings.filePath];
    if (data == nil || data.length == 0) {
        [ShowHandle alertShowTips:[NSString stringWithFormat:@"Read bin file `%@` fail, please select other bin file",self.otaSettings.filePath] doBlock:nil cancelBlock:nil];
        return;
    }
    if (_needSetFirmwareIndexButton.selected) {
        if (_firmwareIndexTF.text.length != 1 && _firmwareIndexTF.text.length != 2) {
            [ShowHandle alertShowTips:@"The size of `Firmware Index` is one bytes." doBlock:nil cancelBlock:nil];
            return;
        }
        BOOL result = [self validateHexadecimalString:_firmwareIndexTF.text];
        if (result == NO) {
            [ShowHandle alertShowTips:@"Please enter hexadecimal string to `Firmware Index`." doBlock:nil cancelBlock:nil];
            return;
        }
        self.otaSettings.firmwareIndex = [self uint8From16String:_firmwareIndexTF.text];
    }

    if (self.otaSettings.securityBootEnable) {
        if (self.otaSettings.securityBootFilePath == nil || self.otaSettings.securityBootFilePath.length == 0) {
            [ShowHandle alertShowTips:@"Please select security boot bin file" doBlock:nil cancelBlock:nil];
            return;
        }
        NSData *data = [FileDataSource.share getDataWithLastPathComponent:self.otaSettings.securityBootFilePath];
        if (data == nil || data.length == 0) {
            [ShowHandle alertShowTips:[NSString stringWithFormat:@"Read security boot bin file `%@` fail, please select other security boot bin file",self.otaSettings.securityBootFilePath] doBlock:nil cancelBlock:nil];
            return;
        }
    }

    if (self.otaSettings.protocol == TelinkOtaProtocol_extend) {
        if (_binVersionTF.text.length != 4) {
            [ShowHandle alertShowTips:@"The size of `Bin version` is two bytes." doBlock:nil cancelBlock:nil];
            return;
        }
        BOOL result = [self validateHexadecimalString:_binVersionTF.text];
        if (result == NO) {
            [ShowHandle alertShowTips:@"Please enter hexadecimal string to `Bin version`." doBlock:nil cancelBlock:nil];
            return;
        }
        self.otaSettings.binVersion = [self uint16From16String:_binVersionTF.text];
        TelinkDebugLog(@"_binVersionTF.text=%@ -> self.otaSettings.binVersion=0x%04X",_binVersionTF.text,self.otaSettings.binVersion);
        
        result = [self validateDecimalString:_pduLengthTF.text];
        if (result == NO) {
            [ShowHandle alertShowTips:@"Please enter decimal string to `PDU Length`." doBlock:nil cancelBlock:nil];
            return;
        }
        int length = [_pduLengthTF.text intValue];
        if (length < 16 || length > 240) {
            [ShowHandle alertShowTips:@"The range of `PDU Length` is 16 ~ 240." doBlock:nil cancelBlock:nil];
            return;
        }
        if (length % 16 != 0) {
            [ShowHandle alertShowTips:@"`PDU Length` is not a multiple of 16." doBlock:nil cancelBlock:nil];
            return;
        }
        self.otaSettings.pduLength = length;
        TelinkDebugLog(@"_pduLengthTF.text=%@ -> self.otaSettings.pduLength=%d",_pduLengthTF.text,self.otaSettings.pduLength);
    }
    
    if (self.otaSettings.protocol == TelinkOtaProtocol_legacy) {
        self.otaSettings.pduLength = 16;
    }
    if (self.backOtaSettings) {
        self.backOtaSettings(self.otaSettings);
    }
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)clickServiceButton {
    TelinkDebugLog(@"clickServiceButton");
    if (self.peripheral.state != CBPeripheralStateConnected) {
        [ShowHandle alertShowTips:@"device not connected" doBlock:nil cancelBlock:nil];
        return;
    }
    __weak typeof(self) weakSelf = self;
    UIAlertController *actionSheet = [UIAlertController alertControllerWithTitle:@"Select Custom Service" message:nil preferredStyle:UIAlertControllerStyleActionSheet];
    for (CBService *service in self.peripheral.services) {
        NSString *serviceUuid = service.UUID.UUIDString;
        UIAlertAction *alertT = [UIAlertAction actionWithTitle:serviceUuid style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            TelinkDebugLog(@"select serviceUuid=%@",serviceUuid);
            weakSelf.otaSettings.serviceUuidString = serviceUuid;
            [weakSelf refreshServiceUI];
        }];
        [actionSheet addAction:alertT];
    }
    UIAlertAction *alertF = [UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        TelinkDebugLog(@"Cancel");
    }];
    [actionSheet addAction:alertF];
    actionSheet.popoverPresentationController.sourceView = _serviceButton;
    actionSheet.popoverPresentationController.sourceRect =  _serviceButton.frame;
    [self presentViewController:actionSheet animated:YES completion:nil];
}

- (void)clickCharacteristicButton {
    TelinkDebugLog(@"clickCharacteristicButton");
    if (self.peripheral.state != CBPeripheralStateConnected) {
        [ShowHandle alertShowTips:@"device not connected" doBlock:nil cancelBlock:nil];
        return;
    }
    if (self.otaSettings.serviceUuidString == nil || self.otaSettings.serviceUuidString.length == 0) {
        [ShowHandle alertShowTips:@"select service first" doBlock:nil cancelBlock:nil];
        return;
    }
    __weak typeof(self) weakSelf = self;
    UIAlertController *actionSheet = [UIAlertController alertControllerWithTitle:@"Select Custom Characteristic" message:nil preferredStyle:UIAlertControllerStyleActionSheet];
    for (CBService *service in self.peripheral.services) {
        if ([service.UUID.UUIDString isEqualToString:self.otaSettings.serviceUuidString]) {
            for (CBCharacteristic *characteristic in service.characteristics) {
                NSString *characteristicUuid = characteristic.UUID.UUIDString;
                UIAlertAction *alertT = [UIAlertAction actionWithTitle:characteristicUuid style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                    TelinkDebugLog(@"select characteristicUuid=%@",characteristicUuid);
                    weakSelf.otaSettings.characteristicUuidString = characteristicUuid;
                    [weakSelf refreshCharacteristicUI];
                }];
                [actionSheet addAction:alertT];
            }
        }
    }
    UIAlertAction *alertF = [UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        TelinkDebugLog(@"Cancel");
    }];
    [actionSheet addAction:alertF];
    actionSheet.popoverPresentationController.sourceView = _characteristicButton;
    actionSheet.popoverPresentationController.sourceRect =  _characteristicButton.frame;
    [self presentViewController:actionSheet animated:YES completion:nil];
}

- (void)clickReadIntervalSlider {
    TelinkDebugLog(@"clickReadIntervalSlider");
    int discreteValue = roundl([_readIntervalSlider value]); // Rounds float to an integer
    self.otaSettings.readInterval = discreteValue;
    [self refreshReadIntervalUI];
}

- (void)clickWriteIntervalSlider {
    TelinkDebugLog(@"clickWriteIntervalSlider");
    int discreteValue = roundl([_writeIntervalSlider value]); // Rounds float to an integer
    self.otaSettings.writeInterval = discreteValue;
    [self refreshWriteIntervalUI];
}

- (void)clickFilePathButton {
    TelinkDebugLog(@"clickFilePathButton");
    ChooseFileVC *vc = [[ChooseFileVC alloc] init];
    vc.filePath = self.otaSettings.filePath;
    __weak typeof(self) weakSelf = self;
    [vc setBackFilePath:^(NSString * _Nonnull filePath) {
        weakSelf.otaSettings.filePath = filePath;
        [weakSelf refreshBinFilePathUI];
    }];
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)clickNeedSetFirmwareIndexButton {
    TelinkDebugLog(@"clickNeedSetFirmwareIndexButton");
    _needSetFirmwareIndexButton.selected = !_needSetFirmwareIndexButton.selected;
    self.otaSettings.needSetFirmwareIndex = _needSetFirmwareIndexButton.selected;
    if (_firmwareIndexTF.text.length) {
        self.otaSettings.firmwareIndex = [self uint8From16String:_firmwareIndexTF.text];
    }
}

- (void)clickSecurityBootEnableButton {
    TelinkDebugLog(@"clickSecurityBootEnableButton");
    _securityBootEnableButton.selected = !_securityBootEnableButton.selected;
    self.otaSettings.securityBootEnable = _securityBootEnableButton.selected;
}

- (void)clickSecurityBootFilePathButton {
    TelinkDebugLog(@"clickSecurityBootFilePathButton");
    ChooseFileVC *vc = [[ChooseFileVC alloc] init];
    vc.filePath = self.otaSettings.securityBootFilePath;
    __weak typeof(self) weakSelf = self;
    [vc setBackFilePath:^(NSString * _Nonnull filePath) {
        weakSelf.otaSettings.securityBootFilePath = filePath;
        [weakSelf refreshSecurityBootUI];
    }];
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)clickProtocolLegacyButton {
    TelinkDebugLog(@"clickProtocolLegacyButton");
    _protocolLegacyButton.selected = YES;
    _protocolExtendButton.selected = NO;
    self.otaSettings.protocol = TelinkOtaProtocol_legacy;
    [self refreshProtocolUI];
}

- (void)clickProtocolExtendButton {
    TelinkDebugLog(@"clickProtocolExtendButton");
    _protocolLegacyButton.selected = NO;
    _protocolExtendButton.selected = YES;
    self.otaSettings.protocol = TelinkOtaProtocol_extend;
    [self refreshProtocolUI];
}

- (void)clickVersionCompareButton {
    TelinkDebugLog(@"clickVersionCompareButton");
    _versionCompareButton.selected = !_versionCompareButton.selected;
    self.otaSettings.versionCompare = _versionCompareButton.selected;
}

- (BOOL)compareOneSettings:(OTASettingsModel *)oneSettings otherSettings:(OTASettingsModel *)otherSettings {
    BOOL tem = YES;
    if (![oneSettings.serviceUuidString isEqualToString:otherSettings.serviceUuidString] && oneSettings.serviceUuidString != nil && otherSettings.serviceUuidString != nil) {
        return NO;
    }
    if (![oneSettings.characteristicUuidString isEqualToString:otherSettings.characteristicUuidString] && oneSettings.characteristicUuidString != nil && otherSettings.characteristicUuidString != nil) {
        return NO;
    }
    if (oneSettings.readInterval != otherSettings.readInterval) {
        return NO;
    }
    if (oneSettings.writeInterval != otherSettings.writeInterval) {
        return NO;
    }
    if (![oneSettings.filePath isEqualToString:otherSettings.filePath] && oneSettings.filePath != nil && otherSettings.filePath != nil) {
        return NO;
    }
    if (oneSettings.needSetFirmwareIndex != otherSettings.needSetFirmwareIndex) {
        return NO;
    }
    if (oneSettings.firmwareIndex != otherSettings.firmwareIndex) {
        return NO;
    }
    if (oneSettings.protocol != otherSettings.protocol) {
        return NO;
    }
    if (oneSettings.securityBootEnable != otherSettings.securityBootEnable) {
        return NO;
    }
    if (oneSettings.securityBootEnable) {
        if (![oneSettings.securityBootFilePath isEqualToString:otherSettings.securityBootFilePath] && oneSettings.securityBootFilePath != nil && otherSettings.securityBootFilePath != nil) {
            return NO;
        }
    }
    if (oneSettings.protocol == TelinkOtaProtocol_legacy) {
        return tem;
    } else if (oneSettings.protocol == TelinkOtaProtocol_extend) {
        if (oneSettings.versionCompare != otherSettings.versionCompare) {
            return NO;
        }
        if (oneSettings.binVersion != otherSettings.binVersion) {
            return NO;
        }
        if (oneSettings.pduLength != otherSettings.pduLength) {
            return NO;
        }
    }
    return tem;
}

- (BOOL)validateHexadecimalString:(NSString *)str{
    NSString *strRegex = @"^[0-9a-fA-F]{0,}$";
    NSPredicate *strPredicate = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",strRegex];
    return [strPredicate evaluateWithObject:str];
}

- (BOOL)validateDecimalString:(NSString *)str{
    NSString *strRegex = @"^[0-9]{0,}$";
    NSPredicate *strPredicate = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",strRegex];
    return [strPredicate evaluateWithObject:str];
}

///16进制NSString转Uint8
- (UInt8)uint8From16String:(NSString *)string{
    if (string == nil || string.length == 0) {
        return 0;
    }
    return [self uint8FromBytes:[self turnOverData:[self nsstringToHex:string]]];
}

///16进制NSString转Uint16
- (UInt16)uint16From16String:(NSString *)string{
    if (string == nil || string.length == 0) {
        return 0;
    }
    return [self uint16FromBytes:[self turnOverData:[self nsstringToHex:string]]];
}

///NSData转Uint8
- (UInt8)uint8FromBytes:(NSData *)fData
{
    NSAssert(fData.length == 1, @"uint8FromBytes: (data length != 1)");
    NSData *data = fData;
    UInt8 val = 0;
    [data getBytes:&val length:1];
    return val;
}

///NSData转Uint16
- (UInt16)uint16FromBytes:(NSData *)fData
{
    NSAssert(fData.length <= 2, @"uint16FromBytes: (data length > 2)");
    //    NSData *data = [self turnOverData:fData];
    NSData *data = fData;
    
    UInt16 val0 = 0;
    UInt16 val1 = 0;
    [data getBytes:&val0 range:NSMakeRange(0, 1)];
    if (data.length > 1) [data getBytes:&val1 range:NSMakeRange(1, 1)];
    
    UInt16 dstVal = (val0 & 0xff) + ((val1 << 8) & 0xff00);
    return dstVal;
}

///NSData字节翻转
- (NSData *)turnOverData:(NSData *)data{
    NSMutableData *backData = [NSMutableData data];
    for (int i=0; i<data.length; i++) {
        [backData appendData:[data subdataWithRange:NSMakeRange(data.length-1-i, 1)]];
    }
    return backData;
}

- (NSData *)nsstringToHex:(NSString *)string{
    const char *buf = [string UTF8String];
    NSMutableData *data = [NSMutableData data];
    if (buf)
    {
        unsigned long len = strlen(buf);
        char singleNumberString[3] = {'\0', '\0', '\0'};
        uint32_t singleNumber = 0;
        for(uint32_t i = 0 ; i < len; i+=2)
        {
            if ( ((i+1) < len) && isxdigit(buf[i])  )
            {
                singleNumberString[0] = buf[i];
                singleNumberString[1] = buf[i + 1];
                sscanf(singleNumberString, "%x", &singleNumber);
                uint8_t tmp = (uint8_t)(singleNumber & 0x000000FF);
                [data appendBytes:(void *)(&tmp)length:1];
            } else if (len == 1 && isxdigit(buf[i])) {
                singleNumberString[0] = buf[i];
                sscanf(singleNumberString, "%x", &singleNumber);
                uint8_t tmp = (uint8_t)(singleNumber & 0x000000FF);
                [data appendBytes:(void *)(&tmp)length:1];
            }
            else
            {
                break;
            }
        }
    }
    return data;
}

-(void)dealloc{
    TelinkDebugLog(@"%s",__func__);
}

@end
