// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 50;
	objects = {

/* Begin PBXBuildFile section */
		25147B9C27CF5125009B6500 /* CustomAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = 25147B9627CF5125009B6500 /* CustomAlertView.m */; };
		25147B9D27CF5125009B6500 /* CustomAlert.m in Sources */ = {isa = PBXBuildFile; fileRef = 25147B9727CF5125009B6500 /* CustomAlert.m */; };
		25147B9E27CF5125009B6500 /* CustomAlert.xib in Resources */ = {isa = PBXBuildFile; fileRef = 25147B9827CF5125009B6500 /* CustomAlert.xib */; };
		25147BA127CF5295009B6500 /* BaseModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 25147BA027CF5295009B6500 /* BaseModel.m */; };
		2557EDF82744A2550092C2BA /* IQTextView.m in Sources */ = {isa = PBXBuildFile; fileRef = 2557EDD72744A2550092C2BA /* IQTextView.m */; };
		2557EDF92744A2550092C2BA /* IQToolbar.m in Sources */ = {isa = PBXBuildFile; fileRef = 2557EDDB2744A2550092C2BA /* IQToolbar.m */; };
		2557EDFA2744A2550092C2BA /* IQTitleBarButtonItem.m in Sources */ = {isa = PBXBuildFile; fileRef = 2557EDDD2744A2550092C2BA /* IQTitleBarButtonItem.m */; };
		2557EDFB2744A2550092C2BA /* IQBarButtonItem.m in Sources */ = {isa = PBXBuildFile; fileRef = 2557EDDE2744A2550092C2BA /* IQBarButtonItem.m */; };
		2557EDFC2744A2550092C2BA /* IQUIView+IQKeyboardToolbar.m in Sources */ = {isa = PBXBuildFile; fileRef = 2557EDDF2744A2550092C2BA /* IQUIView+IQKeyboardToolbar.m */; };
		2557EDFD2744A2550092C2BA /* IQPreviousNextView.m in Sources */ = {isa = PBXBuildFile; fileRef = 2557EDE02744A2550092C2BA /* IQPreviousNextView.m */; };
		2557EDFE2744A2550092C2BA /* IQKeyboardManager.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 2557EDE82744A2550092C2BA /* IQKeyboardManager.bundle */; };
		2557EDFF2744A2550092C2BA /* IQKeyboardManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 2557EDEB2744A2550092C2BA /* IQKeyboardManager.m */; };
		2557EE002744A2550092C2BA /* IQNSArray+Sort.m in Sources */ = {isa = PBXBuildFile; fileRef = 2557EDED2744A2550092C2BA /* IQNSArray+Sort.m */; };
		2557EE012744A2550092C2BA /* IQUITextFieldView+Additions.m in Sources */ = {isa = PBXBuildFile; fileRef = 2557EDEF2744A2550092C2BA /* IQUITextFieldView+Additions.m */; };
		2557EE022744A2550092C2BA /* IQUIScrollView+Additions.m in Sources */ = {isa = PBXBuildFile; fileRef = 2557EDF02744A2550092C2BA /* IQUIScrollView+Additions.m */; };
		2557EE032744A2550092C2BA /* IQUIView+Hierarchy.m in Sources */ = {isa = PBXBuildFile; fileRef = 2557EDF12744A2550092C2BA /* IQUIView+Hierarchy.m */; };
		2557EE042744A2550092C2BA /* IQUIViewController+Additions.m in Sources */ = {isa = PBXBuildFile; fileRef = 2557EDF52744A2550092C2BA /* IQUIViewController+Additions.m */; };
		2557EE052744A2550092C2BA /* IQKeyboardReturnKeyHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 2557EDF72744A2550092C2BA /* IQKeyboardReturnKeyHandler.m */; };
		2557EE0E27450AD80092C2BA /* 8258_demo.bin in Resources */ = {isa = PBXBuildFile; fileRef = 2557EE0D27450AD80092C2BA /* 8258_demo.bin */; };
		25A45F6B28FFF66F006742A7 /* SettingsVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 25A45F6A28FFF66F006742A7 /* SettingsVC.m */; };
		25A45F7028FFF8ED006742A7 /* BaseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 25A45F6F28FFF8ED006742A7 /* BaseViewController.m */; };
		25A45F7428FFF948006742A7 /* UIImage+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 25A45F7228FFF948006742A7 /* UIImage+Extension.m */; };
		25A45F7828FFFBE1006742A7 /* FilterVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 25A45F7728FFFBE1006742A7 /* FilterVC.m */; };
		25A45F7B28FFFC88006742A7 /* UIStoryboard+extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 25A45F7A28FFFC88006742A7 /* UIStoryboard+extension.m */; };
		25A5B45E24C04DF50073BCB6 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 25A5B45D24C04DF50073BCB6 /* AppDelegate.m */; };
		25A5B46724C04DF50073BCB6 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 25A5B46524C04DF50073BCB6 /* Main.storyboard */; };
		25A5B46924C04DF90073BCB6 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 25A5B46824C04DF90073BCB6 /* Assets.xcassets */; };
		25A5B46C24C04DF90073BCB6 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 25A5B46A24C04DF90073BCB6 /* LaunchScreen.storyboard */; };
		25A5B46F24C04DF90073BCB6 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 25A5B46E24C04DF90073BCB6 /* main.m */; };
		25A5B47A24C04EE20073BCB6 /* 8267_module_tBase.bin in Resources */ = {isa = PBXBuildFile; fileRef = 25A5B47824C04EE20073BCB6 /* 8267_module_tBase.bin */; };
		25A5B47B24C04EE20073BCB6 /* 8267_module_tOTA.bin in Resources */ = {isa = PBXBuildFile; fileRef = 25A5B47924C04EE20073BCB6 /* 8267_module_tOTA.bin */; };
		25A5B48624C04EF80073BCB6 /* OtaViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 25A5B47C24C04EF70073BCB6 /* OtaViewController.m */; };
		25A5B48724C04EF80073BCB6 /* ScanViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 25A5B48324C04EF80073BCB6 /* ScanViewController.m */; };
		25A5B48924C04EF80073BCB6 /* DeviceCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 25A5B48124C04EF80073BCB6 /* DeviceCell.m */; };
		25A5B48F24C04F060073BCB6 /* ARShowTips.m in Sources */ = {isa = PBXBuildFile; fileRef = 25A5B48E24C04F060073BCB6 /* ARShowTips.m */; };
		25A5B49024C04F060073BCB6 /* ARShowTipsView.m in Sources */ = {isa = PBXBuildFile; fileRef = 25A5B48B24C04F060073BCB6 /* ARShowTipsView.m */; };
		25A5B49C24C052150073BCB6 /* ARShowTipsView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 25A5B48C24C04F060073BCB6 /* ARShowTipsView.xib */; };
		25C2CB0D27422E5B00132F00 /* OTASettingsVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 25C2CB0B27422E5B00132F00 /* OTASettingsVC.m */; };
		25C2CB1327422ECE00132F00 /* ChooseFileVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 25C2CB1127422ECE00132F00 /* ChooseFileVC.m */; };
		9112D92A2BB2B6390084ED9F /* UIView+Toast.m in Sources */ = {isa = PBXBuildFile; fileRef = 9112D9282BB2B6390084ED9F /* UIView+Toast.m */; };
		914B5E972AFDC5580007BD58 /* SwitchCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 914B5E952AFDC5580007BD58 /* SwitchCell.m */; };
		914B5E982AFDC5580007BD58 /* SwitchCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 914B5E962AFDC5580007BD58 /* SwitchCell.xib */; };
		914B5E9C2AFDCB890007BD58 /* StringTipsVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 914B5E9B2AFDCB890007BD58 /* StringTipsVC.m */; };
		9173FCD6291A41300040F772 /* UIDevice+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 9173FCD4291A41300040F772 /* UIDevice+Extension.m */; };
		918799D6292247D400643A1A /* TipsVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 918799D5292247D400643A1A /* TipsVC.m */; };
		91A4509E2907CBAB00D5EF76 /* UIColor+Telink.m in Sources */ = {isa = PBXBuildFile; fileRef = 91A4509D2907CBAB00D5EF76 /* UIColor+Telink.m */; };
		91ED0CC62AFCA22100CB3868 /* 8208_dual_mouse.bin in Resources */ = {isa = PBXBuildFile; fileRef = 91ED0CC42AFCA22100CB3868 /* 8208_dual_mouse.bin */; };
		91F8809E2A0A37C9000E9DB1 /* TelinkGenericOTALib.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2557EE0B2744A2670092C2BA /* TelinkGenericOTALib.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		2557EE0A2744A2670092C2BA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2557EE062744A2670092C2BA /* TelinkGenericOTALib.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 257CF72A24BD44FC0053C253;
			remoteInfo = TelinkGenericOTALib;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		25147B9627CF5125009B6500 /* CustomAlertView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CustomAlertView.m; sourceTree = "<group>"; };
		25147B9727CF5125009B6500 /* CustomAlert.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CustomAlert.m; sourceTree = "<group>"; };
		25147B9827CF5125009B6500 /* CustomAlert.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = CustomAlert.xib; sourceTree = "<group>"; };
		25147B9927CF5125009B6500 /* CustomAlert.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CustomAlert.h; sourceTree = "<group>"; };
		25147B9A27CF5125009B6500 /* CustomAlertView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CustomAlertView.h; sourceTree = "<group>"; };
		25147B9F27CF5295009B6500 /* BaseModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BaseModel.h; sourceTree = "<group>"; };
		25147BA027CF5295009B6500 /* BaseModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BaseModel.m; sourceTree = "<group>"; };
		2557EDD72744A2550092C2BA /* IQTextView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IQTextView.m; sourceTree = "<group>"; };
		2557EDD82744A2550092C2BA /* IQTextView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQTextView.h; sourceTree = "<group>"; };
		2557EDDA2744A2550092C2BA /* IQUIView+IQKeyboardToolbar.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "IQUIView+IQKeyboardToolbar.h"; sourceTree = "<group>"; };
		2557EDDB2744A2550092C2BA /* IQToolbar.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IQToolbar.m; sourceTree = "<group>"; };
		2557EDDC2744A2550092C2BA /* IQPreviousNextView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQPreviousNextView.h; sourceTree = "<group>"; };
		2557EDDD2744A2550092C2BA /* IQTitleBarButtonItem.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IQTitleBarButtonItem.m; sourceTree = "<group>"; };
		2557EDDE2744A2550092C2BA /* IQBarButtonItem.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IQBarButtonItem.m; sourceTree = "<group>"; };
		2557EDDF2744A2550092C2BA /* IQUIView+IQKeyboardToolbar.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "IQUIView+IQKeyboardToolbar.m"; sourceTree = "<group>"; };
		2557EDE02744A2550092C2BA /* IQPreviousNextView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IQPreviousNextView.m; sourceTree = "<group>"; };
		2557EDE12744A2550092C2BA /* IQToolbar.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQToolbar.h; sourceTree = "<group>"; };
		2557EDE22744A2550092C2BA /* IQBarButtonItem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQBarButtonItem.h; sourceTree = "<group>"; };
		2557EDE32744A2550092C2BA /* IQTitleBarButtonItem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQTitleBarButtonItem.h; sourceTree = "<group>"; };
		2557EDE52744A2550092C2BA /* IQKeyboardManagerConstantsInternal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQKeyboardManagerConstantsInternal.h; sourceTree = "<group>"; };
		2557EDE62744A2550092C2BA /* IQKeyboardManagerConstants.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQKeyboardManagerConstants.h; sourceTree = "<group>"; };
		2557EDE82744A2550092C2BA /* IQKeyboardManager.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = IQKeyboardManager.bundle; sourceTree = "<group>"; };
		2557EDE92744A2550092C2BA /* IQKeyboardReturnKeyHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQKeyboardReturnKeyHandler.h; sourceTree = "<group>"; };
		2557EDEA2744A2550092C2BA /* IQKeyboardManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQKeyboardManager.h; sourceTree = "<group>"; };
		2557EDEB2744A2550092C2BA /* IQKeyboardManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IQKeyboardManager.m; sourceTree = "<group>"; };
		2557EDED2744A2550092C2BA /* IQNSArray+Sort.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "IQNSArray+Sort.m"; sourceTree = "<group>"; };
		2557EDEE2744A2550092C2BA /* IQUIViewController+Additions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "IQUIViewController+Additions.h"; sourceTree = "<group>"; };
		2557EDEF2744A2550092C2BA /* IQUITextFieldView+Additions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "IQUITextFieldView+Additions.m"; sourceTree = "<group>"; };
		2557EDF02744A2550092C2BA /* IQUIScrollView+Additions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "IQUIScrollView+Additions.m"; sourceTree = "<group>"; };
		2557EDF12744A2550092C2BA /* IQUIView+Hierarchy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "IQUIView+Hierarchy.m"; sourceTree = "<group>"; };
		2557EDF22744A2550092C2BA /* IQNSArray+Sort.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "IQNSArray+Sort.h"; sourceTree = "<group>"; };
		2557EDF32744A2550092C2BA /* IQUIScrollView+Additions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "IQUIScrollView+Additions.h"; sourceTree = "<group>"; };
		2557EDF42744A2550092C2BA /* IQUITextFieldView+Additions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "IQUITextFieldView+Additions.h"; sourceTree = "<group>"; };
		2557EDF52744A2550092C2BA /* IQUIViewController+Additions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "IQUIViewController+Additions.m"; sourceTree = "<group>"; };
		2557EDF62744A2550092C2BA /* IQUIView+Hierarchy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "IQUIView+Hierarchy.h"; sourceTree = "<group>"; };
		2557EDF72744A2550092C2BA /* IQKeyboardReturnKeyHandler.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IQKeyboardReturnKeyHandler.m; sourceTree = "<group>"; };
		2557EE062744A2670092C2BA /* TelinkGenericOTALib.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = TelinkGenericOTALib.xcodeproj; path = ../../../TelinkGenericOTALib/TelinkGenericOTALib.xcodeproj; sourceTree = "<group>"; };
		2557EE0D27450AD80092C2BA /* 8258_demo.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = 8258_demo.bin; sourceTree = "<group>"; };
		25A45F6928FFF66F006742A7 /* SettingsVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SettingsVC.h; sourceTree = "<group>"; };
		25A45F6A28FFF66F006742A7 /* SettingsVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SettingsVC.m; sourceTree = "<group>"; };
		25A45F6E28FFF8ED006742A7 /* BaseViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BaseViewController.h; sourceTree = "<group>"; };
		25A45F6F28FFF8ED006742A7 /* BaseViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BaseViewController.m; sourceTree = "<group>"; };
		25A45F7228FFF948006742A7 /* UIImage+Extension.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+Extension.m"; sourceTree = "<group>"; };
		25A45F7328FFF948006742A7 /* UIImage+Extension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+Extension.h"; sourceTree = "<group>"; };
		25A45F7628FFFBE1006742A7 /* FilterVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FilterVC.h; sourceTree = "<group>"; };
		25A45F7728FFFBE1006742A7 /* FilterVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FilterVC.m; sourceTree = "<group>"; };
		25A45F7928FFFC87006742A7 /* UIStoryboard+extension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIStoryboard+extension.h"; sourceTree = "<group>"; };
		25A45F7A28FFFC88006742A7 /* UIStoryboard+extension.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIStoryboard+extension.m"; sourceTree = "<group>"; };
		25A5B45924C04DF50073BCB6 /* OtaExample.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = OtaExample.app; sourceTree = BUILT_PRODUCTS_DIR; };
		25A5B45C24C04DF50073BCB6 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		25A5B45D24C04DF50073BCB6 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		25A5B46624C04DF50073BCB6 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		25A5B46824C04DF90073BCB6 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		25A5B46B24C04DF90073BCB6 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		25A5B46D24C04DF90073BCB6 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		25A5B46E24C04DF90073BCB6 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		25A5B47824C04EE20073BCB6 /* 8267_module_tBase.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = 8267_module_tBase.bin; sourceTree = "<group>"; };
		25A5B47924C04EE20073BCB6 /* 8267_module_tOTA.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = 8267_module_tOTA.bin; sourceTree = "<group>"; };
		25A5B47C24C04EF70073BCB6 /* OtaViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OtaViewController.m; sourceTree = "<group>"; };
		25A5B47F24C04EF70073BCB6 /* OtaViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OtaViewController.h; sourceTree = "<group>"; };
		25A5B48024C04EF70073BCB6 /* DeviceCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceCell.h; sourceTree = "<group>"; };
		25A5B48124C04EF80073BCB6 /* DeviceCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceCell.m; sourceTree = "<group>"; };
		25A5B48324C04EF80073BCB6 /* ScanViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ScanViewController.m; sourceTree = "<group>"; };
		25A5B48524C04EF80073BCB6 /* ScanViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ScanViewController.h; sourceTree = "<group>"; };
		25A5B48A24C04F050073BCB6 /* ARShowTips.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ARShowTips.h; sourceTree = "<group>"; };
		25A5B48B24C04F060073BCB6 /* ARShowTipsView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ARShowTipsView.m; sourceTree = "<group>"; };
		25A5B48C24C04F060073BCB6 /* ARShowTipsView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = ARShowTipsView.xib; sourceTree = "<group>"; };
		25A5B48D24C04F060073BCB6 /* ARShowTipsView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ARShowTipsView.h; sourceTree = "<group>"; };
		25A5B48E24C04F060073BCB6 /* ARShowTips.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ARShowTips.m; sourceTree = "<group>"; };
		25C2CB0A27422E5B00132F00 /* OTASettingsVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OTASettingsVC.h; sourceTree = "<group>"; };
		25C2CB0B27422E5B00132F00 /* OTASettingsVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OTASettingsVC.m; sourceTree = "<group>"; };
		25C2CB1027422ECE00132F00 /* ChooseFileVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ChooseFileVC.h; sourceTree = "<group>"; };
		25C2CB1127422ECE00132F00 /* ChooseFileVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ChooseFileVC.m; sourceTree = "<group>"; };
		25CBC40324C828C6008A4062 /* PrefixHeader.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PrefixHeader.pch; sourceTree = "<group>"; };
		9112D9272BB2B6390084ED9F /* UIView+Toast.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+Toast.h"; sourceTree = "<group>"; };
		9112D9282BB2B6390084ED9F /* UIView+Toast.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+Toast.m"; sourceTree = "<group>"; };
		914B5E942AFDC5580007BD58 /* SwitchCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SwitchCell.h; sourceTree = "<group>"; };
		914B5E952AFDC5580007BD58 /* SwitchCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SwitchCell.m; sourceTree = "<group>"; };
		914B5E962AFDC5580007BD58 /* SwitchCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = SwitchCell.xib; sourceTree = "<group>"; };
		914B5E9A2AFDCB890007BD58 /* StringTipsVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = StringTipsVC.h; sourceTree = "<group>"; };
		914B5E9B2AFDCB890007BD58 /* StringTipsVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = StringTipsVC.m; sourceTree = "<group>"; };
		9173FCD4291A41300040F772 /* UIDevice+Extension.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIDevice+Extension.m"; sourceTree = "<group>"; };
		9173FCD5291A41300040F772 /* UIDevice+Extension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIDevice+Extension.h"; sourceTree = "<group>"; };
		918799D4292247D400643A1A /* TipsVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TipsVC.h; sourceTree = "<group>"; };
		918799D5292247D400643A1A /* TipsVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TipsVC.m; sourceTree = "<group>"; };
		91A4509B2907CBAB00D5EF76 /* UIColor+Telink.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIColor+Telink.h"; sourceTree = "<group>"; };
		91A4509D2907CBAB00D5EF76 /* UIColor+Telink.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIColor+Telink.m"; sourceTree = "<group>"; };
		91ED0CC42AFCA22100CB3868 /* 8208_dual_mouse.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = 8208_dual_mouse.bin; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		25A5B45624C04DF50073BCB6 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				91F8809E2A0A37C9000E9DB1 /* TelinkGenericOTALib.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		25147B9527CF5125009B6500 /* CustomAlert */ = {
			isa = PBXGroup;
			children = (
				25147B9A27CF5125009B6500 /* CustomAlertView.h */,
				25147B9627CF5125009B6500 /* CustomAlertView.m */,
				25147B9927CF5125009B6500 /* CustomAlert.h */,
				25147B9727CF5125009B6500 /* CustomAlert.m */,
				25147B9827CF5125009B6500 /* CustomAlert.xib */,
			);
			path = CustomAlert;
			sourceTree = "<group>";
		};
		2557EDD32744A2540092C2BA /* SDK */ = {
			isa = PBXGroup;
			children = (
				2557EE062744A2670092C2BA /* TelinkGenericOTALib.xcodeproj */,
				2557EDD42744A2540092C2BA /* IQKeyboardManager-master */,
				9112D9292BB2B6390084ED9F /* Toast */,
			);
			path = SDK;
			sourceTree = "<group>";
		};
		2557EDD42744A2540092C2BA /* IQKeyboardManager-master */ = {
			isa = PBXGroup;
			children = (
				2557EDD52744A2540092C2BA /* IQKeyboardManager */,
			);
			path = "IQKeyboardManager-master";
			sourceTree = "<group>";
		};
		2557EDD52744A2540092C2BA /* IQKeyboardManager */ = {
			isa = PBXGroup;
			children = (
				2557EDD62744A2540092C2BA /* IQTextView */,
				2557EDD92744A2550092C2BA /* IQToolbar */,
				2557EDE42744A2550092C2BA /* Constants */,
				2557EDE72744A2550092C2BA /* Resources */,
				2557EDE92744A2550092C2BA /* IQKeyboardReturnKeyHandler.h */,
				2557EDEA2744A2550092C2BA /* IQKeyboardManager.h */,
				2557EDEB2744A2550092C2BA /* IQKeyboardManager.m */,
				2557EDEC2744A2550092C2BA /* Categories */,
				2557EDF72744A2550092C2BA /* IQKeyboardReturnKeyHandler.m */,
			);
			path = IQKeyboardManager;
			sourceTree = "<group>";
		};
		2557EDD62744A2540092C2BA /* IQTextView */ = {
			isa = PBXGroup;
			children = (
				2557EDD72744A2550092C2BA /* IQTextView.m */,
				2557EDD82744A2550092C2BA /* IQTextView.h */,
			);
			path = IQTextView;
			sourceTree = "<group>";
		};
		2557EDD92744A2550092C2BA /* IQToolbar */ = {
			isa = PBXGroup;
			children = (
				2557EDDA2744A2550092C2BA /* IQUIView+IQKeyboardToolbar.h */,
				2557EDDB2744A2550092C2BA /* IQToolbar.m */,
				2557EDDC2744A2550092C2BA /* IQPreviousNextView.h */,
				2557EDDD2744A2550092C2BA /* IQTitleBarButtonItem.m */,
				2557EDDE2744A2550092C2BA /* IQBarButtonItem.m */,
				2557EDDF2744A2550092C2BA /* IQUIView+IQKeyboardToolbar.m */,
				2557EDE02744A2550092C2BA /* IQPreviousNextView.m */,
				2557EDE12744A2550092C2BA /* IQToolbar.h */,
				2557EDE22744A2550092C2BA /* IQBarButtonItem.h */,
				2557EDE32744A2550092C2BA /* IQTitleBarButtonItem.h */,
			);
			path = IQToolbar;
			sourceTree = "<group>";
		};
		2557EDE42744A2550092C2BA /* Constants */ = {
			isa = PBXGroup;
			children = (
				2557EDE52744A2550092C2BA /* IQKeyboardManagerConstantsInternal.h */,
				2557EDE62744A2550092C2BA /* IQKeyboardManagerConstants.h */,
			);
			path = Constants;
			sourceTree = "<group>";
		};
		2557EDE72744A2550092C2BA /* Resources */ = {
			isa = PBXGroup;
			children = (
				2557EDE82744A2550092C2BA /* IQKeyboardManager.bundle */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		2557EDEC2744A2550092C2BA /* Categories */ = {
			isa = PBXGroup;
			children = (
				2557EDED2744A2550092C2BA /* IQNSArray+Sort.m */,
				2557EDEE2744A2550092C2BA /* IQUIViewController+Additions.h */,
				2557EDEF2744A2550092C2BA /* IQUITextFieldView+Additions.m */,
				2557EDF02744A2550092C2BA /* IQUIScrollView+Additions.m */,
				2557EDF12744A2550092C2BA /* IQUIView+Hierarchy.m */,
				2557EDF22744A2550092C2BA /* IQNSArray+Sort.h */,
				2557EDF32744A2550092C2BA /* IQUIScrollView+Additions.h */,
				2557EDF42744A2550092C2BA /* IQUITextFieldView+Additions.h */,
				2557EDF52744A2550092C2BA /* IQUIViewController+Additions.m */,
				2557EDF62744A2550092C2BA /* IQUIView+Hierarchy.h */,
			);
			path = Categories;
			sourceTree = "<group>";
		};
		2557EE072744A2670092C2BA /* Products */ = {
			isa = PBXGroup;
			children = (
				2557EE0B2744A2670092C2BA /* TelinkGenericOTALib.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		25A45F6D28FFF8D4006742A7 /* Base */ = {
			isa = PBXGroup;
			children = (
				25147B9F27CF5295009B6500 /* BaseModel.h */,
				25147BA027CF5295009B6500 /* BaseModel.m */,
				25A45F6E28FFF8ED006742A7 /* BaseViewController.h */,
				25A45F6F28FFF8ED006742A7 /* BaseViewController.m */,
			);
			path = Base;
			sourceTree = "<group>";
		};
		25A45F7128FFF930006742A7 /* Tools */ = {
			isa = PBXGroup;
			children = (
				91A4509B2907CBAB00D5EF76 /* UIColor+Telink.h */,
				91A4509D2907CBAB00D5EF76 /* UIColor+Telink.m */,
				25A45F7328FFF948006742A7 /* UIImage+Extension.h */,
				25A45F7228FFF948006742A7 /* UIImage+Extension.m */,
				25A45F7928FFFC87006742A7 /* UIStoryboard+extension.h */,
				25A45F7A28FFFC88006742A7 /* UIStoryboard+extension.m */,
				9173FCD5291A41300040F772 /* UIDevice+Extension.h */,
				9173FCD4291A41300040F772 /* UIDevice+Extension.m */,
			);
			path = Tools;
			sourceTree = "<group>";
		};
		25A45F7528FFFBBF006742A7 /* Settings */ = {
			isa = PBXGroup;
			children = (
				25A45F6928FFF66F006742A7 /* SettingsVC.h */,
				25A45F6A28FFF66F006742A7 /* SettingsVC.m */,
				914B5E9A2AFDCB890007BD58 /* StringTipsVC.h */,
				914B5E9B2AFDCB890007BD58 /* StringTipsVC.m */,
				25A45F7628FFFBE1006742A7 /* FilterVC.h */,
				25A45F7728FFFBE1006742A7 /* FilterVC.m */,
			);
			path = Settings;
			sourceTree = "<group>";
		};
		25A5B45024C04DF50073BCB6 = {
			isa = PBXGroup;
			children = (
				25A5B45B24C04DF50073BCB6 /* OtaExample */,
				25A5B45A24C04DF50073BCB6 /* Products */,
				25A5B49924C050900073BCB6 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		25A5B45A24C04DF50073BCB6 /* Products */ = {
			isa = PBXGroup;
			children = (
				25A5B45924C04DF50073BCB6 /* OtaExample.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		25A5B45B24C04DF50073BCB6 /* OtaExample */ = {
			isa = PBXGroup;
			children = (
				2557EDD32744A2540092C2BA /* SDK */,
				25A5B49124C04F2C0073BCB6 /* Supporting Files */,
				25A5B47624C04EC80073BCB6 /* Bin Files */,
				25A5B45C24C04DF50073BCB6 /* AppDelegate.h */,
				25A5B45D24C04DF50073BCB6 /* AppDelegate.m */,
				25A5B48524C04EF80073BCB6 /* ScanViewController.h */,
				25A5B48324C04EF80073BCB6 /* ScanViewController.m */,
				25A45F7528FFFBBF006742A7 /* Settings */,
				25A5B47F24C04EF70073BCB6 /* OtaViewController.h */,
				25A5B47C24C04EF70073BCB6 /* OtaViewController.m */,
				25C2CB0A27422E5B00132F00 /* OTASettingsVC.h */,
				25C2CB0B27422E5B00132F00 /* OTASettingsVC.m */,
				25C2CB1027422ECE00132F00 /* ChooseFileVC.h */,
				25C2CB1127422ECE00132F00 /* ChooseFileVC.m */,
				918799D4292247D400643A1A /* TipsVC.h */,
				918799D5292247D400643A1A /* TipsVC.m */,
				25A5B48024C04EF70073BCB6 /* DeviceCell.h */,
				25A5B48124C04EF80073BCB6 /* DeviceCell.m */,
				914B5E942AFDC5580007BD58 /* SwitchCell.h */,
				914B5E952AFDC5580007BD58 /* SwitchCell.m */,
				914B5E962AFDC5580007BD58 /* SwitchCell.xib */,
				25A5B46524C04DF50073BCB6 /* Main.storyboard */,
				25A45F7128FFF930006742A7 /* Tools */,
				25A45F6D28FFF8D4006742A7 /* Base */,
				25A5B48A24C04F050073BCB6 /* ARShowTips.h */,
				25A5B48E24C04F060073BCB6 /* ARShowTips.m */,
				25A5B48D24C04F060073BCB6 /* ARShowTipsView.h */,
				25A5B48B24C04F060073BCB6 /* ARShowTipsView.m */,
				25A5B48C24C04F060073BCB6 /* ARShowTipsView.xib */,
				25147B9527CF5125009B6500 /* CustomAlert */,
				25A5B46824C04DF90073BCB6 /* Assets.xcassets */,
				25A5B46A24C04DF90073BCB6 /* LaunchScreen.storyboard */,
			);
			path = OtaExample;
			sourceTree = "<group>";
		};
		25A5B47624C04EC80073BCB6 /* Bin Files */ = {
			isa = PBXGroup;
			children = (
				25A5B47824C04EE20073BCB6 /* 8267_module_tBase.bin */,
				25A5B47924C04EE20073BCB6 /* 8267_module_tOTA.bin */,
				2557EE0D27450AD80092C2BA /* 8258_demo.bin */,
				91ED0CC42AFCA22100CB3868 /* 8208_dual_mouse.bin */,
			);
			path = "Bin Files";
			sourceTree = "<group>";
		};
		25A5B49124C04F2C0073BCB6 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				25CBC40324C828C6008A4062 /* PrefixHeader.pch */,
				25A5B46E24C04DF90073BCB6 /* main.m */,
				25A5B46D24C04DF90073BCB6 /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		25A5B49924C050900073BCB6 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		9112D9292BB2B6390084ED9F /* Toast */ = {
			isa = PBXGroup;
			children = (
				9112D9272BB2B6390084ED9F /* UIView+Toast.h */,
				9112D9282BB2B6390084ED9F /* UIView+Toast.m */,
			);
			path = Toast;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		25A5B45824C04DF50073BCB6 /* OtaExample */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 25A5B47224C04DF90073BCB6 /* Build configuration list for PBXNativeTarget "OtaExample" */;
			buildPhases = (
				25A5B45524C04DF50073BCB6 /* Sources */,
				25A5B45624C04DF50073BCB6 /* Frameworks */,
				25A5B45724C04DF50073BCB6 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = OtaExample;
			productName = OtaExample;
			productReference = 25A5B45924C04DF50073BCB6 /* OtaExample.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		25A5B45124C04DF50073BCB6 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1150;
				ORGANIZATIONNAME = "梁家誌";
				TargetAttributes = {
					25A5B45824C04DF50073BCB6 = {
						CreatedOnToolsVersion = 11.5;
					};
				};
			};
			buildConfigurationList = 25A5B45424C04DF50073BCB6 /* Build configuration list for PBXProject "OtaExample" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 25A5B45024C04DF50073BCB6;
			productRefGroup = 25A5B45A24C04DF50073BCB6 /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = 2557EE072744A2670092C2BA /* Products */;
					ProjectRef = 2557EE062744A2670092C2BA /* TelinkGenericOTALib.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				25A5B45824C04DF50073BCB6 /* OtaExample */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		2557EE0B2744A2670092C2BA /* TelinkGenericOTALib.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = TelinkGenericOTALib.framework;
			remoteRef = 2557EE0A2744A2670092C2BA /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXResourcesBuildPhase section */
		25A5B45724C04DF50073BCB6 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				25A5B47B24C04EE20073BCB6 /* 8267_module_tOTA.bin in Resources */,
				25A5B49C24C052150073BCB6 /* ARShowTipsView.xib in Resources */,
				25A5B46C24C04DF90073BCB6 /* LaunchScreen.storyboard in Resources */,
				25147B9E27CF5125009B6500 /* CustomAlert.xib in Resources */,
				914B5E982AFDC5580007BD58 /* SwitchCell.xib in Resources */,
				25A5B46924C04DF90073BCB6 /* Assets.xcassets in Resources */,
				2557EDFE2744A2550092C2BA /* IQKeyboardManager.bundle in Resources */,
				25A5B47A24C04EE20073BCB6 /* 8267_module_tBase.bin in Resources */,
				91ED0CC62AFCA22100CB3868 /* 8208_dual_mouse.bin in Resources */,
				25A5B46724C04DF50073BCB6 /* Main.storyboard in Resources */,
				2557EE0E27450AD80092C2BA /* 8258_demo.bin in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		25A5B45524C04DF50073BCB6 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9112D92A2BB2B6390084ED9F /* UIView+Toast.m in Sources */,
				2557EE012744A2550092C2BA /* IQUITextFieldView+Additions.m in Sources */,
				2557EE022744A2550092C2BA /* IQUIScrollView+Additions.m in Sources */,
				918799D6292247D400643A1A /* TipsVC.m in Sources */,
				25A45F7B28FFFC88006742A7 /* UIStoryboard+extension.m in Sources */,
				2557EE042744A2550092C2BA /* IQUIViewController+Additions.m in Sources */,
				2557EDFF2744A2550092C2BA /* IQKeyboardManager.m in Sources */,
				2557EDFD2744A2550092C2BA /* IQPreviousNextView.m in Sources */,
				25A45F7028FFF8ED006742A7 /* BaseViewController.m in Sources */,
				9173FCD6291A41300040F772 /* UIDevice+Extension.m in Sources */,
				25A45F6B28FFF66F006742A7 /* SettingsVC.m in Sources */,
				2557EE052744A2550092C2BA /* IQKeyboardReturnKeyHandler.m in Sources */,
				25147B9D27CF5125009B6500 /* CustomAlert.m in Sources */,
				2557EDFA2744A2550092C2BA /* IQTitleBarButtonItem.m in Sources */,
				25A45F7828FFFBE1006742A7 /* FilterVC.m in Sources */,
				2557EDFC2744A2550092C2BA /* IQUIView+IQKeyboardToolbar.m in Sources */,
				25A5B48624C04EF80073BCB6 /* OtaViewController.m in Sources */,
				2557EE002744A2550092C2BA /* IQNSArray+Sort.m in Sources */,
				25A5B49024C04F060073BCB6 /* ARShowTipsView.m in Sources */,
				25C2CB1327422ECE00132F00 /* ChooseFileVC.m in Sources */,
				914B5E972AFDC5580007BD58 /* SwitchCell.m in Sources */,
				25A45F7428FFF948006742A7 /* UIImage+Extension.m in Sources */,
				25C2CB0D27422E5B00132F00 /* OTASettingsVC.m in Sources */,
				25A5B45E24C04DF50073BCB6 /* AppDelegate.m in Sources */,
				2557EE032744A2550092C2BA /* IQUIView+Hierarchy.m in Sources */,
				25A5B48924C04EF80073BCB6 /* DeviceCell.m in Sources */,
				2557EDFB2744A2550092C2BA /* IQBarButtonItem.m in Sources */,
				25147B9C27CF5125009B6500 /* CustomAlertView.m in Sources */,
				25147BA127CF5295009B6500 /* BaseModel.m in Sources */,
				91A4509E2907CBAB00D5EF76 /* UIColor+Telink.m in Sources */,
				25A5B46F24C04DF90073BCB6 /* main.m in Sources */,
				25A5B48724C04EF80073BCB6 /* ScanViewController.m in Sources */,
				2557EDF82744A2550092C2BA /* IQTextView.m in Sources */,
				25A5B48F24C04F060073BCB6 /* ARShowTips.m in Sources */,
				914B5E9C2AFDCB890007BD58 /* StringTipsVC.m in Sources */,
				2557EDF92744A2550092C2BA /* IQToolbar.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		25A5B46524C04DF50073BCB6 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				25A5B46624C04DF50073BCB6 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		25A5B46A24C04DF90073BCB6 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				25A5B46B24C04DF90073BCB6 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		25A5B47024C04DF90073BCB6 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		25A5B47124C04DF90073BCB6 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		25A5B47324C04DF90073BCB6 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = OtaExample/OtaExample.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = J3AJAYU5N6;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SRCROOT)/OtaExample/PrefixHeader.pch";
				HEADER_SEARCH_PATHS = "\"$(SRCROOT)/../TelinkGenericOTALib/TelinkGenericOTALib\"";
				INFOPLIST_FILE = OtaExample/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = TelinkBleOTA;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.0.7;
				PRODUCT_BUNDLE_IDENTIFIER = com.telink.TelinkOTA;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTS_MACCATALYST = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		25A5B47424C04DF90073BCB6 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = OtaExample/OtaExample.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = J3AJAYU5N6;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SRCROOT)/OtaExample/PrefixHeader.pch";
				HEADER_SEARCH_PATHS = "\"$(SRCROOT)/../TelinkGenericOTALib/TelinkGenericOTALib\"";
				INFOPLIST_FILE = OtaExample/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = TelinkBleOTA;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.0.7;
				PRODUCT_BUNDLE_IDENTIFIER = com.telink.TelinkOTA;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTS_MACCATALYST = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		25A5B45424C04DF50073BCB6 /* Build configuration list for PBXProject "OtaExample" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				25A5B47024C04DF90073BCB6 /* Debug */,
				25A5B47124C04DF90073BCB6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		25A5B47224C04DF90073BCB6 /* Build configuration list for PBXNativeTarget "OtaExample" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				25A5B47324C04DF90073BCB6 /* Debug */,
				25A5B47424C04DF90073BCB6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 25A5B45124C04DF50073BCB6 /* Project object */;
}
