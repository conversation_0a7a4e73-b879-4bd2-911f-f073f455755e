## V3.2.0

### Dependency Updates

* N/A

### Bug Fixes

* Fix possible crash issues caused by discovering Bluetooth services.

### Features

* N/A

### Performance Improvements

* N/A

### BREAKING CHANGES

* N/A

### Notes

* N/A

### CodeSize

* The size of release ipa is 1.2MB.


### Dependency Updates

* N/A

### Bug Fixes

* 修复读取蓝牙服务可能导致的奔溃问题。

### Features

* N/A

### Performance Improvements

* N/A

### BREAKING CHANGES

* N/A

### Notes

* N/A



## V3.1.9

### Dependency Updates

* N/A

### Bug Fixes

* Add Toast prompt to indicate whether the phone supports ExtendedScanAndConnect.

### Features

* N/A

### Performance Improvements

* N/A

### BREAKING CHANGES

* N/A

### Notes

* N/A

### CodeSize

* The size of release ipa is 1.2MB.


### Dependency Updates

* N/A

### Bug Fixes

* 点击蓝牙扫描时新增提示UI告诉用户当前手机是否支持蓝牙扩展广播包的扫描和连接。

### Features

* N/A

### Performance Improvements

* N/A

### BREAKING CHANGES

* N/A

### Notes

* N/A



## V3.1.8

### Dependency Updates

* N/A

### Bug Fixes

* N/A

### Features

* add Bootloader function.

### Performance Improvements

* N/A

### BREAKING CHANGES

* N/A

### Notes

* N/A

### CodeSize

* N/A


### Dependency Updates

* N/A

### Bug Fixes

* N/A

### Features

* 新增Bootloader模式的OTA功能。

### Performance Improvements

* N/A

### BREAKING CHANGES

* N/A

### Notes

* N/A



## V3.1.7

### Dependency Updates

* N/A

### Bug Fixes

* add timeout action of `peripheralIsReadyToSendWriteWithoutResponse:`.

### Features

* N/A

### Performance Improvements

* N/A

### BREAKING CHANGES

* N/A

### Notes

* N/A

### CodeSize

* N/A


### Dependency Updates

* N/A

### Bug Fixes

* 给系统回调`peripheralIsReadyToSendWriteWithoutResponse:`新增超时逻辑。

### Features

* N/A

### Performance Improvements

* N/A

### BREAKING CHANGES

* N/A

### Notes

* N/A



## V3.1.6

### Dependency Updates

* N/A

### Bug Fixes

* N/A

### Features

* N/A

### Performance Improvements

* N/A

### BREAKING CHANGES

* N/A

### Notes

* N/A

### Document

* 将许可证更新为Apache license 2.0。

### CodeSize

* N/A


### Dependency Updates

* N/A

### Bug Fixes

* N/A

### Features

* N/A

### Performance Improvements

* N/A

### BREAKING CHANGES

* N/A

### Notes

* N/A

### Document

* update license to Apache License 2.0.



## V3.1.5

### Dependency Updates

* N/A

### Bug Fixes

* N/A

### Features

* add filter conditions in the scanning device list interface.

### Performance Improvements

* N/A

### BREAKING CHANGES

* N/A

### Notes

* N/A

### CodeSize

* N/A


### Dependency Updates

* N/A

### Bug Fixes

* N/A

### Features

* 蓝牙扫描界面新增设备过滤条件。

### Performance Improvements

* N/A

### BREAKING CHANGES

* N/A

### Notes

* N/A



## V3.1.4

### Dependency Updates

* N/A

### Bug Fixes

* fix pduLength error when app change OTA protocol from extend to legacy.

### Features

* N/A

### Performance Improvements

* N/A

### BREAKING CHANGES

* N/A

### Notes

* N/A

### CodeSize

* N/A


### Dependency Updates

* N/A

### Bug Fixes

* 修复将OTA协议配置由Extend模式切换到Legacy模式时APP的PDU长度设置异常的bug。

### Features

* N/A

### Performance Improvements

* N/A

### BREAKING CHANGES

* N/A

### Notes

* N/A



## V3.1.3

### Dependency Updates

* N/A

### Bug Fixes

* N/A

### Features

* add new opcode TelinkOtaOpcode_setFirmwareIndex = 0xFF80.

### Performance Improvements

* N/A

### BREAKING CHANGES

* N/A

### Notes

* N/A

### CodeSize

* N/A


### Dependency Updates

* N/A

### Bug Fixes

* N/A

### Features

* 新增OTA操作码TelinkOtaOpcode_setFirmwareIndex = 0xFF80。

### Performance Improvements

* N/A

### BREAKING CHANGES

* N/A

### Notes

* N/A



## V3.1.2

### Dependency Updates

* N/A

### Bug Fixes

* N/A

### Features

* add support for extend mode.

### Performance Improvements

* N/A

### BREAKING CHANGES

* N/A

### Notes

* N/A

### CodeSize

* N/A


### Dependency Updates

* N/A

### Bug Fixes

* N/A

### Features

* 新增支持Extend模式的OTA功能。

### Performance Improvements

* N/A

### BREAKING CHANGES

* N/A

### Notes

* N/A



## V3.1.1

### Dependency Updates

* N/A

### Bug Fixes

* fix the bug of get connected devices.
* fix the bug of delete connected devices.

### Features

* N/A

### Performance Improvements

* N/A

### BREAKING CHANGES

* N/A

### Notes

* N/A

### CodeSize

* N/A


### Dependency Updates

* N/A

### Bug Fixes

* 修复获取已连接设备列表异常的bug。
* 修复移除已连接设备异常的bug。

### Features

* N/A

### Performance Improvements

* N/A

### BREAKING CHANGES

* N/A

### Notes

* N/A



## V3.1.0

### Dependency Updates

* N/A

### Bug Fixes

* fix the bug of discover OTA characteristic.

### Features

* N/A

### Performance Improvements

* N/A

### BREAKING CHANGES

* N/A

### Notes

* N/A

### CodeSize

* N/A


### Dependency Updates

* N/A

### Bug Fixes

* 修复发现蓝牙OTA特征的bug。

### Features

* N/A

### Performance Improvements

* N/A

### BREAKING CHANGES

* N/A

### Notes

* N/A