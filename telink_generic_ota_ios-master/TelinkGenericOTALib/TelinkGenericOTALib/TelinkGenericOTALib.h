/********************************************************************************************************
 * @file     TelinkGenericOTALib.h
 *
 * @brief    A concise description.
 *
 * <AUTHOR> 梁家誌
 * @date     2020/7/14
 *
 * @par     Copyright (c) [2020], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#import <Foundation/Foundation.h>

//! Project version number for TelinkGenericOTALib.
FOUNDATION_EXPORT double TelinkGenericOTALibVersionNumber;

//! Project version string for TelinkGenericOTALib.
FOUNDATION_EXPORT const unsigned char TelinkGenericOTALibVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <TelinkGenericOTALib/PublicHeader.h>

/// Optimize log macros, compile log code in DEBUG mode, and do not compile log code in non DEBUG mode.
#if DEBUG
#define TelinkDebugLog(format, ...) \
NSLog((format),##__VA_ARGS__);
#else
#define TelinkDebugLog(format, ...)
#endif

#import <CoreBluetooth/CoreBluetooth.h>

#import <TelinkGenericOTALib/TelinkConst.h>
#import <TelinkGenericOTALib/TelinkDeviceModel.h>
#import <TelinkGenericOTALib/TelinkBluetoothManager.h>
#import <TelinkGenericOTALib/TelinkOtaManager.h>
#import <TelinkGenericOTALib/FileDataSource.h>
