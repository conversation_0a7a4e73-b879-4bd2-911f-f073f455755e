/********************************************************************************************************
 * @file     TelinkDeviceModel.h
 *
 * @brief    A concise description.
 *
 * <AUTHOR> 梁家誌
 * @date     2020/7/14
 *
 * @par     Copyright (c) [2020], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface TelinkDeviceModel : NSObject
/// The CBPeripheral object of BLE device.
@property (nonatomic, strong) CBPeripheral *peripheral;
/// Complete data of Bluetooth broadcast package.
@property (nonatomic, strong) NSDictionary<NSString *,id> *advertisementData;
/// RSSI of the CBPeripheral object.
@property (nonatomic, strong) NSNumber *RSSI;
/// uuid is the unique identifier of TelinkDeviceModel.
/// The value of uuid is peripheral.identifier.UUIDString.
@property (nonatomic, strong) NSString *uuid;
/// The name of the CBAdvertisementDataLocalNameKey in the broadcast package.
@property (nonatomic, strong) NSString *advName;
/// The name of the CBPeripheral.
@property (nonatomic, strong) NSString *bleName;

/// Initialize TelinkDeviceModel object.
/// @param peripheral the CBPeripheral object of BLE device.
/// @param advertisementData Complete data of Bluetooth broadcast package.
/// @returns return `nil` when initialize TelinkDeviceModel object fail.
- (instancetype)initWithPeripheral:(CBPeripheral *)peripheral advertisementData:(NSDictionary<NSString *,id> *)advertisementData RSSI:(NSNumber *)RSSI;

/// The device name displayed on the UI interface.
/// If there is a CBAdvertisementDataLocalNameKey in the broadcast package, it will be displayed.
/// If it does not exist, it will be displayed as peripheral.name
- (NSString *)showName;

@end

NS_ASSUME_NONNULL_END
