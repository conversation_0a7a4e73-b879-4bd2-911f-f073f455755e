/********************************************************************************************************
 * @file     TelinkBluetoothManager.h
 *
 * @brief    A concise description.
 *
 * <AUTHOR> 梁家誌
 * @date     2020/7/14
 *
 * @par     Copyright (c) [2020], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

#define BLE ([TelinkBluetoothManager shareCentralManager])

typedef void(^discoverTelinkDeviceModelCallBack)(TelinkDeviceModel *deviceModel);
typedef void(^peripheralResultCallBack)(CBPeripheral *peripheral,NSError * _Nullable error);
typedef void(^characteristicResultCallback)(CBPeripheral *peripheral,CBCharacteristic *characteristic,NSError * _Nullable error);
typedef void(^bleIsReadyToSendWriteWithoutResponseCallback)(CBPeripheral *peripheral);

/// GATT Service UUID
/// - seeAlso: 16-bit UUID Numbers Document.pdf  (page.19)
typedef enum : UInt16 {
    /// Generic Access
    GATTServiceGenericAccess = 0x1800,
    /// Generic Attribute
    GATTServiceGenericAttribute = 0x1801,
    /// Immediate Alert
    GATTServiceImmediateAlert = 0x1802,
    /// Link Loss
    GATTServiceLinkLoss = 0x1803,
    /// Tx Power
    GATTServiceTxPower = 0x1804,
    /// Current Time
    GATTServiceCurrentTime = 0x1805,
    /// Reference Time Update
    GATTServiceReferenceTimeUpdate = 0x1806,
    /// Next DST Change
    GATTServiceNextDSTChange = 0x1807,
    /// Glucose
    GATTServiceGlucose = 0x1808,
    /// Health Thermometer
    GATTServiceHealthThermometer = 0x1809,
    /// Device Information
    GATTServiceDeviceInformation = 0x180A,
    /// Heart Rate
    GATTServiceHeartRate = 0x180D,
    /// Phone Alert Status
    GATTServicePhoneAlertStatus = 0x180E,
    /// Battery
    GATTServiceBattery = 0x180F,
    /// Blood Pressure
    GATTServiceBloodPressure = 0x1810,
    /// Alert Notification
    GATTServiceAlertNotification = 0x1811,
    /// Human Interface Device
    GATTServiceHumanInterfaceDevice = 0x1812,
    /// Scan Parameters
    GATTServiceScanParameters = 0x1813,
    /// Running Speed And Cadence
    GATTServiceRunningSpeedAndCadence = 0x1814,
    /// Automation IO
    GATTServiceAutomationIO = 0x1815,
    /// Cycling Speed And Cadence
    GATTServiceCyclingSpeedAndCadence = 0x1816,
    /// Cycling Power
    GATTServiceCyclingPower = 0x1818,
    /// Location And Navigation
    GATTServiceLocationAndNavigation = 0x1819,
    /// Environmental Sensing
    GATTServiceEnvironmentalSensing = 0x181A,
    /// Body Composition
    GATTServiceBodyComposition = 0x181B,
    /// User Data
    GATTServiceUserData = 0x181C,
    /// Weight Scale
    GATTServiceWeightScale = 0x181D,
    /// Bond Management
    GATTServiceBondManagement = 0x181E,
    /// Continuous Glucose Monitoring
    GATTServiceContinuousGlucoseMonitoring = 0x181F,
    /// Internet Protocol Support
    GATTServiceInternetProtocolSupport = 0x1820,
    /// Indoor Positioning
    GATTServiceIndoorPositioning = 0x1821,
    /// Pulse Oximeter
    GATTServicePulseOximeter = 0x1822,
    /// HTTP Proxy
    GATTServiceHTTPProxy = 0x1823,
    /// Transport Discovery
    GATTServiceTransportDiscovery = 0x1824,
    /// Object Transfer
    GATTServiceObjectTransfer = 0x1825,
    /// Fitness Machine
    GATTServiceFitnessMachine = 0x1826,
    /// Mesh Provisioning
    GATTServiceMeshProvisioning = 0x1827,
    /// Mesh Proxy
    GATTServiceMeshProxy = 0x1828,
    /// Reconnection Configuration
    GATTServiceReconnectionConfiguration = 0x1829,
    /// Insulin Delivery
    GATTServiceInsulinDelivery = 0x183A,
    /// Binary Sensor
    GATTServiceBinarySensor = 0x183B,
    /// Emergency Configuration
    GATTServiceEmergencyConfiguration = 0x183C,
    /// Physical Activity Monitor
    GATTServicePhysicalActivityMonitor = 0x183E,
    /// AudioInput Control
    GATTServiceAudioInputControl = 0x1843,
    /// Volume Control
    GATTServiceVolumeControl = 0x1844,
    /// Volume Offset Control
    GATTServiceVolumeOffsetControl = 0x1845,
    /// Device Time
    GATTServiceDeviceTime = 0x1847,
    /// Constant Tone Extension
    GATTServiceConstantToneExtension = 0x184A,
    /// Microphone Control
    GATTServiceMicrophoneControl = 0x184D,
} GATTService;


@interface TelinkBluetoothManager : NSObject
/// Entry point to the central role. Commands should only be issued when its state is <code>CBCentralManagerStatePoweredOn</code>.
@property (nonatomic, strong) CBCentralManager *centralManager;
/// Represents a peripheral.
@property (strong, nonatomic, nullable) CBPeripheral *currentPeripheral;
/// Represents a service's characteristic.
@property (strong, nonatomic, nullable) CBCharacteristic *currentCharacteristic;
/// Callback for Bluetooth status change on mobile phones
@property (copy, nonatomic) void(^ _Nullable updateCentralStateBlock)(CBManagerState state);
/// Callback for device side notify reporting data
@property (nonatomic, copy, nullable) characteristicResultCallback didUpdateValueForCharacteristicResultBlock;
/// Callback completed using writeWithResponse mode to write data
@property (nonatomic, copy, nullable) characteristicResultCallback didWriteValueForCharacteristicResultBlock;
/// Callback for Bluetooth connection disconnection
@property (nonatomic, copy, nullable) peripheralResultCallBack didDisconnectPeripheralResultBlock;
/// IOS 11 and above systems support sending the next WriteWithoutResponse packet after this callback
@property (nonatomic,copy, nullable) bleIsReadyToSendWriteWithoutResponseCallback bluetoothIsReadyToSendWriteWithoutResponseBlock;

/**
 *  @brief  Singleton method
 *
 *  @return the default singleton instance.
 */
+ (instancetype)shareCentralManager;

/// Start scan
/// @param discoverPeripheralBlock Discovering device callbacks
- (void)startScanWithDiscoverPeripheralBlock:(discoverTelinkDeviceModelCallBack)discoverPeripheralBlock;

/// Stop scan
- (void)stopScan;

/// Connect the Bluetooth device, and if the connection is successful, the SDK will automatically call the stopScan interface internally.
/// @param peripheral the CBPeripheral object of BLE device.
/// @param timeout Time out for connecting devices
/// @param block Connection result callback. If the error is nil, the connection is successful, and if the error is not nil, the connection is abnormal.
- (void)connectPeripheral:(CBPeripheral *)peripheral timeout:(NSTimeInterval)timeout resultBlock:(peripheralResultCallBack)block;

/// Disconnect Bluetooth device
/// @param peripheral the CBPeripheral object of BLE device.
/// @param timeout Time out for disconnecting Bluetooth device connection
/// @param block The disconnection result callback shows that if the error is nil, the disconnection is successful, and if the error is not nil, the disconnection is abnormal.
- (void)cancelConnectionPeripheral:(CBPeripheral *)peripheral timeout:(NSTimeInterval)timeout resultBlock:(_Nullable peripheralResultCallBack)block;

/// Discovering a list of all Bluetooth services in Peripheral
/// @param peripheral the CBPeripheral object of BLE device.
/// @param timeout Time out for discovering a list of all Bluetooth services in Peripheral
/// @param block handle callback
- (void)discoverServicesOfPeripheral:(CBPeripheral *)peripheral timeout:(NSTimeInterval)timeout resultBlock:(peripheralResultCallBack)block;

/// Set the notify enable switch for the characteristic of the peripheral.
/// @param state notify state
/// @param peripheral the CBPeripheral object of BLE device.
/// @param characteristic the characteristic object of peripheral.
/// @param timeout timeout
/// @param block handle callback
- (void)changeNotifyToState:(BOOL)state Peripheral:(CBPeripheral *)peripheral characteristic:(CBCharacteristic *)characteristic timeout:(NSTimeInterval)timeout resultBlock:(characteristicResultCallback)block;

/// Write Bluetooth data
/// @param value Bluetooth data
/// @param peripheral the CBPeripheral object of BLE device.
/// @param characteristic the characteristic object of peripheral.
/// @param type WriteWithResponse or WriteWithoutResponse.
- (BOOL)writeValue:(NSData *)value toPeripheral:(CBPeripheral *)peripheral forCharacteristic:(CBCharacteristic *)characteristic type:(CBCharacteristicWriteType)type;

/// Write Bluetooth data available iOS11
/// @param value Bluetooth data
/// @param peripheral the CBPeripheral object of BLE device.
/// @param characteristic the characteristic object of peripheral.
/// @param type WriteWithResponse or WriteWithoutResponse.
/// @param completeHandle write finish handle
- (BOOL)writeValueAvailableIOS11:(NSData *)value toPeripheral:(CBPeripheral *)peripheral forCharacteristic:(CBCharacteristic *)characteristic type:(CBCharacteristicWriteType)type completeHandle:(bleIsReadyToSendWriteWithoutResponseCallback)completeHandle;

/// Read Bluetooth data
/// @param characteristic the characteristic object of peripheral.
/// @param peripheral the CBPeripheral object of BLE device.
- (BOOL)readCharacteristicWithCharacteristic:(CBCharacteristic *)characteristic ofPeripheral:(CBPeripheral *)peripheral;

/*!
 *  @method connectPeripheral:options:
 *
 *  @param peripheral   The <code>CBPeripheral</code> to be connected.
 *  @param options      An optional dictionary specifying connection behavior options.
 *  @param timeout 连接设备的超时时间
 *  @param block 连接结果回调，error为nil则连接成功，error不为nil则连接异常。
 *
 *  @discussion         Initiates a connection to <i>peripheral</i>. Connection attempts never time out and, depending on the outcome, will result
 *                      in a call to either {@link centralManager:didConnectPeripheral:} or {@link centralManager:didFailToConnectPeripheral:error:}.
 *                      Pending attempts are cancelled automatically upon deallocation of <i>peripheral</i>, and explicitly via {@link cancelPeripheralConnection}.
 *
 *  @see                centralManager:didConnectPeripheral:
 *  @see                centralManager:didFailToConnectPeripheral:error:
 *  @seealso            CBConnectPeripheralOptionNotifyOnConnectionKey
 *  @seealso            CBConnectPeripheralOptionNotifyOnDisconnectionKey
 *  @seealso            CBConnectPeripheralOptionNotifyOnNotificationKey
 *  @seealso            CBConnectPeripheralOptionEnableTransportBridgingKey
 *    @seealso            CBConnectPeripheralOptionRequiresANCS
 *
 */
- (void)connectPeripheral:(CBPeripheral *)peripheral options:(nullable NSDictionary<NSString *, id> *)options timeout:(NSTimeInterval)timeout resultBlock:(peripheralResultCallBack)block;

/// Discovering the Bluetooth service list for specific serviceUUIDs in Peripheral
/// @param peripheral the CBPeripheral object of BLE device.
/// @param serviceUUIDs a list of serviceUUID
/// @param timeout timeout
/// @param block handle callback
- (void)discoverServicesOfPeripheral:(CBPeripheral *)peripheral services:(nullable NSArray<CBUUID *> *)serviceUUIDs timeout:(NSTimeInterval)timeout resultBlock:(peripheralResultCallBack)block;

/// Reset parameters, including stopping all timing, stopping scanning, and stopping connections.
- (void)resetProperties;

/// Retrieve connected peripherals.
- (NSArray <CBPeripheral *>*)retrieveConnectedPeripherals;

@end

NS_ASSUME_NONNULL_END
