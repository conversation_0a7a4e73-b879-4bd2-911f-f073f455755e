/********************************************************************************************************
 * @file     TelinkDeviceModel.m
 *
 * @brief    A concise description.
 *
 * <AUTHOR> 梁家誌
 * @date     2020/7/14
 *
 * @par     Copyright (c) [2020], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#import "TelinkDeviceModel.h"

@implementation TelinkDeviceModel

/// Initialize TelinkDeviceModel object.
/// @param peripheral the CBPeripheral object of BLE device.
/// @param advertisementData Complete data of Bluetooth broadcast package.
/// @returns return `nil` when initialize TelinkDeviceModel object fail.
- (instancetype)initWithPeripheral:(CBPeripheral *)peripheral advertisementData:(NSDictionary<NSString *,id> *)advertisementData RSSI:(NSNumber *)RSSI {
    /// Use the init method of the parent class to initialize some properties of the parent class of the subclass instance.
    self = [super init];
    if (self) {
        /// Initialize self.
        _peripheral = peripheral;
        _advertisementData = advertisementData;
        _RSSI = RSSI;
        _uuid = peripheral.identifier.UUIDString;
        _advName = advertisementData[CBAdvertisementDataLocalNameKey];
        _bleName = peripheral.name;        
    }
    return self;
}

/// The device name displayed on the UI interface.
/// If there is a CBAdvertisementDataLocalNameKey in the broadcast package, it will be displayed.
/// If it does not exist, it will be displayed as peripheral.name
- (NSString *)showName {
    NSString *tem = nil;
    if (_advName && _advName.length > 0) {
        //displayed as name of CBAdvertisementDataLocalNameKey.
        tem = _advName;
    } else {
        if (_bleName && _bleName.length > 0) {
            //displayed as peripheral.name
            tem = _bleName;
        }
    }
    return tem;
}

/// Determine if the data of two TelinkDeviceModel is the same
- (BOOL)isEqual:(id)object {
    if ([object isKindOfClass:[TelinkDeviceModel class]]) {
        //uuid is the unique identifier of TelinkDeviceModel.
        return [_uuid isEqualToString:[(TelinkDeviceModel *)object uuid]];
    } else {
        //Two TelinkDeviceModel object is different.
        return NO;
    }
}

@end
