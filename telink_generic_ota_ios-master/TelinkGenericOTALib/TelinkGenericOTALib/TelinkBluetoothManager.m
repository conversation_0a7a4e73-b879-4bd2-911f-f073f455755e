/********************************************************************************************************
 * @file     TelinkBluetoothManager.m
 *
 * @brief    A concise description.
 *
 * <AUTHOR> 梁家誌
 * @date     2020/7/14
 *
 * @par     Copyright (c) [2020], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#import "TelinkBluetoothManager.h"

@interface TelinkBluetoothManager () <CBCentralManagerDelegate, CBPeripheralDelegate>
/// Timeout timer for connecting Bluetooth devices.
@property (strong, nonatomic) NSTimer *connectTimer;
/// Scan callback to Bluetooth device
@property (nonatomic, copy, nullable) discoverTelinkDeviceModelCallBack discoverPeripheralBlock;
/// Callback for connection results
@property (nonatomic, copy, nullable) peripheralResultCallBack connectResultBlock;
/// Callback for disconnected results
@property (nonatomic, copy, nullable) peripheralResultCallBack disconnectResultBlock;
/// Callback for discovering Bluetooth service results on Bluetooth devices
@property (nonatomic, copy, nullable) peripheralResultCallBack discoverServicesResultBlock;
/// Callback for modifying the notify status
@property (nonatomic, copy, nullable) characteristicResultCallback changeNotifyResultBlock;

@end

@implementation TelinkBluetoothManager

/**
 *  @brief  Singleton method
 *
 *  @return the default singleton instance.
 */
+ (instancetype)shareCentralManager {
    /// Singleton instance
    static TelinkBluetoothManager *_centralManager = nil;
    /// Note: The dispatch_once function can ensure that a certain piece
    /// of code is only executed once in the entire application life cycle!
    static dispatch_once_t token;
    dispatch_once(&token, ^{
        /// Initialize the Singleton configure parameters.
        _centralManager = [[TelinkBluetoothManager alloc] init];
    });
    return _centralManager;
}

/// Initialize
- (instancetype)init {
    /// Use the init method of the parent class to initialize some properties of the parent class of the subclass instance.
    if (self = [super init]) {
        /// Initialize self.
        dispatch_queue_t queue = dispatch_queue_create("com.telink.TelinkGenericBluetoothLib", 0);
        _centralManager = [[CBCentralManager alloc] initWithDelegate:self queue:queue];
    }
    return self;
}

/// Timeout handling for connecting Bluetooth devices
- (void)connectPeripheralTimeout {
    // cancel timer.
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(connectPeripheralTimeout) object:nil];
    });
    // connect success.
    //Determine the status of Bluetooth peripheral connection
    if (self.currentPeripheral.state == CBPeripheralStateConnected) {
        return;
    }
    // cancel connecting
    if (self.currentPeripheral) {
        [self.centralManager cancelPeripheralConnection:self.currentPeripheral];
    }
    // callback error
    if (self.connectResultBlock) {
        TelinkDebugLog(@"peripheral connect timeout.");
        NSError *error = [NSError errorWithDomain:@"peripheral connect timeout." code:-1 userInfo:nil];
        self.connectResultBlock(self.currentPeripheral,error);
    }
    // clean block
    self.connectResultBlock = nil;
}

/// Failure handling logic for connecting Bluetooth devices
- (void)connectPeripheralFail {
    // cancel timer.
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(connectPeripheralTimeout) object:nil];
    });
    // callback error
    if (self.connectResultBlock) {
        TelinkDebugLog(@"The Peripheral is fail to connect!");
        NSError *error = [NSError errorWithDomain:@"The Peripheral is fail to connect!" code:-1 userInfo:nil];
        self.connectResultBlock(self.currentPeripheral,error);
    }
    // clean block
    self.connectResultBlock = nil;
}

/// Successful processing logic for connecting Bluetooth devices
- (void)connectPeripheralFinish {
    // cancel timer.
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(connectPeripheralTimeout) object:nil];
    });
    // stop scan
    [self stopScan];
    // callback success
    if (self.connectResultBlock) {
        self.connectResultBlock(self.currentPeripheral,nil);
    }
    // clean block
    self.connectResultBlock = nil;
}

/// Timeout processing logic for disconnecting Bluetooth devices
- (void)cancelConnectPeripheralTimeout {
    // cancel timer.
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(cancelConnectPeripheralTimeout) object:nil];
    });
    // callback error
    if (self.disconnectResultBlock && self.currentPeripheral) {
        TelinkDebugLog(@"cancelConnect peripheral fail.");
        NSError *error = [NSError errorWithDomain:@"cancelConnect peripheral fail." code:-1 userInfo:nil];
        self.disconnectResultBlock(self.currentPeripheral,error);
    }
    // clean block
    self.disconnectResultBlock = nil;
}

/// Successful processing logic for disconnecting Bluetooth devices
- (void)cancelConnectPeripheralFinish {
    // cancel timer.
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(cancelConnectPeripheralTimeout) object:nil];
    });
    // callback success
    if (self.disconnectResultBlock && self.currentPeripheral) {
        self.disconnectResultBlock(self.currentPeripheral, nil);
    }
    // clean block
    self.disconnectResultBlock = nil;
}

/// Processing logic for discovering device Bluetooth service timeout
- (void)discoverServicesOfPeripheralTimeout {
    TelinkDebugLog(@"peripheral discoverServices timeout.");
    // cancel timer.
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(discoverServicesOfPeripheralTimeout) object:nil];
    });
    // callback error
    if (self.discoverServicesResultBlock) {
        NSError *error = [NSError errorWithDomain:@"peripheral discoverServices timeout." code:-1 userInfo:nil];
        self.discoverServicesResultBlock(self.currentPeripheral,error);
    }
    // clean block
    self.discoverServicesResultBlock = nil;
}

/// Processing logic for discovering device Bluetooth service success
- (void)discoverServicesFinish {
    // cancel timer.
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(discoverServicesOfPeripheralTimeout) object:nil];
    });
    // callback success
    if (self.discoverServicesResultBlock) {
        self.discoverServicesResultBlock(self.currentPeripheral, nil);
    }
    // clean block
    self.discoverServicesResultBlock = nil;
}

/// Processing logic for change device Bluetooth service notify status timeout.
- (void)openNotifyOfPeripheralTimeout {
    TelinkDebugLog(@"peripheral open notify timeout.");
    // cancel timer.
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(openNotifyOfPeripheralTimeout) object:nil];
    });
    // callback error
    if (self.changeNotifyResultBlock) {
        NSError *error = [NSError errorWithDomain:@"peripheral open notify timeout." code:-1 userInfo:nil];
        self.changeNotifyResultBlock(self.currentPeripheral,self.currentCharacteristic,error);
    }
    //    self.bluetoothOpenNotifyCallback = nil;
}

/// Processing logic for change device Bluetooth service notify status success.
- (void)openNotifyOfPeripheralFinish {
    // cancel timer.
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(openNotifyOfPeripheralTimeout) object:nil];
    });
    // callback success
    if (self.changeNotifyResultBlock) {
        self.changeNotifyResultBlock(self.currentPeripheral,self.currentCharacteristic,nil);
    }
    //    self.bluetoothOpenNotifyCallback = nil;
}

#pragma mark- CBCentralManagerDelegate  Method

/*!
 *  @method centralManagerDidUpdateState:
 *
 *  @param central  The central manager whose state has changed.
 *
 *  @discussion     Invoked whenever the central manager's state has been updated. Commands should only be issued when the state is
 *                  <code>CBCentralManagerStatePoweredOn</code>. A state below <code>CBCentralManagerStatePoweredOn</code>
 *                  implies that scanning has stopped and any connected peripherals have been disconnected. If the state moves below
 *                  <code>CBCentralManagerStatePoweredOff</code>, all <code>CBPeripheral</code> objects obtained from this central
 *                  manager become invalid and must be retrieved or discovered again.
 *
 *  @see            state
 *
 */
- (void)centralManagerDidUpdateState:(CBCentralManager *)central {
    TelinkDebugLog(@"[%@->%@]",NSStringFromClass([self class]), NSStringFromSelector(_cmd));
    // callback update state
    if (self.updateCentralStateBlock) self.updateCentralStateBlock(central.state);
}

/*!
 *  @method centralManager:didDiscoverPeripheral:advertisementData:RSSI:
 *
 *  @param central              The central manager providing this update.
 *  @param peripheral           A <code>CBPeripheral</code> object.
 *  @param advertisementData    A dictionary containing any advertisement and scan response data.
 *  @param RSSI                 The current RSSI of <i>peripheral</i>, in dBm. A value of <code>127</code> is reserved and indicates the RSSI
 *                                was not available.
 *
 *  @discussion                 This method is invoked while scanning, upon the discovery of <i>peripheral</i> by <i>central</i>. A discovered peripheral must
 *                              be retained in order to use it; otherwise, it is assumed to not be of interest and will be cleaned up by the central manager. For
 *                              a list of <i>advertisementData</i> keys, see {@link CBAdvertisementDataLocalNameKey} and other similar constants.
 *
 *  @seealso                    CBAdvertisementData.h
 *
 */
- (void)centralManager:(CBCentralManager *)central didDiscoverPeripheral:(CBPeripheral *)peripheral advertisementData:(NSDictionary<NSString *, id> *)advertisementData RSSI:(NSNumber *)RSSI {
    TelinkDeviceModel *device = [[TelinkDeviceModel alloc] initWithPeripheral:peripheral advertisementData:advertisementData RSSI:RSSI];
    // callback didDiscoverPeripheral
    if (self.discoverPeripheralBlock) {
        self.discoverPeripheralBlock(device);
    }
}

/*!
 *  @method centralManager:didConnectPeripheral:
 *
 *  @param central      The central manager providing this information.
 *  @param peripheral   The <code>CBPeripheral</code> that has connected.
 *
 *  @discussion         This method is invoked when a connection initiated by {@link connectPeripheral:options:} has succeeded.
 *
 */
- (void)centralManager:(CBCentralManager *)central didConnectPeripheral:(CBPeripheral *)peripheral {
    TelinkDebugLog(@"[%@->%@]",NSStringFromClass([self class]), NSStringFromSelector(_cmd));
    self.currentPeripheral = peripheral;
    peripheral.delegate = self;
    // handle connect success
    [self connectPeripheralFinish];
}

/*!
 *  @method centralManager:didDisconnectPeripheral:error:
 *
 *  @param central      The central manager providing this information.
 *  @param peripheral   The <code>CBPeripheral</code> that has disconnected.
 *  @param error        If an error occurred, the cause of the failure.
 *
 *  @discussion         This method is invoked upon the disconnection of a peripheral that was connected by {@link connectPeripheral:options:}. If the disconnection
 *                      was not initiated by {@link cancelPeripheralConnection}, the cause will be detailed in the <i>error</i> parameter. Once this method has been
 *                      called, no more methods will be invoked on <i>peripheral</i>'s <code>CBPeripheralDelegate</code>.
 *
 */
- (void)centralManager:(CBCentralManager *)central didDisconnectPeripheral:(CBPeripheral *)peripheral error:(nullable NSError *)error {
    TelinkDebugLog(@"[%@->%@] err=%@",NSStringFromClass([self class]), NSStringFromSelector(_cmd), error);
    // handle disconnect success
    [self cancelConnectPeripheralFinish];
    // callback disconnect
    if (self.didDisconnectPeripheralResultBlock) {
        self.didDisconnectPeripheralResultBlock(peripheral, error);
    }
}

/*!
 *  @method centralManager:didFailToConnectPeripheral:error:
 *
 *  @param central      The central manager providing this information.
 *  @param peripheral   The <code>CBPeripheral</code> that has failed to connect.
 *  @param error        The cause of the failure.
 *
 *  @discussion         This method is invoked when a connection initiated by {@link connectPeripheral:options:} has failed to complete. As connection attempts do not
 *                      timeout, the failure of a connection is atypical and usually indicative of a transient issue.
 *
 */
- (void)centralManager:(CBCentralManager *)central didFailToConnectPeripheral:(CBPeripheral *)peripheral error:(nullable NSError *)error {
    TelinkDebugLog(@"[%@->%@]",NSStringFromClass([self class]), NSStringFromSelector(_cmd));
    // handle connect fail
    [self connectPeripheralFail];
}

#pragma mark- CBPeripheralDelegate  Method

/*!
 *  @method peripheral:didDiscoverServices:
 *
 *  @param peripheral    The peripheral providing this information.
 *    @param error        If an error occurred, the cause of the failure.
 *
 *  @discussion            This method returns the result of a @link discoverServices: @/link call. If the service(s) were read successfully, they can be retrieved via
 *                        <i>peripheral</i>'s @link services @/link property.
 *
 */
- (void)peripheral:(CBPeripheral *)peripheral didDiscoverServices:(nullable NSError *)error {
    TelinkDebugLog(@"[%@->%@]",NSStringFromClass([self class]), NSStringFromSelector(_cmd));
    // discoverCharacteristics of first service.
    if (peripheral.services.count > 0) {
        [peripheral discoverCharacteristics:nil forService:peripheral.services.firstObject];
    } else {
        [self discoverServicesFinish];
    }
}

/*!
 *  @method peripheral:didDiscoverCharacteristicsForService:error:
 *
 *  @param peripheral    The peripheral providing this information.
 *  @param service        The <code>CBService</code> object containing the characteristic(s).
 *    @param error        If an error occurred, the cause of the failure.
 *
 *  @discussion            This method returns the result of a @link discoverCharacteristics:forService: @/link call. If the characteristic(s) were read successfully,
 *                        they can be retrieved via <i>service</i>'s <code>characteristics</code> property.
 */
- (void)peripheral:(CBPeripheral *)peripheral didDiscoverCharacteristicsForService:(CBService *)service error:(nullable NSError *)error {
    TelinkDebugLog(@"[%@->%@]",NSStringFromClass([self class]), NSStringFromSelector(_cmd));
    
    CBService *nextS = nil;
    BOOL getLastS = NO;
    for (CBService *se in peripheral.services) {
        if (getLastS) {
            nextS = se;
            break;
        }
        if ([service isEqual:se]) {
            getLastS = YES;
        }
    }
    if (nextS) {
        // 1.discoverCharacteristics of next service
        [peripheral discoverCharacteristics:nil forService:nextS];
    } else {
        CBCharacteristic *nextC = nil;
        for (CBService *ser in peripheral.services) {
            for (CBCharacteristic *c in ser.characteristics) {
                nextC = c;
                break;
            }
            if (nextC) {
                break;
            }
        }
        if (nextC) {
            // 2.discoverDescriptors of first characteristic
            [peripheral discoverDescriptorsForCharacteristic:nextC];
        } else {
            // 3.discoverServices success
            [self discoverServicesFinish];
        }
    }
}

/*!
 *  @method peripheral:didDiscoverDescriptorsForCharacteristic:error:
 *
 *  @param peripheral        The peripheral providing this information.
 *  @param characteristic    A <code>CBCharacteristic</code> object.
 *    @param error            If an error occurred, the cause of the failure.
 *
 *  @discussion                This method returns the result of a @link discoverDescriptorsForCharacteristic: @/link call. If the descriptors were read successfully,
 *                            they can be retrieved via <i>characteristic</i>'s <code>descriptors</code> property.
 */
- (void)peripheral:(CBPeripheral *)peripheral didDiscoverDescriptorsForCharacteristic:(CBCharacteristic *)characteristic error:(nullable NSError *)error {
    TelinkDebugLog(@"[%@->%@]",NSStringFromClass([self class]), NSStringFromSelector(_cmd));
    
    CBCharacteristic *nextC = nil;
    BOOL getLastC = NO;
    for (CBService *service in peripheral.services) {
        for (CBCharacteristic *c in service.characteristics) {
            if (getLastC) {
                nextC = c;
                break;
            }
            if ([c isEqual:characteristic]) {
                getLastC = YES;
            }
        }
        if (nextC) {
            break;
        }
    }
    if (nextC) {
        //1.discoverDescriptors of next characteristic
        [peripheral discoverDescriptorsForCharacteristic:nextC];
    } else {
        CBDescriptor *nextD = nil;
        for (CBService *se in peripheral.services) {
            for (CBCharacteristic *c in se.characteristics) {
                for (CBDescriptor *d in c.descriptors) {
                    nextD = d;
                    break;
                }
                if (nextD) {
                    break;
                }
            }
            if (nextD) {
                break;
            }
        }
        //2.readValueForDescriptor
        if (nextD) {
            //1.readValueForDescriptor of next descriptor
            [peripheral readValueForDescriptor:nextD];
        } else {
            //2.discoverServices success
            [self discoverServicesFinish];
        }
    }
}

/*!
 *  @method peripheral:didUpdateValueForDescriptor:error:
 *
 *  @param peripheral        The peripheral providing this information.
 *  @param descriptor        A <code>CBDescriptor</code> object.
 *    @param error            If an error occurred, the cause of the failure.
 *
 *  @discussion                This method returns the result of a @link readValueForDescriptor: @/link call.
 */
- (void)peripheral:(CBPeripheral *)peripheral didUpdateValueForDescriptor:(CBDescriptor *)descriptor error:(nullable NSError *)error {
    TelinkDebugLog(@"[%@->%@]",NSStringFromClass([self class]), NSStringFromSelector(_cmd));
    
    CBDescriptor *nextD = nil;
    BOOL getLastD = NO;
    for (CBService *se in peripheral.services) {
        for (CBCharacteristic *c in se.characteristics) {
            for (CBDescriptor *d in c.descriptors) {
                if (getLastD) {
                    nextD = d;
                    break;
                }
                if ([descriptor isEqual:d]) {
                    getLastD = YES;
                }
            }
            if (nextD) {
                break;
            }
        }
        if (nextD) {
            break;
        }
    }
    if (nextD) {
        //1.readValueForDescriptor of next descriptor
        [peripheral readValueForDescriptor:nextD];
    } else {
        //2.discoverServices success
        [self discoverServicesFinish];
    }
}

/*!
 *  @method peripheral:didUpdateNotificationStateForCharacteristic:error:
 *
 *  @param peripheral        The peripheral providing this information.
 *  @param characteristic    A <code>CBCharacteristic</code> object.
 *    @param error            If an error occurred, the cause of the failure.
 *
 *  @discussion                This method returns the result of a @link setNotifyValue:forCharacteristic: @/link call.
 */
- (void)peripheral:(CBPeripheral *)peripheral didUpdateNotificationStateForCharacteristic:(CBCharacteristic *)characteristic error:(nullable NSError *)error {
    TelinkDebugLog(@"[%@->%@]",NSStringFromClass([self class]), NSStringFromSelector(_cmd));
    // handle change characteristic notify
    [self openNotifyOfPeripheralFinish];
}

/*!
 *  @method peripheral:didUpdateValueForCharacteristic:error:
 *
 *  @param peripheral        The peripheral providing this information.
 *  @param characteristic    A <code>CBCharacteristic</code> object.
 *    @param error            If an error occurred, the cause of the failure.
 *
 *  @discussion                This method is invoked after a @link readValueForCharacteristic: @/link call, or upon receipt of a notification/indication.
 */
- (void)peripheral:(CBPeripheral *)peripheral didUpdateValueForCharacteristic:(CBCharacteristic *)characteristic error:(nullable NSError *)error {
//    TelinkDebugLog(@"[%@->%@]",NSStringFromClass([self class]), NSStringFromSelector(_cmd));
    // callback block
    if (self.didUpdateValueForCharacteristicResultBlock) {
        self.didUpdateValueForCharacteristicResultBlock(peripheral, characteristic, error);
    }
}

/*!
 *  @method peripheral:didWriteValueForCharacteristic:error:
 *
 *  @param peripheral        The peripheral providing this information.
 *  @param characteristic    A <code>CBCharacteristic</code> object.
 *    @param error            If an error occurred, the cause of the failure.
 *
 *  @discussion                This method returns the result of a {@link writeValue:forCharacteristic:type:} call, when the <code>CBCharacteristicWriteWithResponse</code> type is used.
 */
 - (void)peripheral:(CBPeripheral *)peripheral didWriteValueForCharacteristic:(CBCharacteristic *)characteristic error:(nullable NSError *)error {
    TelinkDebugLog(@"[%@->%@]",NSStringFromClass([self class]), NSStringFromSelector(_cmd));
    
    if (error) {
        TelinkDebugLog(@"error.localizedDescription = %@",error.localizedDescription);
    }
     // callback block
    if (self.didWriteValueForCharacteristicResultBlock) {
        self.didWriteValueForCharacteristicResultBlock(peripheral, characteristic, error);
    }
}

/// since iOS 11.0
/*!
 *  @method peripheralIsReadyToSendWriteWithoutResponse:
 *
 *  @param peripheral   The peripheral providing this update.
 *
 *  @discussion         This method is invoked after a failed call to @link writeValue:forCharacteristic:type: @/link, when <i>peripheral</i> is again
 *                      ready to send characteristic value updates.
 *
 */
- (void)peripheralIsReadyToSendWriteWithoutResponse:(CBPeripheral *)peripheral {
//    TelinkDebugLog(@"[%@->%@]",NSStringFromClass([self class]), NSStringFromSelector(_cmd));
    // cancel timer
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(writeValueAvailableIOS11TimeoutAction) object:nil];
    });
    // callback block
    if (self.bluetoothIsReadyToSendWriteWithoutResponseBlock) {
        self.bluetoothIsReadyToSendWriteWithoutResponseBlock(peripheral);
    }
}

/// Processing logic for write value availableIOS11 timeout.
- (void)writeValueAvailableIOS11TimeoutAction {
    // callback block
    if (self.bluetoothIsReadyToSendWriteWithoutResponseBlock) {
        self.bluetoothIsReadyToSendWriteWithoutResponseBlock(self.currentPeripheral);
    }
}

#pragma mark- Public

/// Start scan
/// @param discoverPeripheralBlock Discovering device callbacks
- (void)startScanWithDiscoverPeripheralBlock:(discoverTelinkDeviceModelCallBack)discoverPeripheralBlock {
    //set block
    self.discoverPeripheralBlock = discoverPeripheralBlock;
    //Determine the status of Bluetooth
    if (self.centralManager.state == CBCentralManagerStatePoweredOn) {
        //scanForPeripheralsWithServices
        [self.centralManager scanForPeripheralsWithServices:nil options:@{CBCentralManagerScanOptionAllowDuplicatesKey:@(YES)}];
    }
}

/// Stop scan
- (void)stopScan {
    [self.centralManager stopScan];
}

/// Connect the Bluetooth device, and if the connection is successful, the SDK will automatically call the stopScan interface internally.
/// @param peripheral the CBPeripheral object of BLE device.
/// @param timeout Time out for connecting devices
/// @param block Connection result callback. If the error is nil, the connection is successful, and if the error is not nil, the connection is abnormal.
- (void)connectPeripheral:(CBPeripheral *)peripheral timeout:(NSTimeInterval)timeout resultBlock:(peripheralResultCallBack)block {
    [self connectPeripheral:peripheral options:nil timeout:timeout resultBlock:block];
}

/*!
 *  @method connectPeripheral:options:
 *
 *  @param peripheral   The <code>CBPeripheral</code> to be connected.
 *  @param options      An optional dictionary specifying connection behavior options.
 *  @param timeout 连接设备的超时时间
 *  @param block 连接结果回调，error为nil则连接成功，error不为nil则连接异常。
 *
 *  @discussion         Initiates a connection to <i>peripheral</i>. Connection attempts never time out and, depending on the outcome, will result
 *                      in a call to either {@link centralManager:didConnectPeripheral:} or {@link centralManager:didFailToConnectPeripheral:error:}.
 *                      Pending attempts are cancelled automatically upon deallocation of <i>peripheral</i>, and explicitly via {@link cancelPeripheralConnection}.
 *
 *  @see                centralManager:didConnectPeripheral:
 *  @see                centralManager:didFailToConnectPeripheral:error:
 *  @seealso            CBConnectPeripheralOptionNotifyOnConnectionKey
 *  @seealso            CBConnectPeripheralOptionNotifyOnDisconnectionKey
 *  @seealso            CBConnectPeripheralOptionNotifyOnNotificationKey
 *  @seealso            CBConnectPeripheralOptionEnableTransportBridgingKey
 *    @seealso            CBConnectPeripheralOptionRequiresANCS
 *
 */
- (void)connectPeripheral:(CBPeripheral *)peripheral options:(nullable NSDictionary<NSString *, id> *)options timeout:(NSTimeInterval)timeout resultBlock:(peripheralResultCallBack)block {
    //Determine the status of Bluetooth
    if (self.centralManager.state != CBCentralManagerStatePoweredOn) {
        TelinkDebugLog(@"Bluetooth is not power on.");
        //handle block
        if (block) {
            NSError *error = [NSError errorWithDomain:[NSString stringWithFormat:@"Bluetooth is not power on. centralManager.state=%ld",(long)self.centralManager.state] code:-1 userInfo:nil];
            block(peripheral,error);
        }
        return;
    }
    //Determine the status of Bluetooth peripheral connection
    if (peripheral.state == CBPeripheralStateConnected) {
        //handle block
        if (block) {
            block(peripheral,nil);
        }
        return;
    }
    //set connectResultBlock
    self.connectResultBlock = block;
    //set currentPeripheral
    self.currentPeripheral = peripheral;
    TelinkDebugLog(@"call system connectPeripheral: uuid=%@",peripheral.identifier.UUIDString);
    __weak typeof(self) weakSelf = self;
    //set a timer
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSObject cancelPreviousPerformRequestsWithTarget:weakSelf selector:@selector(connectPeripheralTimeout) object:nil];
        [weakSelf performSelector:@selector(connectPeripheralTimeout) withObject:nil afterDelay:timeout];
    });
    //connectPeripheral
    [self.centralManager connectPeripheral:peripheral options:options];
}

/// Disconnect Bluetooth device
/// @param peripheral the CBPeripheral object of BLE device.
/// @param timeout Time out for disconnecting Bluetooth device connection
/// @param block The disconnection result callback shows that if the error is nil, the disconnection is successful, and if the error is not nil, the disconnection is abnormal.
- (void)cancelConnectionPeripheral:(CBPeripheral *)peripheral timeout:(NSTimeInterval)timeout resultBlock:(_Nullable peripheralResultCallBack)block {
    self.disconnectResultBlock = block;
    //Determine the status of Bluetooth peripheral connection
    if (peripheral && peripheral.state != CBPeripheralStateDisconnected) {
        TelinkDebugLog(@"cancel single connection");
        //currentPeripheral
        self.currentPeripheral = peripheral;
        //delegate
        self.currentPeripheral.delegate = self;
        __weak typeof(self) weakSelf = self;
        //set a timer
        dispatch_async(dispatch_get_main_queue(), ^{
            [NSObject cancelPreviousPerformRequestsWithTarget:weakSelf selector:@selector(cancelConnectPeripheralTimeout) object:nil];
            [weakSelf performSelector:@selector(cancelConnectPeripheralTimeout) withObject:nil afterDelay:timeout];
        });
        //cancelPeripheralConnection
        [self.centralManager cancelPeripheralConnection:peripheral];
    }else{
        //Determine the status of Bluetooth peripheral connection
        if (peripheral.state == CBPeripheralStateDisconnected) {
            //handle block
            if (self.disconnectResultBlock) {
                self.disconnectResultBlock(peripheral,nil);
            }
            //clean block
            self.disconnectResultBlock = nil;
        }
    }
    
}

/// Discovering a list of all Bluetooth services in Peripheral
/// @param peripheral the CBPeripheral object of BLE device.
/// @param timeout Time out for discovering a list of all Bluetooth services in Peripheral
/// @param block handle callback
- (void)discoverServicesOfPeripheral:(CBPeripheral *)peripheral timeout:(NSTimeInterval)timeout resultBlock:(peripheralResultCallBack)block {
    [self discoverServicesOfPeripheral:peripheral services:nil timeout:timeout resultBlock:block];
    
}

/// Discovering the Bluetooth service list for specific serviceUUIDs in Peripheral
/// @param peripheral the CBPeripheral object of BLE device.
/// @param serviceUUIDs a list of serviceUUID
/// @param timeout timeout
/// @param block handle callback
- (void)discoverServicesOfPeripheral:(CBPeripheral *)peripheral services:(nullable NSArray<CBUUID *> *)serviceUUIDs timeout:(NSTimeInterval)timeout resultBlock:(peripheralResultCallBack)block {
    //Determine the status of Bluetooth
    if (self.centralManager.state != CBCentralManagerStatePoweredOn) {
        TelinkDebugLog(@"Bluetooth is not power on.");
        //handle block
        if (block) {
            NSError *error = [NSError errorWithDomain:@"Bluetooth is not power on." code:-1 userInfo:nil];
            block(peripheral,error);
        }
        return;
    }
    //Determine the status of Bluetooth peripheral connection
    if (peripheral.state != CBPeripheralStateConnected) {
        TelinkDebugLog(@"peripheral is not connected.");
        //handle block
        if (block) {
            NSError *error = [NSError errorWithDomain:@"peripheral is not connected." code:-1 userInfo:nil];
            block(peripheral,error);
        }
        return;
    }
    //set block
    self.discoverServicesResultBlock = block;
    //currentPeripheral
    self.currentPeripheral = peripheral;
    //delegate
    self.currentPeripheral.delegate = self;
    __weak typeof(self) weakSelf = self;
    //set a timer
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSObject cancelPreviousPerformRequestsWithTarget:weakSelf selector:@selector(discoverServicesOfPeripheralTimeout) object:nil];
        [weakSelf performSelector:@selector(discoverServicesOfPeripheralTimeout) withObject:nil afterDelay:timeout];
    });
    //discoverServices
    [self.currentPeripheral discoverServices:serviceUUIDs];
    
}

/// Set the notify enable switch for the characteristic of the peripheral.
/// @param state notify state
/// @param peripheral the CBPeripheral object of BLE device.
/// @param characteristic the characteristic object of peripheral.
/// @param timeout timeout
/// @param block handle callback
- (void)changeNotifyToState:(BOOL)state Peripheral:(CBPeripheral *)peripheral characteristic:(CBCharacteristic *)characteristic timeout:(NSTimeInterval)timeout resultBlock:(characteristicResultCallback)block {
    //Determine the status of Bluetooth
    if (self.centralManager.state != CBCentralManagerStatePoweredOn) {
        TelinkDebugLog(@"Bluetooth is not power on.");
        //handle block
        if (block) {
            NSError *error = [NSError errorWithDomain:@"Bluetooth is not power on." code:-1 userInfo:nil];
            block(peripheral, characteristic, error);
        }
        return;
    }
    //Determine the status of Bluetooth peripheral connection
    if (peripheral.state != CBPeripheralStateConnected) {
        TelinkDebugLog(@"peripheral is not connected.");
        //handle block
        if (block) {
            NSError *error = [NSError errorWithDomain:@"peripheral is not connected." code:-1 userInfo:nil];
            block(peripheral, characteristic, error);
        }
        return;
    }
    //set block
    self.changeNotifyResultBlock = block;
    //currentPeripheral
    self.currentPeripheral = peripheral;
    //currentCharacteristic
    self.currentCharacteristic = characteristic;
    //delegate
    self.currentPeripheral.delegate = self;
    __weak typeof(self) weakSelf = self;
    //set a timer
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSObject cancelPreviousPerformRequestsWithTarget:weakSelf selector:@selector(openNotifyOfPeripheralTimeout) object:nil];
        [weakSelf performSelector:@selector(openNotifyOfPeripheralTimeout) withObject:nil afterDelay:timeout];
    });
    //setNotifyValue
    [peripheral setNotifyValue:state forCharacteristic:characteristic];
}

/// Write Bluetooth data
/// @param value Bluetooth data
/// @param peripheral the CBPeripheral object of BLE device.
/// @param characteristic the characteristic object of peripheral.
/// @param type WriteWithResponse or WriteWithoutResponse.
- (BOOL)writeValue:(NSData *)value toPeripheral:(CBPeripheral *)peripheral forCharacteristic:(CBCharacteristic *)characteristic type:(CBCharacteristicWriteType)type {
    //Determine the status of Bluetooth
    if (self.centralManager.state != CBCentralManagerStatePoweredOn) {
        TelinkDebugLog(@"Bluetooth is not power on.");
        return NO;
    }
    //Determine the status of Bluetooth peripheral connection
    if (peripheral.state != CBPeripheralStateConnected) {
        TelinkDebugLog(@"peripheral is not CBPeripheralStateConnected, can't write.");
        return NO;
    }
    //currentPeripheral
    self.currentPeripheral = peripheral;
    //delegate
    self.currentPeripheral.delegate = self;
    //writeValue
    [self.currentPeripheral writeValue:value forCharacteristic:characteristic type:type];
    return YES;
}

/// Write Bluetooth data available iOS11
/// @param value Bluetooth data
/// @param peripheral the CBPeripheral object of BLE device.
/// @param characteristic the characteristic object of peripheral.
/// @param type WriteWithResponse or WriteWithoutResponse.
/// @param completeHandle write finish handle
- (BOOL)writeValueAvailableIOS11:(NSData *)value toPeripheral:(CBPeripheral *)peripheral forCharacteristic:(CBCharacteristic *)characteristic type:(CBCharacteristicWriteType)type completeHandle:(bleIsReadyToSendWriteWithoutResponseCallback)completeHandle {
    //Determine the status of Bluetooth
    //Determine the status of Bluetooth peripheral connection
    if (self.centralManager.state == CBCentralManagerStatePoweredOn && peripheral.state != CBPeripheralStateConnected) {
        //set a timer
        dispatch_async(dispatch_get_main_queue(), ^{
            [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(writeValueAvailableIOS11TimeoutAction) object:nil];
            [self performSelector:@selector(writeValueAvailableIOS11TimeoutAction) withObject:nil afterDelay:0.5];
        });
    }
    //set block
    self.bluetoothIsReadyToSendWriteWithoutResponseBlock = completeHandle;
    //writeValue
    return [self writeValue:value toPeripheral:peripheral forCharacteristic:characteristic type:type];
}

/// Read Bluetooth data
/// @param characteristic the characteristic object of peripheral.
/// @param peripheral the CBPeripheral object of BLE device.
- (BOOL)readCharacteristicWithCharacteristic:(CBCharacteristic *)characteristic ofPeripheral:(CBPeripheral *)peripheral {
    //Determine the status of Bluetooth
    if (self.centralManager.state != CBCentralManagerStatePoweredOn) {
        TelinkDebugLog(@"Bluetooth is not power on.");
        return NO;
    }
    //Determine the status of Bluetooth peripheral connection
    if (peripheral.state != CBPeripheralStateConnected) {
        TelinkDebugLog(@"peripheral is not CBPeripheralStateConnected, can't write.");
        return NO;
    }
    //currentPeripheral
    self.currentPeripheral = peripheral;
    //currentCharacteristic
    self.currentCharacteristic = characteristic;
    //readValueForCharacteristic
    [self.currentPeripheral readValueForCharacteristic:self.currentCharacteristic];
    return YES;
}

/// Reset parameters, including stopping all timing, stopping scanning, and stopping connections.
- (void)resetProperties {
    //clean all delay timer.
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSObject cancelPreviousPerformRequestsWithTarget:self];
    });
    //stopScan
    [self stopScan];
    //cancelConnectionPeripheral
    [self cancelConnectionPeripheral:self.currentPeripheral timeout:10 resultBlock:nil];
    //clean currentPeripheral
    self.currentPeripheral = nil;
    //clean currentCharacteristic
    self.currentCharacteristic = nil;
}

/// Retrieve connected peripherals.
- (NSArray <CBPeripheral *>*)retrieveConnectedPeripherals {
    //config uuids
    NSArray *uuids = @[@(GATTServiceGenericAccess),@(GATTServiceGenericAttribute),@(GATTServiceImmediateAlert),@(GATTServiceLinkLoss),@(GATTServiceTxPower),@(GATTServiceCurrentTime),@(GATTServiceReferenceTimeUpdate),@(GATTServiceNextDSTChange),@(GATTServiceGlucose),@(GATTServiceHealthThermometer),@(GATTServiceDeviceInformation),@(GATTServiceHeartRate),@(GATTServicePhoneAlertStatus),@(GATTServiceBattery),@(GATTServiceBloodPressure),@(GATTServiceAlertNotification),@(GATTServiceHumanInterfaceDevice),@(GATTServiceScanParameters),@(GATTServiceRunningSpeedAndCadence),@(GATTServiceAutomationIO),@(GATTServiceCyclingSpeedAndCadence),@(GATTServiceCyclingPower),@(GATTServiceLocationAndNavigation),@(GATTServiceEnvironmentalSensing),@(GATTServiceBodyComposition),@(GATTServiceUserData),@(GATTServiceWeightScale),@(GATTServiceBondManagement),@(GATTServiceContinuousGlucoseMonitoring),@(GATTServiceInternetProtocolSupport),@(GATTServiceIndoorPositioning),@(GATTServicePulseOximeter),@(GATTServiceHTTPProxy),@(GATTServiceTransportDiscovery),@(GATTServiceObjectTransfer),@(GATTServiceFitnessMachine),@(GATTServiceMeshProvisioning),@(GATTServiceMeshProxy),@(GATTServiceReconnectionConfiguration),@(GATTServiceInsulinDelivery),@(GATTServiceBinarySensor),@(GATTServiceEmergencyConfiguration),@(GATTServicePhysicalActivityMonitor),@(GATTServiceAudioInputControl),@(GATTServiceVolumeControl),@(GATTServiceVolumeOffsetControl),@(GATTServiceDeviceTime),@(GATTServiceConstantToneExtension),@(GATTServiceMicrophoneControl)];
    NSMutableArray *cbuuids = [NSMutableArray array];
    for (NSNumber *uuidNumber in uuids) {
        //change NSNumber to CBUUID.
        [cbuuids addObject:[CBUUID UUIDWithString:[NSString stringWithFormat:@"%X",uuidNumber.intValue]]];
    }
    /// Retrieves all peripherals that are connected to the system and implement any of the services listed in <i>serviceUUIDs</i>.
    /// Note that this set can include peripherals which were connected by other applications,
    /// which will need to be connected locally via {@link connectPeripheral:options:} before they can be used.
    NSArray *temArray = [self.centralManager retrieveConnectedPeripheralsWithServices:cbuuids];
    return temArray;
}

@end
