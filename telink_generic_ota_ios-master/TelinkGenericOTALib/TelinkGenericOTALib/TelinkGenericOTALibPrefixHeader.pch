/********************************************************************************************************
 * @file     TelinkGenericOTALibPrefixHeader.pch
 *
 * @brief    A concise description.
 *
 * <AUTHOR> 梁家誌
 * @date     2020/7/14
 *
 * @par     Copyright (c) [2020], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#ifndef TelinkGenericOTALibPrefixHeader_pch
#define TelinkGenericOTALibPrefixHeader_pch

// Include any system framework and library headers here that should be included in all compilation units.
// You will also need to set the Prefix Header build setting of one or more of your targets to reference this file.

#if DEBUG
#define TelinkDebugLog(format, ...) \
NSLog((format),##__VA_ARGS__);
#else
#define TelinkDebugLog(format, ...)
#endif

#import <CoreBluetooth/CoreBluetooth.h>

#import "TelinkConst.h"
#import "TelinkDeviceModel.h"
#import "TelinkBluetoothManager.h"
#import "TelinkOtaManager.h"
#import "FileDataSource.h"
#import <ExternalAccessory/ExternalAccessory.h>

#endif /* TelinkGenericOTALibPrefixHeader_pch */
