/********************************************************************************************************
 * @file     TelinkConst.m
 *
 * @brief    A concise description.
 *
 * <AUTHOR> 梁家誌
 * @date     2020/7/14
 *
 * @par     Copyright (c) [2020], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#import "TelinkConst.h"

#pragma mark - Const string

NSString * const kTelinkGenericOTALibVersion = @"v2.0.7";
NSString * const kOTAServiceUUID = @"00010203-0405-0607-0809-0a0b0c0d1912";
NSString * const kOTACharacteristicUUID = @"00010203-0405-0607-0809-0a0b0c0d2b12";

#pragma mark - Const int

UInt8 const kOTAWriteInterval = 6;//ms
UInt8 const kOTAReadInterval = 8;
UInt8 const kOTAReadTimeout = 5;
