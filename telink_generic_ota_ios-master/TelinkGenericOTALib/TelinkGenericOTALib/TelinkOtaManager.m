/********************************************************************************************************
 * @file     TelinkOtaManager.m
 *
 * @brief    A concise description.
 *
 * <AUTHOR> 梁家誌
 * @date     2020/7/21
 *
 * @par     Copyright (c) [2020], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#import "TelinkOtaManager.h"

@implementation OTASettingsModel

/// Initialize
- (instancetype)init {
    /// Use the init method of the parent class to initialize some properties of the parent class of the subclass instance.
    if (self = [super init]) {
        /// Initialize self.
        _serviceUuidString = nil;
        _characteristicUuidString = nil;
        if (@available(iOS 11.0, *)) {
            //The iOS system version number is greater than or equal to iOS11
            _readInterval = 0;
            _writeInterval = 0;
        } else {
            //The iOS system version number is less than iOS11
            //Send an OTA packet every 6ms, and read the OTA characteristic every 8 OTA packets sent。
            _readInterval = kOTAReadInterval;
            _writeInterval = kOTAWriteInterval;
        }
        //default filePath is nil.
        _filePath = nil;
        //default protocol is TelinkOtaProtocol_legacy.
        _protocol = TelinkOtaProtocol_legacy;
        //default versionCompare is NO.
        _versionCompare = NO;
        //default binVersion is 0x0.
        _binVersion = 0x0;
        //default pduLength is 16.
        _pduLength = 16*1;
        //default needSetFirmwareIndex is NO.
        _needSetFirmwareIndex = NO;
        //default firmwareIndex is 0.
        _firmwareIndex = 0;
        //default securityBootEnable is NO.
        _securityBootEnable = NO;
        //default securityBootFilePath is nil.
        _securityBootFilePath = nil;
    }
    return self;
}

/// Initialize OTASettingsModel object.
/// - Parameter model: The old OTASettingsModel object.
- (instancetype)initWithOTASettingsModel:(OTASettingsModel *)model {
    /// Use the init method of the parent class to initialize some properties of the parent class of the subclass instance.
    if (self = [super init]) {
        /// Initialize self.
        /// get all parameters from old OTASettingsModel.
        _serviceUuidString = model.serviceUuidString;
        _characteristicUuidString = model.characteristicUuidString;
        _readInterval = model.readInterval;
        _writeInterval = model.writeInterval;
        _filePath = model.filePath;
        _needSetFirmwareIndex = model.needSetFirmwareIndex;
        _firmwareIndex = model.firmwareIndex;
        _protocol = model.protocol;
        _versionCompare = model.versionCompare;
        _binVersion = model.binVersion;
        _pduLength = model.pduLength;
        _securityBootEnable = model.securityBootEnable;
        _securityBootFilePath = model.securityBootFilePath;
    }
    return self;
}

- (void)setPduLength:(UInt8)pduLength {
    //Check the legality of pduLength
    if (pduLength >= 16*1 && pduLength <= 16*15 && pduLength % 16 == 0) {
        //set new pduLength
        _pduLength = pduLength;
        TelinkDebugLog(@"set pduLength to %d success!",pduLength);
    }
}

/// get dictionary from OTASettingsModel object.
/// @returns return dictionary object.
- (NSDictionary *)outputSettingsDictionary {
    //init NSMutableDictionary
    NSMutableDictionary *mDict = [NSMutableDictionary dictionary];
    if (_serviceUuidString) {
        //serviceUuidString
        [mDict setValue:_serviceUuidString forKey:@"serviceUuidString"];
    }
    if (_characteristicUuidString) {
        //characteristicUuidString
        [mDict setValue:_characteristicUuidString forKey:@"characteristicUuidString"];
    }
    //readInterval
    [mDict setValue:@(_readInterval) forKey:@"readInterval"];
    //writeInterval
    [mDict setValue:@(_writeInterval) forKey:@"writeInterval"];
    if (_filePath) {
        //filePath
        [mDict setValue:_filePath forKey:@"filePath"];
    }
    //needSetFirmwareIndex
    [mDict setValue:@(_needSetFirmwareIndex) forKey:@"needSetFirmwareIndex"];
    //firmwareIndex
    [mDict setValue:@(_firmwareIndex) forKey:@"firmwareIndex"];
    //protocol
    [mDict setValue:@(_protocol) forKey:@"protocol"];
    //versionCompare
    [mDict setValue:@(_versionCompare) forKey:@"versionCompare"];
    //binVersion
    [mDict setValue:@(_binVersion) forKey:@"binVersion"];
    //pduLength
    [mDict setValue:@(_pduLength) forKey:@"pduLength"];
    //securityBootEnable
    [mDict setValue:@(_securityBootEnable) forKey:@"securityBootEnable"];
    if (_securityBootFilePath) {
        //securityBootFilePath
        [mDict setValue:_securityBootFilePath forKey:@"securityBootFilePath"];
    }
    return mDict;
}

/// Set dictionary to OTASettingsModel object.
/// @param dictionary OTASettingsModel dictionary object.
- (void)inputSettingsDictionary:(NSDictionary *)dictionary {
    if ([dictionary.allKeys containsObject:@"serviceUuidString"]) {
        //serviceUuidString
        _serviceUuidString = dictionary[@"serviceUuidString"];
    }
    if ([dictionary.allKeys containsObject:@"characteristicUuidString"]) {
        //characteristicUuidString
        _characteristicUuidString = dictionary[@"characteristicUuidString"];
    }
    if ([dictionary.allKeys containsObject:@"readInterval"]) {
        //readInterval
        _readInterval = [dictionary[@"readInterval"] intValue];
    }
    if ([dictionary.allKeys containsObject:@"writeInterval"]) {
        //writeInterval
        _writeInterval = [dictionary[@"writeInterval"] intValue];
    }
    if ([dictionary.allKeys containsObject:@"filePath"]) {
        //filePath
        _filePath = dictionary[@"filePath"];
    }
    if ([dictionary.allKeys containsObject:@"needSetFirmwareIndex"]) {
        //needSetFirmwareIndex
        _needSetFirmwareIndex = [dictionary[@"needSetFirmwareIndex"] boolValue];
    }
    if ([dictionary.allKeys containsObject:@"firmwareIndex"]) {
        //firmwareIndex
        _firmwareIndex = [dictionary[@"firmwareIndex"] intValue];
    }
    if ([dictionary.allKeys containsObject:@"protocol"]) {
        //protocol
        _protocol = [dictionary[@"protocol"] intValue];
    }
    if ([dictionary.allKeys containsObject:@"versionCompare"]) {
        //versionCompare
        _versionCompare = [dictionary[@"versionCompare"] boolValue];
    }
    if ([dictionary.allKeys containsObject:@"binVersion"]) {
        //binVersion
        _binVersion = [dictionary[@"binVersion"] intValue];
    }
    if ([dictionary.allKeys containsObject:@"pduLength"]) {
        //pduLength
        _pduLength = [dictionary[@"pduLength"] intValue];
    }
    if ([dictionary.allKeys containsObject:@"securityBootEnable"]) {
        //securityBootEnable
        _securityBootEnable = [dictionary[@"securityBootEnable"] boolValue];
    }
    if ([dictionary.allKeys containsObject:@"securityBootFilePath"]) {
        //securityBootFilePath
        _securityBootFilePath = dictionary[@"securityBootFilePath"];
    }
}

/// Get OTASettingsModel object describe string.
- (NSString *)getDetailString {
    NSString *tem = @"";
    if (_serviceUuidString) {
        //show serviceUuidString
        tem = [tem stringByAppendingFormat:@"service: %@",_serviceUuidString];
    } else {
        //show default(1912)
        tem = [tem stringByAppendingString:@"service: [use default(1912)]"];
    }
    if (_characteristicUuidString) {
        //show characteristicUuidString
        tem = [tem stringByAppendingFormat:@"\ncharacteristic: %@",_characteristicUuidString];
    } else {
        //show default(2B12)
        tem = [tem stringByAppendingString:@"\ncharacteristic: [use default(2B12)]"];
    }
    //show readInterval
    tem = [tem stringByAppendingFormat:@"\nread interval: %d(packet)",_readInterval];
    //show write interval
    tem = [tem stringByAppendingFormat:@"\nwrite interval: %d(ms)",_writeInterval];
    if (_filePath) {
        //show file path
        tem = [tem stringByAppendingFormat:@"\nfile path: %@",_filePath];
    } else {
        //show file not selected
        tem = [tem stringByAppendingString:@"\nfile path: error - file not selected"];
    }
    //show security boot
    tem = [tem stringByAppendingFormat:@"\nsecurity boot: %@",_securityBootEnable == YES ? @"true" : @"false"];
    if (_securityBootFilePath) {
        //show security boot file path
        tem = [tem stringByAppendingFormat:@"\nsecurity boot file path: %@",_securityBootFilePath];
    } else {
        //show file not selected
        tem = [tem stringByAppendingString:@"\nsecurity boot file path: error - file not selected"];
    }
    if (_needSetFirmwareIndex) {
        //show Set Firmware Index
        tem = [tem stringByAppendingFormat:@"\nSet Firmware Index: 0x%02X",_firmwareIndex];
    } else {
        //show not set Firmware Index
        tem = [tem stringByAppendingFormat:@"\nSet Firmware Index: false"];
    }
    //show protocol
    tem = [tem stringByAppendingFormat:@"\nprotocol: %@",_protocol == TelinkOtaProtocol_legacy ? @"Legacy" : @"Extend"];
    if (_protocol == TelinkOtaProtocol_extend) {
        //show version compare
        tem = [tem stringByAppendingFormat:@"\nversion compare: %@",_versionCompare == YES ? @"true" : @"false"];
        //show bin version
        tem = [tem stringByAppendingFormat:@"\nbin version: 0x%04X",_binVersion];
        //show pdu length
        tem = [tem stringByAppendingFormat:@"\npdu length: %d",_pduLength];
    }
    return tem;
}

- (NSData *)securityBootBinData {
    if (_securityBootBinData == nil) {
        //get securityBootBinData by securityBootFilePath.
        _securityBootBinData = [FileDataSource.share getDataWithLastPathComponent:_securityBootFilePath];
    }
    return _securityBootBinData;
}

@end

@interface TelinkOtaFirmWareVersionResponseModel : NSObject
/// All parameters of TelinkOtaFirmWareVersionResponseModel.
@property (strong,nonatomic) NSData *parameters;
/// versionNumber
@property (assign,nonatomic) UInt16 versionNumber;
/// versionAccept
@property (assign,nonatomic) BOOL versionAccept;
@end

@implementation TelinkOtaFirmWareVersionResponseModel

/// Initialize
- (instancetype)initWithParameters:(NSData *)parameters {
    /// Use the init method of the parent class to initialize some properties of the parent class of the subclass instance.
    if (self = [super init]) {
        /// Initialize self.
        _parameters = [NSData dataWithData:parameters];
        UInt16 tem16 = 0;
        Byte *dataByte = (Byte *)parameters.bytes;
        //get _versionNumber from parameters.
        memcpy(&tem16, dataByte, 2);
        _versionNumber = tem16;
        //get _versionAccept from parameters.
        UInt8 tem8 = 0;
        memcpy(&tem8, dataByte+2, 1);
        _versionAccept = tem8 == 0 ? NO : YES;
    }
    return self;
}
@end

@interface TelinkOtaResultModel : NSObject
/// All parameters of TelinkOtaFirmWareVersionResponseModel.
@property (strong,nonatomic) NSData *parameters;
/// result code
@property (assign,nonatomic) TelinkOtaResultCode result;
@end

@implementation TelinkOtaResultModel

/// Initialize
- (instancetype)initWithParameters:(NSData *)parameters {
    /// Use the init method of the parent class to initialize some properties of the parent class of the subclass instance.
    if (self = [super init]) {
        /// Initialize self.
        _parameters = [NSData dataWithData:parameters];
        //get _result from parameters.
        UInt8 tem8 = 0;
        Byte *dataByte = (Byte *)parameters.bytes;
        memcpy(&tem8, dataByte, 1);
        _result = tem8;
    }
    return self;
}
@end

@interface TelinkOtaManager ()
/// OTA流程及进度的回调
@property (nonatomic, copy, nullable) otaProgressCallBack otaProgressBlock;
/// OTA结果的回调
@property (nonatomic, copy, nullable) peripheralResultCallBack otaResultBlock;
/// 切换BootLoader模式的结果的回调
@property (nonatomic, copy, nullable) BackBootLoaderPeripheralCallback backBootLoaderPeripheralBlock;

/// OTA的bin文件二进制数据
@property (strong, nonatomic) NSData *otaData;
@property (nonatomic,assign) BOOL OTAing;
@property (nonatomic,assign) BOOL stopOTAFlag;
@property (nonatomic,assign) NSInteger offset;
/// 当前OTA数据包的下标Index值
@property (nonatomic,assign) NSInteger otaPackIndex;//index of current ota packet
@property (nonatomic,assign) BOOL sendFinish;
//@property (nonatomic,assign) NSTimeInterval writeOTAInterval;//interval of write ota data, default is 6ms
@property (nonatomic,assign) NSTimeInterval readTimeoutInterval;//timeout of read OTACharacteristic(write 8 packet, read one time), default is 5s.
@property (nonatomic,assign) BOOL isReading;
@property (nonatomic,assign) BOOL isReadBeforeOtaIndex0;//send OtaIndex0 pdu after read response.
@property (nonatomic, strong) NSMutableArray <TelinkDeviceModel *>*bootLoaderDeviceList;
@end

@implementation TelinkOtaManager

#pragma mark- init

/**
 *  @brief  Singleton method
 *
 *  @return the default singleton instance.
 */
+ (instancetype)share {
    /// Singleton instance
    static TelinkOtaManager *_otaManager = nil;
    /// Note: The dispatch_once function can ensure that a certain piece
    /// of code is only executed once in the entire application life cycle!
    static dispatch_once_t token;
    dispatch_once(&token, ^{
        /// Initialize the Singleton configure parameters.
        _otaManager = [[TelinkOtaManager alloc] init];
    });
    return _otaManager;
}

/// Initialize
- (instancetype)init {
    /// Use the init method of the parent class to initialize some properties of the parent class of the subclass instance.
    if (self = [super init]) {
        /// Initialize self.
        /// init OTASettingsModel
        _settings = [[OTASettingsModel alloc] init];
        //default readTimeoutInterval is 5.0
        _readTimeoutInterval = 5.0;
        //default OTAing is NO.
        _OTAing = NO;
        //default stopOTAFlag is NO.
        _stopOTAFlag = NO;
    }
    return self;
}

/**
    Start OTA
 
 @param otaData data for OTA
 @param per peripheral for OTA
 @param otaProgressAction callback with single model OTA progress
 @param otaResultAction callback when peripheral OTA finish, OTA is successful when error is nil.
 @return  true when call API success;false when call API fail.
 @note 注意：需要客户已经调用`startConnectPeripheral:`连接设备成功的前提下才可以调用该OTA方法，且不可重复调用该接口。
 The OTA method can only be called if the customer has successfully called 'startConnectPeripheral:' to connect to the device, and the interface cannot be called repeatedly.
 */
- (BOOL)startOTAWithOtaData:(NSData *)otaData peripheral:(CBPeripheral *)per otaProgressAction:(otaProgressCallBack)otaProgressAction otaResultAction:(peripheralResultCallBack)otaResultAction {
    //OTAing, can't call repeated.
    if (_OTAing) {
        TelinkDebugLog(@"OTAing, can't call repeated.");
        return NO;
    }
    //OTA data is invalid.
    if (!otaData || otaData.length == 0) {
        TelinkDebugLog(@"OTA data is invalid.");
        return NO;
    }
    //otaData
    self.otaData = otaData;
    //set otaProgressBlock
    self.otaProgressBlock = otaProgressAction;
    //set otaResultBlock
    self.otaResultBlock = otaResultAction;
    //set Peripheral
    [self setBluetoothManagerParametersWithPeripheral:per];
    //check OTA characteristic
    if (TelinkBluetoothManager.shareCentralManager.currentCharacteristic == nil) {
        return NO;
    }
    //check isNotifying
    if (TelinkBluetoothManager.shareCentralManager.currentCharacteristic.isNotifying) {
        //start OTA
        [self startOtaAction];
    } else {
        //open notify
        __weak typeof(self) weakSelf = self;
        [TelinkBluetoothManager.shareCentralManager changeNotifyToState:YES Peripheral:TelinkBluetoothManager.shareCentralManager.currentPeripheral characteristic:TelinkBluetoothManager.shareCentralManager.currentCharacteristic timeout:5.0 resultBlock:^(CBPeripheral * _Nonnull peripheral, CBCharacteristic * _Nonnull characteristic, NSError * _Nullable error) {
            [weakSelf startOtaAction];
        }];
    }
    return YES;
}

/// config parameters of peripheral
/// @param peripheral the CBPeripheral object of BLE device.
- (void)setBluetoothManagerParametersWithPeripheral:(CBPeripheral *)peripheral {
    //currentPeripheral
    TelinkBluetoothManager.shareCentralManager.currentPeripheral = peripheral;
    //clean currentCharacteristic
    TelinkBluetoothManager.shareCentralManager.currentCharacteristic = nil;
    //clean bluetoothIsReadyToSendWriteWithoutResponseBlock
    TelinkBluetoothManager.shareCentralManager.bluetoothIsReadyToSendWriteWithoutResponseBlock = nil;
    //get otaServiceUUIDString
    NSString *otaServiceUUIDString = kOTAServiceUUID;
    if (self.settings.serviceUuidString && self.settings.serviceUuidString.length > 0) {
        otaServiceUUIDString = self.settings.serviceUuidString;
    }
    //get otaCharacteristicUUIDString
    NSString *otaCharacteristicUUIDString = kOTACharacteristicUUID;
    if (self.settings.characteristicUuidString && self.settings.characteristicUuidString.length > 0) {
        otaCharacteristicUUIDString = self.settings.characteristicUuidString;
    }
    //set OTA characteristic
    for (CBService *service in peripheral.services) {
        if ([service.UUID isEqual:[CBUUID UUIDWithString:otaServiceUUIDString]]) {
            for (CBCharacteristic *c in service.characteristics) {
                if ([c.UUID isEqual:[CBUUID UUIDWithString:otaCharacteristicUUIDString]]) {
                    TelinkBluetoothManager.shareCentralManager.currentCharacteristic = c;
                    break;
                }
            }
            break;
        }
    }
}

/// 将输入设备切换到BootLoader模式，并将BootLoader模式的设备返回APP。
/// Switch the input device to BootLoader mode and return the device in BootLoader mode to the APP.
/// @param peripheral 输入设备，可以是BootLoader模式设备，也可以是非BootLoader模式设备。
/// The input device can be either a BootLoader mode device or a non BootLoader mode device.
/// @param resultHandle 结果回调，如果返回设备为nil表示没有扫描到BootLoader模式的设备。
/// Result callback, if the returned device is nil, it indicates that no device in BootLoader mode has been scanned.
- (void)switchToBootLoaderPeripheralWithPeripheral:(CBPeripheral *)peripheral resultHandle:(BackBootLoaderPeripheralCallback)resultHandle {
    //set backBootLoaderPeripheralBlock
    self.backBootLoaderPeripheralBlock = resultHandle;
    //set Peripheral
    [self setBluetoothManagerParametersWithPeripheral:peripheral];
    //不是BootLoader设备，需要切换
    [self sendOTAStart];
    //set a delay timer
    __weak typeof(self) weakSelf = self;
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(switchToBootLoaderPeripheralFinish) object:nil];
        [self performSelector:@selector(switchToBootLoaderPeripheralFinish) withObject:nil afterDelay:3.0];
    });
    self.bootLoaderDeviceList = [NSMutableArray array];
    //scan 3 seconds for discover Bootloader device.
    [TelinkBluetoothManager.shareCentralManager startScanWithDiscoverPeripheralBlock:^(TelinkDeviceModel * _Nonnull deviceModel) {
        //all Bootloader device has BootLoaderNameString.
        if ([deviceModel.advName isEqualToString:BootLoaderNameString]) {
            if ([weakSelf.bootLoaderDeviceList containsObject:deviceModel]) {
                NSInteger index = [weakSelf.bootLoaderDeviceList indexOfObject:deviceModel];
                TelinkDeviceModel *oldModel = [weakSelf.bootLoaderDeviceList objectAtIndex:index];
                if (oldModel.RSSI.intValue == 127) {
                    //旧设备的127，则无条件替换为新设备
                    [weakSelf.bootLoaderDeviceList replaceObjectAtIndex:index withObject:deviceModel];
                } else {
                    if (deviceModel.RSSI.intValue != 127 && deviceModel.RSSI.intValue > oldModel.RSSI.intValue) {
                        //新旧设备都不是127，则比较RSSI大小
                        [weakSelf.bootLoaderDeviceList replaceObjectAtIndex:index withObject:deviceModel];
                    }
                }
            } else {
                //不存在设备，则直接添加
                [weakSelf.bootLoaderDeviceList addObject:deviceModel];
            }
        }
    }];
}

/// switch To BootLoader Peripheral Finish
- (void)switchToBootLoaderPeripheralFinish {
    //stop scan
    [TelinkBluetoothManager.shareCentralManager stopScan];
    //switch fail
    if (self.bootLoaderDeviceList.count == 0) {
        if (self.backBootLoaderPeripheralBlock) {
            self.backBootLoaderPeripheralBlock(nil);
        }
    } else if (self.bootLoaderDeviceList.count == 1) {
        //switch success
        //only discover one Bootloader device
        //handle block
        if (self.backBootLoaderPeripheralBlock) {
            self.backBootLoaderPeripheralBlock(self.bootLoaderDeviceList.firstObject.peripheral);
        }
    } else {
        //switch success
        int rssi = self.bootLoaderDeviceList.firstObject.RSSI.intValue;
        if (rssi == 127) {
            rssi = -126;
        }
        //get the peripheral that RSSI is largest.
        CBPeripheral *p = self.bootLoaderDeviceList.firstObject.peripheral;
        for (int i=1; i<self.bootLoaderDeviceList.count; i++) {
            int r = self.bootLoaderDeviceList[i].RSSI.intValue;
            if (r == 127) {
                r = -126;
            }
            if (r > rssi) {
                p = self.bootLoaderDeviceList[i].peripheral;
            }
        }
        //handle block
        if (self.backBootLoaderPeripheralBlock) {
            self.backBootLoaderPeripheralBlock(p);
        }
    }
    //clean block
    self.backBootLoaderPeripheralBlock = nil;
}

/// Start OTA Action
- (void)startOtaAction {
    __weak typeof(self) weakSelf = self;
    //set DidDisconnectPeripheralResultBlock
    [TelinkBluetoothManager.shareCentralManager setDidDisconnectPeripheralResultBlock:^(CBPeripheral * _Nonnull peripheral, NSError * _Nullable error) {
        //check stopOTAFlag
        if (weakSelf.stopOTAFlag == NO) {
            //check otaResultBlock
            if (weakSelf.otaResultBlock) {
                //check peripheral
                if ([peripheral isEqual:TelinkBluetoothManager.shareCentralManager.currentPeripheral]) {
                    if (!weakSelf.sendFinish) {
                        //OTA fail!
                        [weakSelf otaFailAction];
                    } else {
                        //OTA success for legacy!
                        if (weakSelf.settings.protocol == TelinkOtaProtocol_legacy) {
                            [weakSelf otaSuccessAction];
                        }
                    }
                }
            }
        }
    }];
    //set DidUpdateValueForCharacteristicResultBlock
    [TelinkBluetoothManager.shareCentralManager setDidUpdateValueForCharacteristicResultBlock:^(CBPeripheral * _Nonnull peripheral, CBCharacteristic * _Nonnull characteristic, NSError * _Nullable error) {
        //get notify data success
        if (error == nil) {
            NSData *notifyData = characteristic.value;
            TelinkDebugLog(@"notify data = %@",notifyData);
            //clean delay timer `readTimeoutAction`
            //clean delay timer `firmwareVersionRequestTimeoutAction`
            __weak typeof(self) weakSelf = self;
            dispatch_async(dispatch_get_main_queue(), ^{
                [NSObject cancelPreviousPerformRequestsWithTarget:weakSelf selector:@selector(readTimeoutAction) object:nil];
                [NSObject cancelPreviousPerformRequestsWithTarget:weakSelf selector:@selector(firmwareVersionRequestTimeoutAction) object:nil];
            });
            //check isReading
            if (weakSelf.isReading) {
                weakSelf.isReading = NO;
                if (weakSelf.isReadBeforeOtaIndex0) {
                    //read before index=0 OTA Packet.
                    weakSelf.isReadBeforeOtaIndex0 = NO;
                    [self startSendFirmwareData];
                } else {
                    //other
                    [weakSelf sendOTAPartData];
                }
                return;
            }
            
            //get TelinkOtaOpcode of notify data.
            TelinkOtaOpcode code = [weakSelf getTelinkOtaOpcodeWithPduData:notifyData];
            if (code == TelinkOtaOpcode_otaFirmWareVersionResponse) {
                //TelinkOtaOpcode_otaFirmWareVersionResponse
                //init TelinkOtaFirmWareVersionResponseModel
                TelinkOtaFirmWareVersionResponseModel *model = [[TelinkOtaFirmWareVersionResponseModel alloc] initWithParameters:[notifyData subdataWithRange:NSMakeRange(2, notifyData.length-2)]];
                TelinkDebugLog(@"TelinkOtaFirmWareVersionResponseModel=%@",model);
                if (model.versionAccept) {
                    //check FirmWareVersion success
                    //continue OTA
                    weakSelf.isReadBeforeOtaIndex0 = YES;
                    [weakSelf sendOTAStartExtendWithOTAPacketLength:weakSelf.settings.pduLength versionCompare:weakSelf.settings.versionCompare];
                    [weakSelf readAction];
                } else {
                    //OTA fail
                    NSError *err = [NSError errorWithDomain:@"FirmWareVersionResponse, versionAccept=NO." code:-1 userInfo:nil];
                    [weakSelf otaFailActionWithError:err];
                }
            } else if (code == TelinkOtaOpcode_otaResult) {
                //TelinkOtaOpcode_otaResult
                //init TelinkOtaResultModel
                TelinkOtaResultModel *model = [[TelinkOtaResultModel alloc] initWithParameters:[notifyData subdataWithRange:NSMakeRange(2, notifyData.length-2)]];
                TelinkDebugLog(@"TelinkOtaResultModel=%@,result=%@",model,[weakSelf getResultStringOfResultCode:model.result]);
                if (model.result == TelinkOtaResultCode_success) {
                    //OTA success
                    [weakSelf otaSuccessAction];
                } else {
                    //OTA fail
                    //callback result code.
                    NSError *err = [NSError errorWithDomain:[weakSelf getResultStringOfResultCode:model.result] code:model.result userInfo:nil];
                    [weakSelf otaFailActionWithError:err];
                }
            }
        }
    }];
    //set config parameters before start OTA.
    //default OTAing is YES.
    self.OTAing = YES;
    //default otaPackIndex is -1.
    self.otaPackIndex = -1;
    //default sendFinish is NO.
    self.sendFinish = NO;
    //default stopOTAFlag is NO.
    self.stopOTAFlag = NO;
    //default offset is 0.
    self.offset = 0;
    //Send OTA Bluetooth comamnd `otaVersionGet`
    [self sendOTAVersionGet];
    if (self.settings.needSetFirmwareIndex) {
        //Send OTA Bluetooth comamnd `FirmwareIndexSet`
        [self sendFirmwareIndexSetWithIndex:self.settings.firmwareIndex];
    }
    if (self.settings.protocol == TelinkOtaProtocol_legacy) {
        //TelinkOtaProtocol_legacy
        self.isReadBeforeOtaIndex0 = YES;
        //Send OTA Bluetooth comamnd `otaStart`
        [self sendOTAStart];
        //Read OTA characteristic
        [self readAction];
    } else if (self.settings.protocol == TelinkOtaProtocol_extend) {
        //TelinkOtaProtocol_extend
        [self addFirmwareVersionRequestTimeoutTimer];
        //Send OTA Bluetooth comamnd `otaFirmWareVersionRequest`
        [self sendOTAFirmWareVersionRequestWithFirmWareVersion:self.settings.binVersion versionCompare:self.settings.versionCompare];
    }
}

- (void)startSendFirmwareData {
    if (self.settings.securityBootEnable) {
        //securityBoot mode
        [self sendOtaPublicKeyDataAndSignData];
    } else {
        //other mode
        if (@available(iOS 11.0, *)) {
            //iOS11.0及以上
            [self sendOTAPartDataAvailableIOS11];
        } else {
            //iOS11.0以下
            [self sendOTAPartData];
        }
    }
}

//10ms发送一个pdu，一共8个pdu，耗时80ms。之后是正常的bin文件发送流程。
- (void)sendOtaPublicKeyDataAndSignData {
    __weak typeof(self) weakSelf = self;
    NSOperationQueue *oprationQueue = [[NSOperationQueue alloc] init];
    [oprationQueue addOperationWithBlock:^{
        //get publicKey Data
        NSData *publicKeyData = [FileDataSource.share getPublicKeyDataWithSecurityBootBinData:self.settings.securityBootBinData];
        //get sign Data
        NSData *signData = [FileDataSource.share getSignatureDataWithSecurityBootBinData:self.settings.securityBootBinData];
        //check publicKeyData
        if (publicKeyData.length != 64) {
            //OTA fail
            NSError *err = [NSError errorWithDomain:@"Read public key of SecurityBootBin Fail!" code:-1 userInfo:nil];
            [weakSelf otaFailActionWithError:err];
            return;
        }
        //check signData
        if (signData.length != 64) {
            //OTA fail
            NSError *err = [NSError errorWithDomain:@"Read Signature data of SecurityBootBin Fail!" code:-1 userInfo:nil];
            [weakSelf otaFailActionWithError:err];
            return;
        }
        //Send publicKeyData to Bluetooth devices in 4 batches.
        for (UInt16 i=0; i<=3; i++) {
            [weakSelf sendOTAData:[publicKeyData subdataWithRange:NSMakeRange(16*i, 16)] index:0xFF10+i];
            [NSThread sleepForTimeInterval:0.01];
        }
        //Send signData to Bluetooth devices in 4 batches.
        for (UInt16 i=0; i<=3; i++) {
            [weakSelf sendOTAData:[signData subdataWithRange:NSMakeRange(16*i, 16)] index:0xFF14+i];
            [NSThread sleepForTimeInterval:0.01];
        }
        if (@available(iOS 11.0, *)) {
            //iOS11.0及以上
            [weakSelf sendOTAPartDataAvailableIOS11];
        } else {
            //iOS11.0以下
            [weakSelf sendOTAPartData];
        }
    }];
}

/// Stop OTA
- (void)stopOTA {
    //check OTAing
    if (_OTAing) {
        //reset all OTA parameters.
        //reset sendFinish to NO.
        self.sendFinish = NO;
        //reset offset to 0.
        self.offset = 0;
        //clean otaProgressBlock
        self.otaProgressBlock = nil;
        //clean otaResultBlock
        self.otaResultBlock = nil;
        //reset currentCharacteristic to nil.
        TelinkBluetoothManager.shareCentralManager.currentCharacteristic = nil;
        //reset otaData to nil.
        self.otaData = nil;
        //reset currentPeripheral to nil.
        TelinkBluetoothManager.shareCentralManager.currentPeripheral = nil;
        //clean bluetoothIsReadyToSendWriteWithoutResponseBlock
        TelinkBluetoothManager.shareCentralManager.bluetoothIsReadyToSendWriteWithoutResponseBlock = nil;
    }
}

/// OTA success handle
- (void)otaSuccessAction {
    //reset all OTA parameters.
    [self resetParameter];
    //handle otaResultBlock
    if (self.otaResultBlock) {
        self.otaResultBlock(TelinkBluetoothManager.shareCentralManager.currentPeripheral, nil);
        //clean otaResultBlock
        self.otaResultBlock = nil;
    }
}

/// OTA fail handle
- (void)otaFailAction {
    //reset all OTA parameters.
    [self resetParameter];
    //handle otaResultBlock
    if (self.otaResultBlock) {
        NSError *err = [NSError errorWithDomain:@"The Peripheral is disconnected! OTA Fail!" code:-1 userInfo:nil];
        self.otaResultBlock(TelinkBluetoothManager.shareCentralManager.currentPeripheral, err);
        //clean otaResultBlock
        self.otaResultBlock = nil;
    }
}

/// OTA fail handle with error parameter
/// - Parameter error: error of OTA fail
- (void)otaFailActionWithError:(NSError *)error {
    //reset all OTA parameters.
    [self resetParameter];
    //handle otaResultBlock
    if (self.otaResultBlock) {
        self.otaResultBlock(TelinkBluetoothManager.shareCentralManager.currentPeripheral, error);
        //clean otaResultBlock
        self.otaResultBlock = nil;
    }
}

/// reset all OTA parameters.
- (void)resetParameter {
    //reset all OTA parameters.
    //reset OTAing to NO.
    _OTAing = NO;
    //reset stopOTAFlag to YES.
    _stopOTAFlag = YES;
    //reset otaPackIndex to -1.
    self.otaPackIndex = -1;
    //clean bluetoothIsReadyToSendWriteWithoutResponseBlock
    TelinkBluetoothManager.shareCentralManager.bluetoothIsReadyToSendWriteWithoutResponseBlock = nil;
}

/// Get Result String Of Result Code
/// - Parameter resultCode: ota result code
- (NSString *)getResultStringOfResultCode:(TelinkOtaResultCode)resultCode {
    NSString *tem = @"";
    switch (resultCode) {
            //TelinkOtaResultCode_success
        case TelinkOtaResultCode_success:
            tem = @"success";
            break;
            //TelinkOtaResultCode_dataPacketSequenceError
        case TelinkOtaResultCode_dataPacketSequenceError:
            tem = @"OTA data packet sequence number error: repeated OTA PDU or lost some OTA PDU.";
            break;
            //TelinkOtaResultCode_packetInvalid
        case TelinkOtaResultCode_packetInvalid:
            tem = @"invalid OTA packet: 1. invalid OTA command; 2. addr_index out of range; 3.not standard OTA PDU length.";
            break;
            //TelinkOtaResultCode_dataCRCError
        case TelinkOtaResultCode_dataCRCError:
            tem = @"packet PDU CRC err.";
            break;
            //TelinkOtaResultCode_writeFlashError
        case TelinkOtaResultCode_writeFlashError:
            tem = @"write OTA data to flash ERR.";
            break;
            //TelinkOtaResultCode_dataUncomplete
        case TelinkOtaResultCode_dataUncomplete:
            tem = @"lost last one or more OTA PDU.";
            break;
            //TelinkOtaResultCode_flowError
        case TelinkOtaResultCode_flowError:
            tem = @"peer device send OTA command or OTA data not in correct flow.";
            break;
            //TelinkOtaResultCode_firmwareCheckError
        case TelinkOtaResultCode_firmwareCheckError:
            tem = @"firmware CRC check error.";
            break;
            //TelinkOtaResultCode_versionCompareError
        case TelinkOtaResultCode_versionCompareError:
            tem = @"the version number to be update is lower than the current version.";
            break;
            //TelinkOtaResultCode_pduLengthError
        case TelinkOtaResultCode_pduLengthError:
            tem = @"PDU length error: not 16*n, or not equal to the value it declare in \"CMD_OTA_START_EXT\" packet";
            break;
            //TelinkOtaResultCode_firmwareMarkError
        case TelinkOtaResultCode_firmwareMarkError:
            tem = @"firmware mark error: not generated by telink's BLE SDK.";
            break;
            //TelinkOtaResultCode_firmwareSizeError
        case TelinkOtaResultCode_firmwareSizeError:
            tem = @"firmware size error: no firmware_size; firmware size too small or too big.";
            break;
            //TelinkOtaResultCode_dataPacketTimeout
        case TelinkOtaResultCode_dataPacketTimeout:
            tem = @"time interval between two consequent packet exceed a value(user can adjust this value).";
            break;
            //TelinkOtaResultCode_timeout
        case TelinkOtaResultCode_timeout:
            tem = @"OTA flow total timeout.";
            break;
            //TelinkOtaResultCode_failDueToConnectionTerminate
        case TelinkOtaResultCode_failDueToConnectionTerminate:
            tem = @"OTA fail due to current connection terminate(maybe connection timeout or local/peer device terminate connection).";
            break;
            //TelinkOtaResultCode_SECBOOT_HW_ERR
        case TelinkOtaResultCode_SECBOOT_HW_ERR:
            tem = @"OTA server device hardware error.";
            break;
            //TelinkOtaResultCode_SECBOOT_SYSTEM_ERR
        case TelinkOtaResultCode_SECBOOT_SYSTEM_ERR:
            tem = @"OTA server device system error.";
            break;
            //TelinkOtaResultCode_SECBOOT_FUNC_NOT_ENABLE
        case TelinkOtaResultCode_SECBOOT_FUNC_NOT_ENABLE:
            tem = @"OTA server device do not enable secure boot function.";
            break;
            //TelinkOtaResultCode_SECBOOT_PUBKEY_SIGN_SEQ_ERR
        case TelinkOtaResultCode_SECBOOT_PUBKEY_SIGN_SEQ_ERR:
            tem = @"OTA public key & signature sequence number error: repeated or lost.";
            break;
            //TelinkOtaResultCode_SECBOOT_PUBLIC_KEY_ERR
        case TelinkOtaResultCode_SECBOOT_PUBLIC_KEY_ERR:
            tem = @"OTA client public key not match OTA server device local hash.";
            break;
            //TelinkOtaResultCode_SECBOOT_NO_SIGN_BEFORE_OTA_PDU
        case TelinkOtaResultCode_SECBOOT_NO_SIGN_BEFORE_OTA_PDU:
            tem = @"OTA client do not send Signature data before OTA firmware PDU.";
            break;
            //TelinkOtaResultCode_SECBOOT_SIGN_VERIFY_FAIL
        case TelinkOtaResultCode_SECBOOT_SIGN_VERIFY_FAIL:
            tem = @"OTA signature verification fail.";
            break;

        default:
            //other
            tem = @"undefined result code";
            break;
    }
    return tem;
}

#pragma mark send ota packets

/// Send OTA packet
- (void)sendOTAPartData {
    //check stopOTAFlag
    //check sendFinish
    if (self.stopOTAFlag || self.sendFinish) {
        return;
    }
    //check currentPeripheral
    //check currentPeripheral.state
    if (TelinkBluetoothManager.shareCentralManager.currentPeripheral && TelinkBluetoothManager.shareCentralManager.currentPeripheral.state == CBPeripheralStateConnected) {
        //calculate lastLength
        NSInteger lastLength = self.otaData.length - _offset;
        //set isReading to NO.
        self.isReading = NO;
        
        //OTA 结束包特殊处理
        if (lastLength == 0) {
            //send last OTA packet.
            [self sendOTAEndWithLastOtaIndex:self.otaPackIndex];
            //set sendFinish to YES.
            self.sendFinish = YES;
            return;
        }
        //next otaPackIndex
        self.otaPackIndex ++;
        //calculate writeLength
        NSInteger writeLength = (lastLength >= self.settings.pduLength) ? self.settings.pduLength : lastLength;
        //get writeData
        NSData *writeData = [self.otaData subdataWithRange:NSMakeRange(self.offset, writeLength)];
        //send writeData
        [self sendOTAData:writeData index:(int)self.otaPackIndex];
        //next offset
        self.offset += writeLength;
        //calculate progress
        float progress = ((float)self.offset) / self.otaData.length;
        //callback otaProgressBlock
        if (self.otaProgressBlock) {
            self.otaProgressBlock(progress);
        }
        //Check if a Read command needs to be sent
        if ((self.otaPackIndex + 1) % self.settings.readInterval == 0 && self.otaData.length != self.offset) {
            //read
            [self readAction];
            return;
        }
        //delay to send next OTA packet
        __weak typeof(self) weakSelf = self;
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf performSelector:@selector(sendOTAPartData) withObject:nil afterDelay:weakSelf.settings.writeInterval/1000.0];
        });
    }
}

/// Read OTA Characteristic
- (void)readAction {
    //set isReading to YES.
    self.isReading = YES;
    //readCharacteristicWithCharacteristic
    [TelinkBluetoothManager.shareCentralManager readCharacteristicWithCharacteristic:TelinkBluetoothManager.shareCentralManager.currentCharacteristic ofPeripheral:TelinkBluetoothManager.shareCentralManager.currentPeripheral];
    //set a dalay timer.
    __weak typeof(self) weakSelf = self;
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSObject cancelPreviousPerformRequestsWithTarget:weakSelf selector:@selector(readTimeoutAction) object:nil];
        [weakSelf performSelector:@selector(readTimeoutAction) withObject:nil afterDelay:kOTAReadTimeout];
    });
}

/// Add Firmware Version Request Timeout Timer
- (void)addFirmwareVersionRequestTimeoutTimer {
    //add timer
    __weak typeof(self) weakSelf = self;
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSObject cancelPreviousPerformRequestsWithTarget:weakSelf selector:@selector(firmwareVersionRequestTimeoutAction) object:nil];
        [weakSelf performSelector:@selector(firmwareVersionRequestTimeoutAction) withObject:nil afterDelay:kOTAReadTimeout];
    });
}

/// firmwareVersionRequestTimeoutAction
- (void)firmwareVersionRequestTimeoutAction {
    //clean timer of `firmwareVersionRequestTimeoutAction`
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(firmwareVersionRequestTimeoutAction) object:nil];
    });
    //OTA fail
    NSError *err = [NSError errorWithDomain:@"OTA fail: firmware version request timeout(please check whether the notify property exists or please check whether the device supports OTA in Extend protocol)!" code:-1 userInfo:nil];
    [self otaFailActionWithError:err];
}

/// readTimeoutAction
- (void)readTimeoutAction {
    //clean timer of `readTimeoutAction`
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(readTimeoutAction) object:nil];
    });
    //OTA fail
    NSError *err = [NSError errorWithDomain:@"Read OTA characteristic is timeout! OTA Fail!" code:-1 userInfo:nil];
    [self otaFailActionWithError:err];
}

/// Send OTA packet available iOS11
- (void)sendOTAPartDataAvailableIOS11 {
    //check stopOTAFlag
    //check sendFinish
    if (self.stopOTAFlag || self.sendFinish) {
        return;
    }
    //check currentPeripheral
    //check currentPeripheral.state
    if (TelinkBluetoothManager.shareCentralManager.currentPeripheral && TelinkBluetoothManager.shareCentralManager.currentPeripheral.state == CBPeripheralStateConnected) {
        //calculate lastLength
        NSInteger lastLength = self.otaData.length - _offset;
        //OTA 结束包特殊处理
        if (lastLength == 0) {
            //send last OTA packet.
            [self sendOTAEndWithLastOtaIndex:self.otaPackIndex];
            //set sendFinish to YES.
            self.sendFinish = YES;
            return;
        }
        //next otaPackIndex
        self.otaPackIndex ++;
        //calculate writeLength
        NSInteger writeLength = (lastLength >= self.settings.pduLength) ? self.settings.pduLength : lastLength;
        //get writeData
        NSData *writeData = [self.otaData subdataWithRange:NSMakeRange(self.offset, writeLength)];
        //next offset
        self.offset += writeLength;
        //calculate progress
        float progress = ((float)self.offset) / self.otaData.length;
        //callback otaProgressBlock
        if (self.otaProgressBlock) {
            self.otaProgressBlock(progress);
        }
        __weak typeof(self) weakSelf = self;
        //formatData
        NSData *formatData = [self getOTAFormatDataWithData:writeData index:self.otaPackIndex];
        //writeValueAvailableIOS11
        [TelinkBluetoothManager.shareCentralManager writeValueAvailableIOS11:formatData toPeripheral:TelinkBluetoothManager.shareCentralManager.currentPeripheral forCharacteristic:TelinkBluetoothManager.shareCentralManager.currentCharacteristic type:CBCharacteristicWriteWithoutResponse completeHandle:^(CBPeripheral * _Nonnull peripheral) {
            //delay to send next OTA packet
            dispatch_async(dispatch_get_main_queue(), ^{
                [weakSelf performSelector:@selector(sendOTAPartDataAvailableIOS11) withObject:nil afterDelay:weakSelf.settings.writeInterval/1000.0];
            });
        }];
    }
}

/// 发送单个OTA数据包：组成结构：2字节index + 1~16字节的有效OTA数据 + 2字节CRC数据。
/// @param data 单个数据包包含的有效OTA数据，长度为1~16.
/// @param index 数据包下标，从0开始累加，两个字节长度。即0x0000~0xFFFF。
- (void)sendOTAData:(NSData *)data index:(int)index {
    //calculate writeData
    NSData *writeData = [self getOTAFormatDataWithData:data index:index];
    //sendOtaData
    [self sendOtaData:writeData];
}

/// Get OTA Packet data
/// @param data bin data
/// @param index ota packet index
- (NSData *)getOTAFormatDataWithData:(NSData *)data index:(UInt16)index {
    //init NSMutableData
    NSMutableData *mData = [NSMutableData data];
    //init data of index
    UInt16 tem16 = index;
    NSData *temData = [NSData dataWithBytes:&tem16 length:2];
    //append data of index
    [mData appendData:temData];
    //append data of bin file
    [mData appendData:data];
    UInt8 packet16Count = ceil(data.length / 16.0);
    if (packet16Count * 16 > data.length) {
        //Use 0xFF to supplement the data length to 16 bytes
        Byte temBytes[16];
        memset(temBytes, 0xff, 16);
        temData = [NSData dataWithBytes:temBytes length:packet16Count * 16 - data.length];
        [mData appendData:temData];
    }
    Byte *tempBytes = (Byte *)[mData bytes];
    //calculate crc16 of indexData+binFileData
    UInt16 crc = crc16(tempBytes, (int)mData.length);
    temData = [NSData dataWithBytes:&crc length:2];
    //append data of crc16
    [mData appendData:temData];
    TelinkDebugLog(@"---->%@",mData);
    return mData;
}

#pragma mark send ota OpCode

/// OTA version get
- (void)sendOTAVersionGet {
    //opcode to hex data
    UInt16 tem16 = TelinkOtaOpcode_otaVersion;
    NSData *writeData = [NSData dataWithBytes:&tem16 length:2];
    //sendOtaData
    [self sendOtaData:writeData];
    TelinkDebugLog(@"send OTA Version Get - > %@", writeData);
}

/// OTA start
- (void)sendOTAStart {
    //opcode to hex data
    UInt16 tem16 = TelinkOtaOpcode_otaStart;
    NSData *writeData = [NSData dataWithBytes:&tem16 length:2];
    //sendOtaData
    [self sendOtaData:writeData];
    TelinkDebugLog(@"send OTA Start - > %@", writeData);
}

/// OTA end
/// - Parameter lastOtaIndex: The index of the last OTA packet.
- (void)sendOTAEndWithLastOtaIndex:(UInt16)lastOtaIndex {
    //init NSMutableData
    NSMutableData *writeData = [NSMutableData data];
    //opcode to hex data
    UInt16 tem16 = TelinkOtaOpcode_otaEnd;
    NSData *temData = [NSData dataWithBytes:&tem16 length:2];
    //append data of opcode
    [writeData appendData:temData];
    //lastOtaIndex to hex data
    tem16 = lastOtaIndex;
    temData = [NSData dataWithBytes:&tem16 length:2];
    //append data of lastOtaIndex
    [writeData appendData:temData];
    //~lastOtaIndex to hex data
    tem16 = ~lastOtaIndex;
    temData = [NSData dataWithBytes:&tem16 length:2];
    //append data of ~lastOtaIndex
    [writeData appendData:temData];
    //sendOtaData
    [self sendOtaData:writeData];
    TelinkDebugLog(@"send OTA End - > %@", writeData);
}

/// OTA extend start
/// - Parameters:
///   - otaPacketLength: The length of Bin file data contained in a single OTA packet.
///   - versionCompare: Tell the device side OTA if version number comparison is required.
- (void)sendOTAStartExtendWithOTAPacketLength:(UInt8)otaPacketLength versionCompare:(BOOL)versionCompare {
    //init NSMutableData
    NSMutableData *writeData = [NSMutableData data];
    //opcode to hex data
    UInt16 tem16 = TelinkOtaOpcode_otaStartExtend;
    NSData *temData = [NSData dataWithBytes:&tem16 length:2];
    //append data of opcode
    [writeData appendData:temData];
    //otaPacketLength to hex data
    UInt8 tem8 = otaPacketLength;
    temData = [NSData dataWithBytes:&tem8 length:1];
    //append data of otaPacketLength
    [writeData appendData:temData];
    //versionCompare to hex data
    tem8 = versionCompare ? 1 : 0;
    temData = [NSData dataWithBytes:&tem8 length:1];
    //append data of versionCompare
    [writeData appendData:temData];
    //sendOtaData
    [self sendOtaData:writeData];
    TelinkDebugLog(@"send OTA Start Extend - > %@", writeData);
}

/// OTA FirmWare Version Request
/// - Parameters:
///   - firmwareVersion: firmware Version
///   - versionCompare: Tell the device side OTA if version number comparison is required.
- (void)sendOTAFirmWareVersionRequestWithFirmWareVersion:(UInt16)firmwareVersion versionCompare:(BOOL)versionCompare {
    //init NSMutableData
    NSMutableData *writeData = [NSMutableData data];
    //opcode to hex data
    UInt16 tem16 = TelinkOtaOpcode_otaFirmWareVersionRequest;
    NSData *temData = [NSData dataWithBytes:&tem16 length:2];
    //append data of opcode
    [writeData appendData:temData];
    //firmwareVersion to hex data
    tem16 = firmwareVersion;
    temData = [NSData dataWithBytes:&tem16 length:2];
    //append data of firmwareVersion
    [writeData appendData:temData];
    //versionCompare to hex data
    UInt8 tem8 = versionCompare ? 1 : 0;
    temData = [NSData dataWithBytes:&tem8 length:1];
    //append data of versionCompare
    [writeData appendData:temData];
    //sendOtaData
    [self sendOtaData:writeData];
    TelinkDebugLog(@"send OTA FirmWare Version Request - > %@", writeData);
}

/// Firmware Index Set
/// - Parameter index: Firmware Index
- (void)sendFirmwareIndexSetWithIndex:(UInt8)index {
    //init NSMutableData
    NSMutableData *writeData = [NSMutableData data];
    //opcode to hex data
    UInt16 tem16 = TelinkOtaOpcode_setFirmwareIndex;
    NSData *data = [NSData dataWithBytes:&tem16 length:2];
    //append data of opcode
    [writeData appendData:data];
    //index to hex data
    UInt8 tem8 = index;
    data = [NSData dataWithBytes:&tem8 length:1];
    //append data of index
    [writeData appendData:data];
    //sendOtaData
    [self sendOtaData:writeData];
    TelinkDebugLog(@"send Firmware Index Set - > %@", writeData);
}

/// Send Bluetooth data
/// - Parameter data: ota hex data
- (void)sendOtaData:(NSData *)data {
    //writeValue with CBCharacteristicWriteWithoutResponse
    [TelinkBluetoothManager.shareCentralManager writeValue:data toPeripheral:TelinkBluetoothManager.shareCentralManager.currentPeripheral forCharacteristic:TelinkBluetoothManager.shareCentralManager.currentCharacteristic type:CBCharacteristicWriteWithoutResponse];
}

/// Get TelinkOta Opcode With PduData
/// - Parameter pduData: ota hex data
- (TelinkOtaOpcode)getTelinkOtaOpcodeWithPduData:(NSData *)pduData {
    TelinkOtaOpcode opcode = 0;
    if (pduData && pduData.length >= 2) {
        UInt16 tem16 = 0;
        Byte *dataByte = (Byte *)pduData.bytes;
        memcpy(&tem16, dataByte, 2);
        opcode = tem16;
    }
    return opcode;
}

/// calculate crc16
/// - Parameters:
///   - pD: point of data
///   - len: length of data
extern unsigned short crc16 (unsigned char *pD, int len) {
    static unsigned short poly[2]={0, 0xa001};              //0x8005 <==> 0xa001
    unsigned short crc = 0xffff;
    int i,j;
    for(j=len; j>0; j--) {
        unsigned char ds = *pD++;
        for(i=0; i<8; i++) {
            crc = (crc >> 1) ^ poly[(crc ^ ds ) & 1];
            ds = ds >> 1;
        }
    }
    return crc;
}

@end
