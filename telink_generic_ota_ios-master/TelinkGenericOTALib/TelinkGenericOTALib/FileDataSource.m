/********************************************************************************************************
 * @file     FileDataSource.m
 *
 * @brief    A concise description.
 *
 * <AUTHOR> 梁家誌
 * @date     2020/7/14
 *
 * @par     Copyright (c) [2020], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#import "FileDataSource.h"

@interface FileDataSource()
@property (nonatomic, strong) NSMutableArray *filePaths;
@end

@implementation FileDataSource

/**
 *  @brief  Singleton method
 *
 *  @return the default singleton instance.
 */
+ (FileDataSource *)share{
    /// Singleton instance
    static FileDataSource *share = nil;
    /// Note: The dispatch_once function can ensure that a certain piece
    /// of code is only executed once in the entire application life cycle!
    static dispatch_once_t tempOnce=0;
    dispatch_once(&tempOnce, ^{
        /// Initialize the Singleton configure parameters.
        share = [[FileDataSource alloc] init];
        [share initData];
    });
    return share;
}

- (void)initData{
    //init filePaths
    _filePaths = [NSMutableArray array];
}

/// Get All Bin File Path
- (NSArray <NSString *>*)getAllBinFilePath {
    //remove all old file paths.
    [_filePaths removeAllObjects];

    // 搜索bin文件的目录(工程内部添加的bin文件)
    NSArray *bins = [[NSBundle mainBundle] pathsForResourcesOfType:@"bin" inDirectory:nil];
    for (int i = 0; i<bins.count; i++) {
        //add bins to filePaths.
        [_filePaths addObject:bins[i]];
    }

    //搜索Documents(通过iTunes File 加入的文件需要在此搜索)
    NSFileManager *manager = [NSFileManager defaultManager];
    NSError *error = nil;
    //get fileLocalPath
    NSString *fileLocalPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) firstObject];
    //get fileNames
    NSArray *fileNames = [manager contentsOfDirectoryAtPath:fileLocalPath error:&error];
    for (NSString *path in fileNames) {
        if ([path containsString:@".bin"]) {
            //add path to filePaths
            [_filePaths addObject:[fileLocalPath stringByAppendingPathComponent:path]];
        }
    }
    return _filePaths;
}

/// Get Data With Last Path Component
/// - Parameter lastPathComponent: Last Path Component
- (NSData *)getDataWithLastPathComponent:(NSString *)lastPathComponent {
    //路径中间的UUID可能会改变。
    NSArray *tem = [NSArray arrayWithArray:self.getAllBinFilePath];
    NSString *path = nil;
    //get filePath with lastPathComponent
    for (NSString *filePath in tem) {
        //If there are multiple files with the same file name, an exception will be recognized
        if ([filePath.lastPathComponent isEqualToString:lastPathComponent.lastPathComponent]) {
            path = filePath;
            break;
        }
    }
    //init data by path
    NSData *data = [NSData dataWithContentsOfFile:path];
    return data;
}

/// 获取Bin文件的PID的值。
/// @param data Bin文件的二进制数据。
- (UInt16)getPidWithOTAData:(NSData *)data {
    UInt16 pid = 0;
    Byte *tempBytes = (Byte *)[data bytes];
    if (data && data.length >= 4) {
        memcpy(&pid, tempBytes + 0x2, 2);
    }
    return pid;
}

/// 获取Bin文件的VID的值。
/// @param data Bin文件的二进制数据。
- (UInt16)getVidWithOTAData:(NSData *)data {
    UInt16 vid = 0;
    Byte *tempBytes = (Byte *)[data bytes];
    if (data && data.length >= 6) {
        memcpy(&vid, tempBytes + 0x4, 2);
    }
    return vid;
}

/// 获取SecurityBoot的Bin文件的公钥数据。
/// @param data SecurityBoot的Bin文件的二进制数据。
- (NSData *)getPublicKeyDataWithSecurityBootBinData:(NSData *)data {
    NSData *tem = nil;
    if (data && data.length >= 32) {
        UInt16 offset = 0;
        Byte *tempBytes = (Byte *)[data bytes];
        memcpy(&offset, tempBytes + data.length - 32, 2);
        if (data.length >= offset + 64) {
            tem = [data subdataWithRange:NSMakeRange(offset, 64)];
        }
    }
    return tem;
}

/// 获取SecurityBoot的Bin文件的签名数据。
/// @param data SecurityBoot的Bin文件的二进制数据。
- (NSData *)getSignatureDataWithSecurityBootBinData:(NSData *)data {
    NSData *tem = nil;
    if (data && data.length >= 30) {
        UInt16 offset = 0;
        Byte *tempBytes = (Byte *)[data bytes];
        memcpy(&offset, tempBytes + data.length - 30, 2);
        if (data.length >= offset + 64) {
            tem = [data subdataWithRange:NSMakeRange(offset, 64)];
        }
    }
    return tem;
}

@end
