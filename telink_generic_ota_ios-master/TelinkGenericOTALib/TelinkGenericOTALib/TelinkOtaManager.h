/********************************************************************************************************
 * @file     TelinkOtaManager.h
 *
 * @brief    A concise description.
 *
 * <AUTHOR> 梁家誌
 * @date     2020/7/21
 *
 * @par     Copyright (c) [2020], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

#define BootLoaderNameString @"OTA-Bootloader"
typedef void (^BackBootLoaderPeripheralCallback)(CBPeripheral * _Nullable backBootLoaderPeripheral);

/*
 BootLoader逻辑：
 1.连接上当前非BootLoader的设备，发送Start OTA指令(0xFF01)。
 2.设备会重启，并且设备名称会在原来名称基础上加上“-BootLoader”？
 3.APP重新扫描BootLoader的设备，连接，扫描服务，根据之前设置的OTA Settings来正常发送OTA数据包即可。
 
 注意扫描BootLoader设备的逻辑：
 1.扫描时长3秒钟。
 2.如果扫描到1个对应名称的BootLoader设备，则选这个设备即可。
 3.如果扫描到多个对应名称的BootLoader设备，选择RSSI最强的设备，且需要过滤RSSI=127的设备。
 
 
 BootLoader logic:
 1. Connect to the current non BootLoader device and send the Start OTA command (0xFF01).
 2. Will the device restart and add "- BootLoader" to the original name?
 3. The app scans the BootLoader device again, connects, scans the service, and sends OTA packets normally according to the previously set OTA Settings.
 
 Pay attention to the logic of scanning BootLoader devices:
 1. Scan for 3 seconds.
 2. If one BootLoader device with the corresponding name is scanned, select that device.
 3. If multiple BootLoader devices with corresponding names are scanned, select the device with the strongest RSSI and filter out devices with RSSI=127.
 */

typedef void(^otaProgressCallBack)(float progress);

/// 表格 7-4 CMD 的 opcode
/// - seeAlso: AN_20111001-C_Telink B91 BLE Single Connection SDK Developer Handbook.pdf  (page.303)
/// Note:
/// Use:To identify the command use in Legacy protocol、Extend protocol or both of all
/// Legacy: Only use in the Legacy protocol
/// Extend: Only use in the Extend protocol
/// All: use both in the Legacy protocol and Extend protocol
typedef enum : UInt16 {
    /// (Legacy)该命令为获得 slave 当前 firmware 版本号的命令，user 若采用 OTA Legacy protocol 进行 OTA 升级，可以选 择使用，在使用该命令时，可通过 slave 端预留的回调函数来完成 firmware 版本号的传递。
    TelinkOtaOpcode_otaVersion = 0xFF00,
    /// (Legacy)该命令为 OTA 升级开始命令，master 发这个命令给 slave，用来正式启动 OTA 更新。该命令仅供 Legacy Protocol 进行使用，user 若采用 OTA Legacy protocol，则必须使用该命令。
    TelinkOtaOpcode_otaStart = 0xFF01,
    /// (All)该命令为结束命令，OTA 中的 legacy 和 extend protocol 均采用该命令为结束命令，当 master 确定所有的 OTA 数据都被 slave 正确接收后，发送 OTA end 命令。为了让 slave 再次确定已经完全收到了 master 所有数据 (double check，加一层保险)，OTA end 命令后面带 4 个有效的 bytes，后面详细介绍。
    TelinkOtaOpcode_otaEnd = 0xFF02,
    /// (Extend)该命令为 extend protocol 中的 OTA 升级开始命令，master 发这个命令给 slave，用来正式启动 OTA 更新。 user 若采用 OTA extend protocol 则必须采用该命令作为开始命令。
    TelinkOtaOpcode_otaStartExtend = 0xFF03,
    /// (Extend)该命令为 OTA 升级过程中的版本比较请求命令，该命令由 client 发起给 Server 端，请求获取版本号和升级许 可。
    TelinkOtaOpcode_otaFirmWareVersionRequest = 0xFF04,
    /// (Extend)该命令为版本响应命令，server 端在收到 client 发来的版本比较请求命令(CMD_OTA_FW_VERSION_REQ) 后，会将已有的 firmware 版本号与 client 端请求升级的版本号进行对比，确定是否升级，相关信息通过该命令返 回发送给 client.
    TelinkOtaOpcode_otaFirmWareVersionResponse = 0xFF05,
    /// (All)该命令为 OTA 结果返回命令，OTA 结束后 slave 会将结果信息发送给 master，在整个 OTA 过程中，无论成功 或失败，OTA_result 只会上报一次，user 可根据返回的结果来判断升级是否成功。
    TelinkOtaOpcode_otaResult = 0xFF06,
    TelinkOtaOpcode_setFirmwareIndex = 0xFF80,
} TelinkOtaOpcode;

/// Result:OTA 结果信息，所有可能的返回结果如下表所示:
/// - seeAlso: AN_20111001-C_Telink B91 BLE Single Connection SDK Developer Handbook.pdf  (page.307)
typedef enum : UInt8 {
    /// success
    TelinkOtaResultCode_success = 0x00,
    /// OTA data packet sequence number error: repeated OTA PDU or lost some OTA PDU.
    TelinkOtaResultCode_dataPacketSequenceError = 0x01,
    /// invalid OTA packet: 1. invalid OTA command; 2. addr_index out of range; 3.not standard OTA PDU length.
    TelinkOtaResultCode_packetInvalid = 0x02,
    /// packet PDU CRC err.
    TelinkOtaResultCode_dataCRCError = 0x03,
    /// write OTA data to flash ERR.
    TelinkOtaResultCode_writeFlashError = 0x04,
    /// lost last one or more OTA PDU.
    TelinkOtaResultCode_dataUncomplete = 0x05,
    /// peer device send OTA command or OTA data not in correct flow.
    TelinkOtaResultCode_flowError = 0x06,
    /// firmware CRC check error.
    TelinkOtaResultCode_firmwareCheckError = 0x07,
    /// the version number to be update is lower than the current version.
    TelinkOtaResultCode_versionCompareError = 0x08,
    /// PDU length error: not 16*n, or not equal to the value it declare in "CMD_OTA_START_EXT" packet.
    TelinkOtaResultCode_pduLengthError = 0x09,
    /// firmware mark error: not generated by telink's BLE SDK.
    TelinkOtaResultCode_firmwareMarkError = 0x0A,
    /// firmware size error: no firmware_size; firmware size too small or too big.
    TelinkOtaResultCode_firmwareSizeError = 0x0B,
    /// time interval between two consequent packet exceed a value(user can adjust this value).
    TelinkOtaResultCode_dataPacketTimeout = 0x0C,
    /// OTA flow total timeout.
    TelinkOtaResultCode_timeout = 0x0D,
    /// OTA fail due to current connection terminate(maybe connection timeout or local/peer device terminate connection).
    TelinkOtaResultCode_failDueToConnectionTerminate = 0x0E,
    
    /* only secure boot mode involved form 0x80 */
    /// OTA server device hardware error.
    TelinkOtaResultCode_SECBOOT_HW_ERR = 0x80,
    /// OTA server device system error.
    TelinkOtaResultCode_SECBOOT_SYSTEM_ERR = 0x81,
    /// OTA server device do not enable secure boot function.
    TelinkOtaResultCode_SECBOOT_FUNC_NOT_ENABLE = 0x82,
    /// OTA public key & signature sequence number error: repeated or lost.
    TelinkOtaResultCode_SECBOOT_PUBKEY_SIGN_SEQ_ERR = 0x83,
    /// OTA client public key not match OTA server device local hash.
    TelinkOtaResultCode_SECBOOT_PUBLIC_KEY_ERR = 0x84,
    /// OTA client do not send Signature data before OTA firmware PDU.
    TelinkOtaResultCode_SECBOOT_NO_SIGN_BEFORE_OTA_PDU = 0x85,
    /// OTA signature verification fail.
    TelinkOtaResultCode_SECBOOT_SIGN_VERIFY_FAIL = 0x86,
    
    // other, Reserved for future use.

} TelinkOtaResultCode;

typedef enum : UInt8 {
    TelinkOtaProtocol_legacy = 0,
    TelinkOtaProtocol_extend = 1,
} TelinkOtaProtocol;

@interface OTASettingsModel : NSObject
/// Service uuid of OTA
@property (strong, nonatomic) NSString *serviceUuidString;
/// Characteristic uuid of OTA
@property (strong, nonatomic) NSString *characteristicUuidString;
/// read interval
@property (assign, nonatomic) UInt8 readInterval;
/// 为了兼容iOS11以下系统而新增的发包间隔，单位毫秒。
/// The newly added interval for sending packages to be compatible with iOS11 and below systems, measured in milliseconds.
@property (assign, nonatomic) UInt16 writeInterval;
/// OTA Bin file path
@property (strong, nonatomic) NSString *filePath;
/// Identify whether to send the 'SetFirmwareIndex' command
@property (assign, nonatomic) BOOL needSetFirmwareIndex;
/// The value of firmwareIndex, range is 0~ 255.
@property (assign, nonatomic) UInt8 firmwareIndex;
/// TelinkOtaProtocol, TelinkOtaProtocol_legacy or TelinkOtaProtocol_extend.
@property (assign, nonatomic) TelinkOtaProtocol protocol;
/// 这个是`sendOTAFirmWareVersionRequestWithFirmWareVersion:`指令里面的参数
/// This is a parameter in the 'sendOTAFirmWareVersionRequestWithFirmWareVersion:' command
@property (assign, nonatomic) BOOL versionCompare;
/// Version number of Bin file.
@property (assign, nonatomic) UInt16 binVersion;
/// Extend模式的OTA，发送当个OTA包的最大OTA数据的长度，为16的倍数，范围是16*1~16*15。默认为16*1
/// The OTA in Extend mode sends the maximum length of OTA data for a current OTA packet, which is a multiple of 16 and ranges from 16 * 1 to 16 * 15. Default is 16 * 1
@property (assign, nonatomic) UInt8 pduLength;
/// Extend模式的OTA，新增一个配置security boot，默认为NO。
/// OTA in extend mode, add a new configuration for security boot, which defaults to NO.
@property (assign, nonatomic) BOOL securityBootEnable;
/// security boot的描述符bin文件路径
/// The descriptor bin file path for security boot
@property (strong, nonatomic) NSString *securityBootFilePath;
/// security boot的描述符bin文件数据，客户未赋值则从securityBootFilePath取数据。
/// The descriptor bin file data for security boot is retrieved from the securityBootFilePath if the client has not assigned a value.
@property (strong, nonatomic) NSData *securityBootBinData;

/// Initialize OTASettingsModel object.
/// - Parameter model: The old OTASettingsModel object.
- (instancetype)initWithOTASettingsModel:(OTASettingsModel *)model;

/// get dictionary from OTASettingsModel object.
/// @returns return dictionary object.
- (NSDictionary *)outputSettingsDictionary;

/// Set dictionary to OTASettingsModel object.
/// @param dictionary OTASettingsModel dictionary object.
- (void)inputSettingsDictionary:(NSDictionary *)dictionary;

/// Get OTASettingsModel object describe string.
- (NSString *)getDetailString;

@end

@interface TelinkOtaManager : NSObject
@property (strong, nonatomic) OTASettingsModel *settings;

/**
 *  @brief  Singleton method
 *
 *  @return the default singleton instance.
 */
+ (instancetype)share;

/**
    Start OTA
 
 @param otaData data for OTA
 @param per peripheral for OTA
 @param otaProgressAction callback with single model OTA progress
 @param otaResultAction callback when peripheral OTA finish, OTA is successful when error is nil.
 @return  true when call API success;false when call API fail.
 @note 注意：需要客户已经调用`startConnectPeripheral:`连接设备成功的前提下才可以调用该OTA方法，且不可重复调用该接口。
 The OTA method can only be called if the customer has successfully called 'startConnectPeripheral:' to connect to the device, and the interface cannot be called repeatedly.
 */
- (BOOL)startOTAWithOtaData:(NSData *)otaData peripheral:(CBPeripheral *)per otaProgressAction:(otaProgressCallBack)otaProgressAction otaResultAction:(peripheralResultCallBack)otaResultAction;

/// Stop OTA
- (void)stopOTA;

/// 将输入设备切换到BootLoader模式，并将BootLoader模式的设备返回APP。
/// Switch the input device to BootLoader mode and return the device in BootLoader mode to the APP.
/// @param peripheral 输入设备，可以是BootLoader模式设备，也可以是非BootLoader模式设备。
/// The input device can be either a BootLoader mode device or a non BootLoader mode device.
/// @param resultHandle 结果回调，如果返回设备为nil表示没有扫描到BootLoader模式的设备。
/// Result callback, if the returned device is nil, it indicates that no device in BootLoader mode has been scanned.
- (void)switchToBootLoaderPeripheralWithPeripheral:(CBPeripheral *)peripheral resultHandle:(BackBootLoaderPeripheralCallback)resultHandle;

@end

NS_ASSUME_NONNULL_END
