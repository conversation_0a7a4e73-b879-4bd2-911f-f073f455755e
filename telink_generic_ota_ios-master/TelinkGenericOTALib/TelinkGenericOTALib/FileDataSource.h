/********************************************************************************************************
 * @file     FileDataSource.h
 *
 * @brief    A concise description.
 *
 * <AUTHOR> 梁家誌
 * @date     2020/7/14
 *
 * @par     Copyright (c) [2020], Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *******************************************************************************************************/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface FileDataSource : NSObject

/**
 *  @brief  Singleton method
 *
 *  @return the default singleton instance.
 */
+ (FileDataSource *)share;

/// Get All Bin File Path
- (NSArray <NSString *>*)getAllBinFilePath;

/// Get Data With Last Path Component
/// - Parameter lastPathComponent: Last Path Component
- (NSData *)getDataWithLastPathComponent:(NSString *)lastPathComponent;

/// 获取Bin文件的PID的值。
/// @param data Bin文件的二进制数据。
- (UInt16)getPidWithOTAData:(NSData *)data;

/// 获取Bin文件的VID的值。
/// @param data Bin文件的二进制数据。
- (UInt16)getVidWithOTAData:(NSData *)data;


/// 获取SecurityBoot的Bin文件的公钥数据。
/// @param data SecurityBoot的Bin文件的二进制数据。
- (NSData *)getPublicKeyDataWithSecurityBootBinData:(NSData *)data;

/// 获取SecurityBoot的Bin文件的签名数据。
/// @param data SecurityBoot的Bin文件的二进制数据。
- (NSData *)getSignatureDataWithSecurityBootBinData:(NSData *)data;

@end

NS_ASSUME_NONNULL_END
