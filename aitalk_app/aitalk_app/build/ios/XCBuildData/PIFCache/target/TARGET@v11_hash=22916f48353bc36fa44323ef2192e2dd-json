{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98df0be194fa690c822e8ad40f457327d6", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=0 PERMISSION_CONTACTS=0", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ac0ea615800e24339c41440a3fa1c219", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=0 PERMISSION_CONTACTS=0", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ac0ea615800e24339c41440a3fa1c219", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=0 PERMISSION_CONTACTS=0", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e8e64398d3da156c778e623d055191c5", "guid": "bfdfe7dc352907fc980b868725387e9833a3350eae93870e786cd862d753790e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985dae1e9cd006e7bf7477e140fce51116", "guid": "bfdfe7dc352907fc980b868725387e9849df4966a178dabf5b9c1f45df20934c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981152db4a812c0a21def33e29b8c736dc", "guid": "bfdfe7dc352907fc980b868725387e98c4b684bfe4739e3e6f4911a982120f58", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d437854311e298b205782b06e5f449d", "guid": "bfdfe7dc352907fc980b868725387e988234f02f13f8eaf816a6c411eff59ef8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98009d8827b1743eb19d4235175a5e9e2c", "guid": "bfdfe7dc352907fc980b868725387e98e8934c65040fb6e9b86ec9634c67df2e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98344df3884f81289bf492c6ad777ff152", "guid": "bfdfe7dc352907fc980b868725387e985ca72156c4eb4bf6c21648c0eb8353da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5694570f26c44b113edf0913f0f3ec0", "guid": "bfdfe7dc352907fc980b868725387e98ccacc88038fcdf91b9b263549971f5d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821e8a507bf5f9cad6103cd63bb6c410b", "guid": "bfdfe7dc352907fc980b868725387e9838ab0608d53a816333e5e8672bfdc85b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf37b09dd1d170f24d765064452d6ae1", "guid": "bfdfe7dc352907fc980b868725387e98a12a58820e946a48a5e3d3089d886e3a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f9795d1ee30cc18578238f7e034f749", "guid": "bfdfe7dc352907fc980b868725387e98989482ea541cf573448928c3cf09d9c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fabcc555f7934e59a1d2e6bae1ca18f", "guid": "bfdfe7dc352907fc980b868725387e987cab0bdab68854abd6cae7a16ff5f356", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828db19414e644bc00477786ea917f2f8", "guid": "bfdfe7dc352907fc980b868725387e982cf098b28180dc857e565b963c1f3a1e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876a9cd34fe5b6301e1ab98ce766fe141", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d0aa7dee1dc6db9a1de97e3f7b4ed9c", "guid": "bfdfe7dc352907fc980b868725387e982a58ba1b1f002297555dbf1befdd4bc0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf011da89a480bd81c446e3358ab50de", "guid": "bfdfe7dc352907fc980b868725387e98311597869d44bbdb692c5542c213880f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e44abfcbea10a801dbcb5888b77b366", "guid": "bfdfe7dc352907fc980b868725387e98d4ff82017cc0febc0608f5b1a5d7cab2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c467cd8315c3ab41ad6d57f924000b94", "guid": "bfdfe7dc352907fc980b868725387e986aebd7080ad1d4ea1c90169a963f37c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5fc93be42cd8d5f70d844d650a7bdce", "guid": "bfdfe7dc352907fc980b868725387e98ee980b3af77c09ce771851d01a87bc59", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c4791a43a073158acdbdb38555001a5", "guid": "bfdfe7dc352907fc980b868725387e9863460aee3a5f4144c4b8302c884dc522", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98544c721a6ea7ab634248bb8cc3bb2904", "guid": "bfdfe7dc352907fc980b868725387e98962012d9710b1d8a406bbb52326eec90", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981de2c71b261d6374ab49efa40ace2641", "guid": "bfdfe7dc352907fc980b868725387e988d47c7c912639e603659dce5d3303a16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afe466f3360b16b63af2b687f9e23bf8", "guid": "bfdfe7dc352907fc980b868725387e98651b4bb2e3071ec1e5f336e4d3413aa0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae4b4bd9f02f3f67df3d084ec5840ae7", "guid": "bfdfe7dc352907fc980b868725387e98c58f4f2b32b9221bb33c00b0bdace8ec", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985712e5f30b49991ff309197a7214db0c", "guid": "bfdfe7dc352907fc980b868725387e98c8d21bbfcbf55d2aa2a54a74979c8617"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98748585d66d738612b10d38e8757b9037", "guid": "bfdfe7dc352907fc980b868725387e989b01a59aebaf399d2f75605210ea86ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831970f4ec57fa7a7fc22f6eea2b24483", "guid": "bfdfe7dc352907fc980b868725387e9845ac4f3aa1b304f273acc100e7dc3489"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898087294958795d0b0d59124195260d4", "guid": "bfdfe7dc352907fc980b868725387e980a94579c03e96dda17e7a6e1c2afcaa6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a0cd3f67edf1669f53757a61814bc47", "guid": "bfdfe7dc352907fc980b868725387e9899ac52f267d37fb3b7002de899109e0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e90442de70911612dee91c7ba784f31", "guid": "bfdfe7dc352907fc980b868725387e9809b17e0052a32cfabbee35041b60d8fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf3f9e1c64fd81b9d18722a9893724c6", "guid": "bfdfe7dc352907fc980b868725387e98c0c49d30f1082cef24ada065a79ffd9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae4b21214690c073b89a760fc175cf03", "guid": "bfdfe7dc352907fc980b868725387e98553b42738320dee155352fb60454a304"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984add890f69891f943c24b41fec1178eb", "guid": "bfdfe7dc352907fc980b868725387e98acc185f8cd9820baba204214b7822f48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98579f9ea21e302f52778a60cac6e83776", "guid": "bfdfe7dc352907fc980b868725387e98ec2a48579429bdd761e3aec4815a2af6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98997ea2347f563a08df69d11f162e02cc", "guid": "bfdfe7dc352907fc980b868725387e98451f5753df34cb5e525c3b66cc050b44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff944d12d22d93f082cb0e3f4adfabaf", "guid": "bfdfe7dc352907fc980b868725387e9872f4020df7dd49d3ec489475e78041d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b414c3b792e9ad796582241bb1a9ac8", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809a62f9396e0306a929719d11eae5773", "guid": "bfdfe7dc352907fc980b868725387e98a419843e4d83a4b53f058b0121b132a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1b957301c720211b37f7763c6fd5f75", "guid": "bfdfe7dc352907fc980b868725387e98d358842643c6416b7b7193cf144ea500"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f72f4d1b9cb463b7562cbf2bb455726", "guid": "bfdfe7dc352907fc980b868725387e98fa0dd451bed2471830e8ebca1cedefc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ccdea5d1ad36460904487c2f8ce971f", "guid": "bfdfe7dc352907fc980b868725387e98e22a958594cebd1a793b3b2a0047ae28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea2581f08cdb2ed9d5c4fd7fb2594bd1", "guid": "bfdfe7dc352907fc980b868725387e98b2ed1cc8d2ba8b29bfa583541a7a0ec0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f43eb10f1de4face0d480193405b75d", "guid": "bfdfe7dc352907fc980b868725387e98aea2ac554b983cc69c4257e71c778c8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0b9b02c2af5e90ff53a5bc8412a58d4", "guid": "bfdfe7dc352907fc980b868725387e98df81ec25041808c51fc7b75ae0b31b86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866b546a83397a4cfdb5d110b13553d50", "guid": "bfdfe7dc352907fc980b868725387e982a9693cd37376af9112fbefbaf4ecfb2"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98694402cafaaf96f5099d07dfc1c41bec", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}