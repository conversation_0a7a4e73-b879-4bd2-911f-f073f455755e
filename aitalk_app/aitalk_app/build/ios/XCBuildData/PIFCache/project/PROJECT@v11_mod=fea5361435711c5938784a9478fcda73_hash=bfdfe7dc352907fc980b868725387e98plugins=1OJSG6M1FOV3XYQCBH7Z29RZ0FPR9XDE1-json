{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e9841694db9191404dab0574df10aa4d92c", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e983e45497ad99b5f0ab9ebcda8afbb048b", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98ab0849b3c8d2627830dfc45f1064e777", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c52bf3f3f4eeed446065291c46a3107e", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/ios/Classes/AudioContext.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d61fd7e05feb71e4274e5d6664d7e12e", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/ios/Classes/AudioplayersDarwinPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98473845a7276b58014973eda1c53d2383", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/ios/Classes/AudioplayersDarwinPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ff0ff2d48cb58f4352553ae4ff776169", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/ios/Classes/SwiftAudioplayersDarwinPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d6c83fab464f5af5f69f4f42c05c0df8", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/ios/Classes/Utils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c7662ac6b5707d5ee47f5f931be96bab", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/ios/Classes/WrappedMediaPlayer.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9872f5a2cd2886b1b3c5cfe60529e81b38", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d9db5f22e5874335d45135a27ebc4df", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9ab102e663dd866f21caec51d8d7981", "name": "audioplayers_darwin", "path": "audioplayers_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e157f3b6388d2795540d1ca2a89c2a4c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ceac36f92100839c728ae5e2248cf3f6", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880448a339a24f6e1048f297c30ff6495", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f2f2eb6f822940d78a3d373a4892c174", "name": "aitalk_app", "path": "aitalk_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9860526806eed89bc4159aa91cc90220a3", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b8082a48567e19d63cf1eac2d8c4f938", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983b4c8c787906e20fefbba7d0b7fe6806", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f2e79ccbb033d78d0b33c7c3f2e593fd", "name": "paul.li", "path": "paul.li", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9831755586becb641fe8b0ec5a8aa924ce", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9848d0072268864487a94e0c21f807fd51", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986d0449f19b9972a226564b6d7b4d875e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985ed91814c31cfe83fb620d29541ce933", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980f1bc7f3ae4c1f0e924b012caff8f901", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984e56cc3190427f0f9ea3f3036dc010aa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9835e3807bffe42afd6c26ccfb8061599e", "name": "..", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e984d5724d28ce782e78f2cdb684303df87", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/ios/audioplayers_darwin.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e980f60319cb435e9aff0f3ce24b95fcb0b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984ccca05ec9ec608e063c0a3abefe1087", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9835240a954fa65210593199132b38a9d4", "path": "audioplayers_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98af4ee435de06480f149878e3700fef83", "path": "audioplayers_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98776701ca80afe9bacd51a6bea3e2d14a", "path": "audioplayers_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9889ccbcfcbf17e6afc088bf6c8ac382a8", "path": "audioplayers_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98332bcc60810ab3dfec66f92756a0aef9", "path": "audioplayers_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d93bcf84e36720a9f29191f19257f3f1", "path": "audioplayers_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f8183258e35d1d76228b81120018b576", "path": "audioplayers_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b105e117778110f07508ece95ba8ad5f", "name": "Support Files", "path": "../../../../Pods/Target Support Files/audioplayers_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9846664947c986fefd85ff3cec91452b4d", "name": "audioplayers_darwin", "path": "../.symlinks/plugins/audioplayers_darwin/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9893d915c55fc58353f63387728e72a677", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9884b0b31ea0c0fc5355cee9f15691e60d", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980600b2c085ba69c5b20287dabdd42baa", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9878ee2f6a64d1dd56ce294ffa49733ee9", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9829a9825a9c15a3c687e66b2679eda0f5", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9891f1d39fa26ae45a91311f1032d895fa", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e6fd8ecff35679b65dd85bdec3a487a1", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_blue_plus_darwin-4.0.1/darwin/flutter_blue_plus_darwin/Sources/flutter_blue_plus_darwin/FlutterBluePlusPlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d63a8f86cb2b8b3401b7e6e24f8c7f6e", "path": "../../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_blue_plus_darwin-4.0.1/darwin/flutter_blue_plus_darwin/Sources/flutter_blue_plus_darwin/include/flutter_blue_plus_darwin/FlutterBluePlusPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9818c143eace7c3e5462b0099ef5e5a3e7", "name": "flutter_blue_plus_darwin", "path": "flutter_blue_plus_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a035a24a31a4f6c573dfeb6ccecef683", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988cc51028b5e82b76e71ef93b391d46f2", "name": "flutter_blue_plus_darwin", "path": "flutter_blue_plus_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98717b414f2a8ec1a9ee8336ca9126c8a0", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983cebf4ea4b83d400ac232bca9044f538", "name": "flutter_blue_plus_darwin", "path": "flutter_blue_plus_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98970964b95e66aa84611510295bb21988", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cfd0443b0ef691e6c5bbe3ac5a518b24", "name": "flutter_blue_plus_darwin", "path": "flutter_blue_plus_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98147c40a5487930b15ab22466102329b4", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981cf26a08862a452d545fff44825c07e7", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986fc07e3197e9f6d5e2c67c25194bf761", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9820c7f950a4b79b643ecdf2eddc67aa4b", "name": "aitalk_app", "path": "aitalk_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a17d34e9e6526325b25db5c23a9a4e21", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989e13b3c694f868613e01118f5232414c", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982608da09717f8cc8d199ee8df03b6b77", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc6a92ba2fa58b270418fbbebde8a89a", "name": "paul.li", "path": "paul.li", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9848d13f5687acdebafc0f164244151e84", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b9b1c606097a57bf67b41590eb17dd2a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836bd9a1b55d0b60ba004dd6532fff924", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9884e35b3d46f767a74a1565b005675e7b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989b4c2ac6198c082bd755fa0e9b9f6a4f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98265b2b71271bed035bf5ee03e5c30c88", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980e4cfcf90b25ca2d335df3f928181fc9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98851a0bb983e74b5a53239a911c37443d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98722a20ca22586805b13c32557d30602c", "name": "..", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_blue_plus_darwin-4.0.1/darwin/flutter_blue_plus_darwin/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e988752398fff185fb9f8825863dfd19c18", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_blue_plus_darwin-4.0.1/darwin/flutter_blue_plus_darwin.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e980308dcfec657c6f8d3a03f35d51dcf0d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_blue_plus_darwin-4.0.1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985ba0e48fe70c05fc43ac45ab7be34210", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9869721afcdad910fa600d267745eb33f0", "path": "flutter_blue_plus_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9854ff249a06c273d00cda4bb61bd3e8da", "path": "flutter_blue_plus_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986213fe3e065b1a5287ac4768418f746c", "path": "flutter_blue_plus_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9802fa122c6dcfb1379061c0867f051f8b", "path": "flutter_blue_plus_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dc426ff7b82aabca8ab69ed2350feb56", "path": "flutter_blue_plus_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9878bc8208f7f04cdd27df35024ec40224", "path": "flutter_blue_plus_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d5d21a424a77bf9e55d07d9da146a7ea", "path": "flutter_blue_plus_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980ea58083a7537a249912d35204517ee1", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_blue_plus_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bde4a0692c35f6a8df2d603ca2df8c8b", "name": "flutter_blue_plus_darwin", "path": "../.symlinks/plugins/flutter_blue_plus_darwin/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e989a481e7ea8f90fb15c26141bade09b5e", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98286124f22b5524f6fe3282c794380085", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981b8ad7f5b6deb881ff2b3a30acb14630", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b2175b7dbe2f761851fea621434055fd", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d586bea31928f771f430bd9560bcc49b", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989e323009c3e945748e3ee8e4ced493ca", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b0237eb37c768a06c142a513a9259aa4", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f528cdb576a3f75ba538b86bbcac14e8", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fdc5993181ce460fafaefc6f8bff3d60", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98392cf206866643079f693b1798c60b9d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d0947f3b845f21b6c23b82a5ba747d27", "name": "aitalk_app", "path": "aitalk_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982a5716c30cb643d9fa88e81bf0ed438f", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dd2266da6702875328025e721a8f9ffb", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e8f63a71aad8aaa8b3e041f5aed642c3", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b5695333b80b1ede943c55de61a58104", "name": "paul.li", "path": "paul.li", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ee1ba5a2e1b400740ff4a280ff8b751", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce0ed2478ddbb7faed82154adfdfb55b", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98973164be7e99bd635b1868ab0fdd8c96", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980d98aa83679d4b6bde7e5cd5e2105512", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9813165c4f58d9ad92f2a02b2c9fec5628", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98899a6077ecb762af1ac5688c22df9fc9", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984a5a46b5737d337a11bbbdff895caa37", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d43394233f40c926292bfa8bb765c424", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980b2becbf62fe7a61339df7c20023c98e", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a406dd9d5e266e17a055fe6061db2967", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981b19477b62f13401e94113743d500d7a", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981971b56e3c0df51d6d5b6d6222d96fef", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ef786dd2f9113630df834fc987fa82a3", "name": "aitalk_app", "path": "aitalk_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a306d20464404b35695d80236f3fc51c", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98073ca1e4beba934c7485bea2c2026afc", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983778574d2e2c2d1c004c64d6d950fe3c", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988eaca0d2d4bac9f2dd4c0414fc1fb848", "name": "paul.li", "path": "paul.li", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988f21301606f4e2bcbc31c4d18e07ccfd", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986561c746b886819955d3886c6b0a403b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9850a00efacee1c4b5192fa7287c33e9f6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980758d786b0cf22ca312cfcfbf484ae11", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b3f5d7c00eb933d051b57c6f00f91990", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988fa320a2d602961ee2b012ec5022f827", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a88a8551925f16d47d7b3ae6a92f08cb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9854b5f2037834a85d6d9d83ec75db55ba", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983b17c334434a3f64dd9f0d627dafebce", "name": "..", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98c0b8460c55b576d7e766185684a02f1f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98161ac8a09c8931fe430d91462770628c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98be95b18a20f35fd4136608eaac9dae4f", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986504405b41023c6de81b23c67de0a56d", "path": "path_provider_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98914cacf8ce58ca0c791b0fef64301ade", "path": "path_provider_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d1414b9a2fa44c98c1bb6eff9d2bb153", "path": "path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9882784807e8a8899fadfdf2d46c6055ff", "path": "path_provider_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f8c9f8a972c77c883da36d1dff25ac65", "path": "path_provider_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b5dea79c7aa1f0cdbcaff2f20db9457a", "path": "path_provider_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98931cc958991f1cff45871078f7ae2095", "path": "path_provider_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98bfdfe64baa65da9c50f8154032cd5314", "path": "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98674a81e742c040fa9937d4dc912a9ac1", "name": "Support Files", "path": "../../../../Pods/Target Support Files/path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98db15ea067ef089e11c5a8d2c678fa7b1", "name": "path_provider_foundation", "path": "../.symlinks/plugins/path_provider_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d0aa7dee1dc6db9a1de97e3f7b4ed9c", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerEnums.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cf011da89a480bd81c446e3358ab50de", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9809a62f9396e0306a929719d11eae5773", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980e44abfcbea10a801dbcb5888b77b366", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d1b957301c720211b37f7763c6fd5f75", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionManager.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e8e64398d3da156c778e623d055191c5", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985712e5f30b49991ff309197a7214db0c", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985dae1e9cd006e7bf7477e140fce51116", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AssistantPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98748585d66d738612b10d38e8757b9037", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AssistantPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981152db4a812c0a21def33e29b8c736dc", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AudioVideoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9831970f4ec57fa7a7fc22f6eea2b24483", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AudioVideoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984d437854311e298b205782b06e5f449d", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BackgroundRefreshStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9898087294958795d0b0d59124195260d4", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BackgroundRefreshStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98009d8827b1743eb19d4235175a5e9e2c", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BluetoothPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984a0cd3f67edf1669f53757a61814bc47", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BluetoothPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a5694570f26c44b113edf0913f0f3ec0", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/ContactPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bf3f9e1c64fd81b9d18722a9893724c6", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/ContactPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9821e8a507bf5f9cad6103cd63bb6c410b", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/CriticalAlertsPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ae4b21214690c073b89a760fc175cf03", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/CriticalAlertsPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bf37b09dd1d170f24d765064452d6ae1", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/EventPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984add890f69891f943c24b41fec1178eb", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/EventPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986f9795d1ee30cc18578238f7e034f749", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/LocationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98579f9ea21e302f52778a60cac6e83776", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/LocationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982fabcc555f7934e59a1d2e6bae1ca18f", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/MediaLibraryPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98997ea2347f563a08df69d11f162e02cc", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/MediaLibraryPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9828db19414e644bc00477786ea917f2f8", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/NotificationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ff944d12d22d93f082cb0e3f4adfabaf", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/NotificationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c467cd8315c3ab41ad6d57f924000b94", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b5fc93be42cd8d5f70d844d650a7bdce", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhonePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989f72f4d1b9cb463b7562cbf2bb455726", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhonePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983c4791a43a073158acdbdb38555001a5", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhotoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989ccdea5d1ad36460904487c2f8ce971f", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhotoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98544c721a6ea7ab634248bb8cc3bb2904", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SensorPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ea2581f08cdb2ed9d5c4fd7fb2594bd1", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SensorPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981de2c71b261d6374ab49efa40ace2641", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SpeechPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989f43eb10f1de4face0d480193405b75d", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SpeechPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98afe466f3360b16b63af2b687f9e23bf8", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/StoragePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e0b9b02c2af5e90ff53a5bc8412a58d4", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/StoragePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ae4b4bd9f02f3f67df3d084ec5840ae7", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/UnknownPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9866b546a83397a4cfdb5d110b13553d50", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/UnknownPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989350428a89af80bed8c19a149c1edc21", "name": "strategies", "path": "strategies", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98344df3884f81289bf492c6ad777ff152", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/util/Codec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983e90442de70911612dee91c7ba784f31", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/util/Codec.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9872beb2a4dd4fc6f597d935feaa4598e3", "name": "util", "path": "util", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98915ef4baefb39b3bf4d7681c733641bf", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e984298b899ae2c80732406e5a64a8817a1", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98729a008135468890280750699827077d", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98427e79fcbd405cec682b01ce92be4348", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a733a9ba54b38420a2d1785517dab63", "name": "permission_handler_apple", "path": "permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9809095237f84501ec449d4d01e4f29655", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce7c5fd4016aefdd24ef50789a7a511f", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9837dc7b89e1ea5fffb50767d83154fac9", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aa813cc8e8aefea7ccaa35474dca0216", "name": "aitalk_app", "path": "aitalk_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983d22e26e0450fe42bbd1b91c656b8196", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982536db3ea021db62a6cebdb03da316b9", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b4bc7faf4f6f58fda0e0d12d4772ea7f", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a93c9d83c023b3a79452f5614b89c0b0", "name": "paul.li", "path": "paul.li", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98542737bd4314f633e67275129f965a28", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9823fbe1c785c5f097f8067a6a6ae1b7be", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98154d935b91e9d77448af3e00ca8c7788", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989e096e5174f86ff40bf644dbd1da09de", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e3d243eea0f75e37e6f43450f7889d8e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e5897249720f178650ca1822604e687a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ccb3ddc34181e374f4644b61a2079660", "name": "..", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98e27c50e5efd2b2adcbb93ef74dc5b857", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e980bcc68157b358e1e02fb83f1aa7d2fb1", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/permission_handler_apple.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98393f027aecccde7c257d080d6c967f66", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e982e85d63d6e1386411662ed7f0cb7c83e", "path": "permission_handler_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983b414c3b792e9ad796582241bb1a9ac8", "path": "permission_handler_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981c537d517f00ed5a1c304205ce641169", "path": "permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984a0089b5da4a0a80ea1a1e8dfa90d548", "path": "permission_handler_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9876a9cd34fe5b6301e1ab98ce766fe141", "path": "permission_handler_apple-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98df0be194fa690c822e8ad40f457327d6", "path": "permission_handler_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ac0ea615800e24339c41440a3fa1c219", "path": "permission_handler_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98da7230b5c5306870a7515874a981950c", "path": "ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9828565937f6849800bfc7c9c06c7a45be", "name": "Support Files", "path": "../../../../Pods/Target Support Files/permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f8c2945a8113bbdec9155eb068ff3492", "name": "permission_handler_apple", "path": "../.symlinks/plugins/permission_handler_apple/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9841241f67850b0927c4926ad16bf7f8d1", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989537238e978eb56430ea8793f978af39", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98716409c8eaf638efb93fd86ccbd80ffe", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c38affe57e50895eb457373f7b038074", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9878f9551e572d3a5ddf50a56e9d54b775", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982cca2f07e8e7b0c786714fbe85b718a7", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987c4aa26279ab799c10bfbf452bc852a0", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9863e0031b5d16a509ea436e425ff08a20", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986243c23babc0cb14ea817a1a1ed41e43", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a823e54a29fcb93182973bb9012643f7", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98328923304da61795e4510e79ac1280ac", "name": "aitalk_app", "path": "aitalk_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980cea3d98983ce95204904214d0e3216f", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a5347733086abfe9516b3cd78c8bbe46", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9802646d6f1cb0a772cc341ef4a8c7bb8c", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987be46d372756203a700c82ff58a09f78", "name": "paul.li", "path": "paul.li", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98833fc61e5dda85334840febdee97b244", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988e905b57b62316c977747bc9dc07be7d", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9825ce74e9401cb8f4b9453f46b2833ed2", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986aac14a06eb5b5ad82d18dc8f5551b3f", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985d567e7e081f4a0439974b0c5c53ebc5", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9868eb1d49940c40d4a14d64f0ca7dfa8b", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988e49695122b6fdac07ef2cfd45702702", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983cef5fb2a322ac0c76062d9e53c23978", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980536644a1581a5b569b4f6af519338a2", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9869c5ec8eb321b77bf16912063ea5accf", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980d3d54217a97b51980de339b5e80b578", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dabb5febdb60311433872cb96b721e76", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d59f219de7ab8feed9869bc2a41e75e7", "name": "aitalk_app", "path": "aitalk_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c784a06c9e162a16651d5f8a9ea16e7c", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9839b86ab9533efc095cfc1f6170521282", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a31b30fa69b4b591651ed3100f7ab131", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98632472e5e95678e5f277b4ad138f6579", "name": "paul.li", "path": "paul.li", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af0026522d0d037a80a14b11f1cc261f", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f279024e5d74e2b8a58d204162e3283b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983b3df460916dd02fcb614420ada75590", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9814b5f308a37d74c61fcb55b08ce87bac", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986a5af97bd5203119d62ed4ffb1b4fbac", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c38def3bde4fa5ac24d00d8415731648", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988034be37e0910902e2a886b4351079c9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98314c91ee3622cf5fd3249800f57a6366", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9821739075d0e01f8f84769df7fb5b6fde", "name": "..", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9875af125c684edcddf5946b00f58ddfd0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98f751f541fcf4e85c3953bd290112dfb6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98727e6ddadfa128499ee427104bbdd41e", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f30caca92363074c581eadc30d3179f5", "path": "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98628077e9b30ccd8a284ff70ea8b60e1d", "path": "shared_preferences_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d0ce01e361b1fcef17af7dbab22aa6db", "path": "shared_preferences_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98095f5b9037dc205f49be9dae0345f89a", "path": "shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f4de20b170f0c3df911f56354a301f3b", "path": "shared_preferences_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98610dc5e8e116074439a0dc7c28b9401f", "path": "shared_preferences_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980324f984cbe9a4650038b15fb6ff675d", "path": "shared_preferences_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f8eed8e8af857119c46e342ba3fac6b9", "path": "shared_preferences_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98452fe86f89ac5893c2c6b08010e2f07e", "name": "Support Files", "path": "../../../../Pods/Target Support Files/shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987544a42a7d4033b3bef732bded4ceb81", "name": "shared_preferences_foundation", "path": "../.symlinks/plugins/shared_preferences_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e987a8234c530b7a497ca8e6c45b1d4de2a", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f32af3166f791404dc253d653481ea14", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987053ac0d8a52024cd14143ae1f7e2f8a", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9887910c89cace636407f508d6d58d8e45", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f2532f4cb956d9300290997fad4842c6", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c10032a3d41500c6875a0ad50137af18", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9895c9fa1e280f1c2f9874ed0f6ce4924d", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c33dd59a3528d355ba242adc55bae409", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980eac30f698feed14108aa52b8a397406", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986383bfe2c4d470df0399175bbf1e56e1", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e8899f4ff6567c5dcf19c2a0ff040cf8", "name": "aitalk_app", "path": "aitalk_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cd2a88de23666be1bf8f55b28f5f18e5", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984650c4fde1fc80d60229146717f92d4e", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e79a1f559feea20bbd5507cebc09e000", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98357c6e44460c18224656c52fe6e0d1c0", "name": "paul.li", "path": "paul.li", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985383b9880315bf36776dde47b2c7da8c", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aa701bcdd6bf981c806b9060b9a18b2c", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fafbc74f9e62a5e6c2a9b116fb66d816", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984bda61ac899b53d45b561d19c1b20c16", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980497469b5329a05112d7f3687bb3689d", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98910f8569b42f31484c9a929984810e9b", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f714461af4d9187bdca4c0e296a1699e", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984e12fb23b359d34b5e8e1715a352cebe", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cd6a210d2c6ce492be48b57ffd20ace1", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98416867b96671105d77264f643bbd16e0", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cde2333d16c14491adeabf31a256c54c", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDB.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9870812ca3805a79d5f33df83c90cdfd9d", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f708806b05ea3eb48c221084e8e3db94", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a9714321f8570a43cd6905591dbc0852", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cd1d55798e154b9b608ad1dad2dc4074", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988394eb7ac61618b92c400417eb0b2ac0", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9865e34aa41a7f2e2fc1fd2f050c0a6581", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98efdb18d0e0c034bcda3b83b88b009efc", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cd77fa7cfef4cda73cebec612df1604d", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fdc1217bc107a52d882a5451d993e3c2", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cf81a8cde8d4476ae9a04ead544f1a5a", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986343c028539945aea97ad9973e96c308", "path": "../../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqfliteImportPublic.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f815ba9e58bbe0b7aa3900efba6c0ea7", "path": "../../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqflitePluginPublic.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986c08ef292c5cc0b036645d13a4625f8a", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9851ba05337876318fd2f070312aa3cb08", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bf13d63ecdcb237f3a5c291d5c40f582", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ae31fc6f3fb2f7a3bda89aa3ac62bd2e", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d36833b893fd41ca8af7cdbfd1dec9b8", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982216d4b248b4808dd8f947f469897791", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9886351568d8c4f396ede0b74263d22ae6", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982e833dfef2fd6b5dfaaca5b57f296583", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983f45a02bd714e9617f849cd809b2bda4", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985dac0373516c94ba4910cbeb83ac3a1c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e9803e81669636ade03558d444e29f82", "name": "aitalk_app", "path": "aitalk_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e527c833f1a47ddf01d31d43c06e767e", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9845f634e6a3f50536afcec26af652d90e", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983102d23632c7afc7cf434358591236da", "name": "work", "path": "work", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a198baec15ec36adb148c5d8407b737d", "name": "paul.li", "path": "paul.li", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9881ab3d446cee94c25043c584d3695fce", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9837402b507e8e85ef1973fb98a11b1066", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98381cdbdcca986b8a8fb8a6963a329705", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9843831c56ad83a2169d8f13129e109e44", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989b3f781ad4acb86674ba4e36a84a9b9a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989674a19b337e913418001b1a8ebb5b62", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f7234fe520ab20ea5edd52c4157ad183", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9863b58628ea41faae122df0b6f2693b68", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987966904bea9ea37dd8f33a47dab94559", "name": "..", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98c247eaf7d8090a30b8ccb1dfab92dc08", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "bfdfe7dc352907fc980b868725387e9803c5a2d0d5eeeb0156645a046cc03de6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98fa95cf495df681ce77a04afc039238d4", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984e0237aefe52a333918b80c4a564e847", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98399ec2ccb0cef601d9153982ff895d42", "path": "ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9846f06c28605c794fcfaf01aba9374eee", "path": "sqflite_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98689ae8424eefe8c9cd1649269d5f313c", "path": "sqflite_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9846a4f2d8f577789cd1f76183aeef0b2b", "path": "sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982cbcc4ff13be251219540c0d731a15dc", "path": "sqflite_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f176cfb307da9a5734a6c92f1c3a2c10", "path": "sqflite_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f91e8112d744a47191688e922761be8d", "path": "sqflite_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98689e61825ec394b3ce0c0c977dfd105b", "path": "sqflite_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984934532ce6d07398d5a84c66cfd12cef", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d17dd46092c83ca479853345a53789ee", "name": "sqflite_darwin", "path": "../.symlinks/plugins/sqflite_darwin/darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e76ee4a464aca11308e8a09da842abe1", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9899753739776b473e75ef75c97d0c67bb", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreBluetooth.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98694402cafaaf96f5099d07dfc1c41bec", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f432c1385e51b373704dec23b5eddf2d", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98093e0d274405de132c9336fa40762070", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e98ef545c20423cea7275836886eb6484ef", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a3f4680c874ecbcb5b5a6338bc13a426", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9814181933c973297b0001d0e9ee64381d", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9887440869a53d6020d968ddf6b9b30aa0", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fed0970e703d355c9aff813f280aafdc", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e9826e505869a67d81656859ded68178c1d", "path": "Pods-Runner-frameworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98de02ac5005ab1eb052abf25c63731e95", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98f6f4dcb116856f2da0fa97d3015cb1af", "path": "Pods-Runner-resources.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ec6fbca7264a6936f2adfc48dd7f5bc", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9850cc7fc2d23136fb4fac488d6c47df20", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98dc1a21852c7a085c7dd8f65cf0fa9907", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f17abf55d75f35efcaf45a1185b085b6", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cf3c1972df678a5e36df75a46391500d", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984c4f55ec853c945e234980557a98aed8", "path": "Pods-RunnerTests.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fc0f7e7242f459f81e455145932dcafd", "path": "Pods-RunnerTests-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985f8b68b152f46f18718da20c04e675cb", "path": "Pods-RunnerTests-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98022654f1ff78dd844d694dba2439dab2", "path": "Pods-RunnerTests-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989e5ad6b9a07953a12c7008a15bd9c99c", "path": "Pods-RunnerTests-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5e8bcdff29e5f8321be18f7989b4bc7", "path": "Pods-RunnerTests-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98144cd18850e477837c238075d5256ffe", "path": "Pods-RunnerTests.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981b663a2c82f0220040296818ba53477e", "path": "Pods-RunnerTests.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98965b92d39d30a7872295adc2841cd1b1", "path": "Pods-RunnerTests.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9859551a2ccb1df711861b574920cd49bf", "name": "Pods-RunnerTests", "path": "Target Support Files/Pods-RunnerTests", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dafc421ff02609f2772b356038eb9849", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/Desktop/paul.li/work/Code/flutter/aitalk_app/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/Desktop/paul.li/work/Code/flutter/aitalk_app/ios/Pods", "targets": ["TARGET@v11_hash=2cece05763c59a6be24307d0031badde", "TARGET@v11_hash=e38114a57edc8b92ebb284abeea65041", "TARGET@v11_hash=8a71737f37f5360e98453365ad187cf2", "TARGET@v11_hash=d2233ed7a1d850f7a14909e42b153cc0", "TARGET@v11_hash=8fa8eb7d6be1c0ec13583c3fc7d81b9a", "TARGET@v11_hash=22916f48353bc36fa44323ef2192e2dd", "TARGET@v11_hash=4446b61aa5be120caf684a5dee150c40", "TARGET@v11_hash=ab81fdaff06e1c2e020fc189aa80dfdd", "TARGET@v11_hash=719010467d1de14cdcae03f9783c40d0", "TARGET@v11_hash=ffb0edf5fb151759ff0f01308d3d7536", "TARGET@v11_hash=9861e7a5cd30c4b12f9d1ff2cb06f905", "TARGET@v11_hash=8d2a6ec9555b3771f1ea75d483be95fd", "TARGET@v11_hash=64666a65bafc4ba85e24cfe555c6944d"]}