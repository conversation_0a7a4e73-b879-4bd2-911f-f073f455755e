{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "audioplayers_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_blue_plus_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus_darwin-4.0.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-19.3.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_pcm_sound", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_pcm_sound-3.1.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "record_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/record_ios-1.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "audioplayers_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_android-4.0.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_blue_plus_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus_android-4.0.5/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-19.3.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_pcm_sound", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_pcm_sound-3.1.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "record_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/record_android-1.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "audioplayers_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_blue_plus_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus_darwin-4.0.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-19.3.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_pcm_sound", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_pcm_sound-3.1.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "record_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/record_macos-1.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "audioplayers_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_linux-3.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_blue_plus_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus_linux-3.0.2/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-6.0.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "record_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/record_linux-1.1.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}], "windows": [{"name": "audioplayers_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_windows-3.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_windows-1.0.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "record_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/record_windows-1.0.6/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}], "web": [{"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/", "dependencies": [], "dev_dependency": false}, {"name": "audioplayers_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_web-4.1.0/", "dependencies": [], "dev_dependency": false}, {"name": "flutter_blue_plus_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus_web-3.0.1/", "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_html", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/", "dependencies": [], "dev_dependency": false}, {"name": "record_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/record_web-1.1.9/", "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "audio_session", "dependencies": []}, {"name": "audioplayers", "dependencies": ["audioplayers_android", "audioplayers_darwin", "audioplayers_linux", "audioplayers_web", "audioplayers_windows", "path_provider"]}, {"name": "audioplayers_android", "dependencies": []}, {"name": "audioplayers_darwin", "dependencies": []}, {"name": "audioplayers_linux", "dependencies": []}, {"name": "audioplayers_web", "dependencies": []}, {"name": "audioplayers_windows", "dependencies": []}, {"name": "flutter_blue_plus", "dependencies": ["flutter_blue_plus_android", "flutter_blue_plus_darwin", "flutter_blue_plus_linux", "flutter_blue_plus_web"]}, {"name": "flutter_blue_plus_android", "dependencies": []}, {"name": "flutter_blue_plus_darwin", "dependencies": []}, {"name": "flutter_blue_plus_linux", "dependencies": []}, {"name": "flutter_blue_plus_web", "dependencies": []}, {"name": "flutter_local_notifications", "dependencies": ["flutter_local_notifications_linux", "flutter_local_notifications_windows"]}, {"name": "flutter_local_notifications_linux", "dependencies": []}, {"name": "flutter_local_notifications_windows", "dependencies": []}, {"name": "flutter_pcm_sound", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "record", "dependencies": ["record_web", "record_windows", "record_linux", "record_android", "record_ios", "record_macos"]}, {"name": "record_android", "dependencies": []}, {"name": "record_ios", "dependencies": []}, {"name": "record_linux", "dependencies": []}, {"name": "record_macos", "dependencies": []}, {"name": "record_web", "dependencies": []}, {"name": "record_windows", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}], "date_created": "2025-07-28 21:00:43.596465", "version": "3.32.5", "swift_package_manager_enabled": {"ios": false, "macos": false}}