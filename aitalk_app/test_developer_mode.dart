import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'lib/views/profile/developer_mode_screen.dart';
import 'lib/views/device/bluetooth_ota_screen.dart';
import 'lib/l10n/app_localizations.dart';

void main() {
  group('Developer Mode Tests', () {
    testWidgets('DeveloperModeScreen should display correctly', (
      WidgetTester tester,
    ) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(
        MaterialApp(
          localizationsDelegates: AppLocalizations.localizationsDelegates,
          supportedLocales: AppLocalizations.supportedLocales,
          home: const DeveloperModeScreen(),
        ),
      );

      // Verify that the developer mode screen is displayed
      expect(find.text('开发者模式'), findsOneWidget);
      expect(find.text('蓝牙OTA'), findsOneWidget);
      expect(find.text('设备固件升级'), findsOneWidget);
    });

    testWidgets('BluetoothOtaScreen should display correctly', (
      WidgetTester tester,
    ) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(
        MaterialApp(
          localizationsDelegates: AppLocalizations.localizationsDelegates,
          supportedLocales: AppLocalizations.supportedLocales,
          home: const BluetoothOtaScreen(),
        ),
      );

      // Verify that the bluetooth OTA screen is displayed
      expect(find.text('蓝牙OTA升级'), findsOneWidget);
      expect(find.text('设备信息'), findsOneWidget);
      expect(find.text('选择固件文件'), findsOneWidget);
      expect(find.text('开始升级'), findsOneWidget);
    });

    testWidgets('Navigation from DeveloperMode to BluetoothOTA should work', (
      WidgetTester tester,
    ) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(
        MaterialApp(
          localizationsDelegates: AppLocalizations.localizationsDelegates,
          supportedLocales: AppLocalizations.supportedLocales,
          home: const DeveloperModeScreen(),
        ),
      );

      // Tap on the Bluetooth OTA item
      await tester.tap(find.text('蓝牙OTA'));
      await tester.pumpAndSettle();

      // Verify that we navigated to the Bluetooth OTA screen
      expect(find.text('蓝牙OTA升级'), findsOneWidget);
    });
  });
}
