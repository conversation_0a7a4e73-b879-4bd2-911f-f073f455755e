<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>MELPE</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <TargetOption>
        <TargetCommonOption>
          <Device>STM32F401RE</Device>
          <Vendor>STMicroelectronics</Vendor>
          <Cpu>IROM(0x08000000,0x60000) IRAM(0x20000000,0x18000) CPUTYPE("Cortex-M4") FPU2 CLOCK(84000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0STM32F4xx_384 -********** -FL060000 -FP0($$Device:STM32F401RE$Flash\STM32F4xx_384.FLM))</FlashDriverDll>
          <DeviceId>7542</DeviceId>
          <RegisterFile>$$Device:STM32F401RE$Device\Include\stm32f4xx.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:STM32F401RE$SVD\STM32F40x.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\obj\</OutputDirectory>
          <OutputName>Melpe</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>1</UseSimulator>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
          </Simulator>
          <Target>
            <UseTarget>0</UseTarget>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <RestoreTracepoints>1</RestoreTracepoints>
            <RestoreTracepoints>1</RestoreTracepoints>
            <RestoreTracepoints>1</RestoreTracepoints>
          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>0</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile>.\Periph.ini</InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
            <Driver>BIN\UL2CM3.DLL</Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x18000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x60000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x80000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x18000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>4</Optim>
            <oTime>1</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>0</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>0</uC99>
            <useXO>0</useXO>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>__DBG_ITM</Define>
              <Undefine></Undefine>
              <IncludePath>C:\Keil5.0\ARM\RV31\INC;..\..\..\melpe_fxp_src</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>.\obj\Melpe.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Codec</GroupName>
          <Files>
            <File>
              <FileName>classify.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\classify.c</FilePath>
            </File>
            <File>
              <FileName>coeff.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\coeff.c</FilePath>
            </File>
            <File>
              <FileName>dsp_sub.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\dsp_sub.c</FilePath>
            </File>
            <File>
              <FileName>fec_code.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\fec_code.c</FilePath>
            </File>
            <File>
              <FileName>fft_lib.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\fft_lib.c</FilePath>
            </File>
            <File>
              <FileName>fs_lib.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\fs_lib.c</FilePath>
            </File>
            <File>
              <FileName>fsvq_cb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\fsvq_cb.c</FilePath>
            </File>
            <File>
              <FileName>global.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\global.c</FilePath>
            </File>
            <File>
              <FileName>harm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\harm.c</FilePath>
            </File>
            <File>
              <FileName>lpc_lib.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\lpc_lib.c</FilePath>
            </File>
            <File>
              <FileName>mat_lib.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\mat_lib.c</FilePath>
            </File>
            <File>
              <FileName>math_lib.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\math_lib.c</FilePath>
            </File>
            <File>
              <FileName>mathdp31.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\mathdp31.c</FilePath>
            </File>
            <File>
              <FileName>melp_ana.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\melp_ana.c</FilePath>
            </File>
            <File>
              <FileName>melp_chn.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\melp_chn.c</FilePath>
            </File>
            <File>
              <FileName>melp_sub.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\melp_sub.c</FilePath>
            </File>
            <File>
              <FileName>melp_syn.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\melp_syn.c</FilePath>
            </File>
            <File>
              <FileName>msvq_cb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\msvq_cb.c</FilePath>
            </File>
            <File>
              <FileName>npp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\npp.c</FilePath>
            </File>
            <File>
              <FileName>pit_lib.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\pit_lib.c</FilePath>
            </File>
            <File>
              <FileName>pitch.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\pitch.c</FilePath>
            </File>
            <File>
              <FileName>postfilt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\postfilt.c</FilePath>
            </File>
            <File>
              <FileName>qnt12.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\qnt12.c</FilePath>
            </File>
            <File>
              <FileName>qnt12_cb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\qnt12_cb.c</FilePath>
            </File>
            <File>
              <FileName>sc1200.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\sc1200.c</FilePath>
            </File>
            <File>
              <FileName>transcode.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\transcode.c</FilePath>
            </File>
            <File>
              <FileName>vq_lib.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\vq_lib.c</FilePath>
            </File>
            <File>
              <FileName>mathhalf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\ARM\mathhalf.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>System</GroupName>
          <Files>
            <File>
              <FileName>Retarget.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\ARM\Retarget.c</FilePath>
            </File>
            <File>
              <FileName>Serial.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\ARM\Serial.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::CMSIS</GroupName>
          <Files>
            <File>
              <FileName>arm_cortexM4lf_math.lib</FileName>
              <FileType>4</FileType>
              <FilePath>C:\Keil5.0\ARM\PACK\ARM\CMSIS\3.20.4\CMSIS\Lib\ARM\arm_cortexM4lf_math.lib</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::Device</GroupName>
          <Files>
            <File>
              <FileName>RTE_Device.h</FileName>
              <FileType>5</FileType>
              <FilePath>RTE\Device\STM32F401RE\RTE_Device.h</FilePath>
            </File>
            <File>
              <FileName>startup_stm32f401xx.s</FileName>
              <FileType>2</FileType>
              <FilePath>RTE\Device\STM32F401RE\startup_stm32f401xx.s</FilePath>
            </File>
            <File>
              <FileName>system_stm32f4xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>RTE\Device\STM32F401RE\system_stm32f4xx.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components>
      <component Cclass="CMSIS" Cgroup="CORE" Cvendor="ARM" Cversion="3.20.0" condition="CMSIS Core">
        <package name="CMSIS" schemaVersion="1.0" url="http://www.keil.com/pack/" vendor="ARM" version="3.20.4"/>
        <targetInfos>
          <targetInfo name="MELPE"/>
        </targetInfos>
      </component>
      <component Cclass="CMSIS" Cgroup="DSP" Cvendor="ARM" Cversion="1.4.1" condition="CMSIS DSP">
        <package name="CMSIS" schemaVersion="1.0" url="http://www.keil.com/pack/" vendor="ARM" version="3.20.4"/>
        <targetInfos>
          <targetInfo name="MELPE"/>
        </targetInfos>
      </component>
      <component Cclass="Device" Cgroup="Startup" Cvendor="Keil" Cversion="1.3.0" condition="STM32F4xx CMSIS Device">
        <package name="STM32F4xx_DFP" schemaVersion="1.2" url="http://www.keil.com/pack" vendor="Keil" version="1.0.6"/>
        <targetInfos>
          <targetInfo name="MELPE"/>
        </targetInfos>
      </component>
    </components>
    <files>
      <file attr="config" category="header" name="RTE_Driver\Config\RTE_Device.h" version="1.0.0">
        <instance index="0">RTE\Device\STM32F401RE\RTE_Device.h</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="Keil" Cversion="1.3.0" condition="STM32F4xx CMSIS Device"/>
        <package name="STM32F4xx_DFP" schemaVersion="1.2" url="http://www.keil.com/pack" vendor="Keil" version="1.0.6"/>
        <targetInfos>
          <targetInfo name="MELPE"/>
        </targetInfos>
      </file>
      <file attr="config" category="source" condition="STM32F401" name="Device\Source\ARM\startup_stm32f401xx.s" version="1.0.0">
        <instance index="0">RTE\Device\STM32F401RE\startup_stm32f401xx.s</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="Keil" Cversion="1.3.0" condition="STM32F4xx CMSIS Device"/>
        <package name="STM32F4xx_DFP" schemaVersion="1.2" url="http://www.keil.com/pack" vendor="Keil" version="1.0.6"/>
        <targetInfos>
          <targetInfo name="MELPE"/>
        </targetInfos>
      </file>
      <file attr="config" category="source" name="Device\Source\system_stm32f4xx.c" version="1.0.0">
        <instance index="0">RTE\Device\STM32F401RE\system_stm32f4xx.c</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="Keil" Cversion="1.3.0" condition="STM32F4xx CMSIS Device"/>
        <package name="STM32F4xx_DFP" schemaVersion="1.2" url="http://www.keil.com/pack" vendor="Keil" version="1.0.6"/>
        <targetInfos>
          <targetInfo name="MELPE"/>
        </targetInfos>
      </file>
      <file attr="config" category="header" name="RTE_Driver\Config\RTE_Device.h">
        <instance index="0" removed="1">RTE\Device\STM32F407VG\RTE_Device.h</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="Keil" Cversion="1.0.0" Dname="STM32F407VG" condition="STM32F4xx CMSIS Device"/>
        <package name="STM32F4xx_DFP" schemaVersion="1.0" url="http://www.keil.com/pack" vendor="Keil" version="1.0.5"/>
        <targetInfos/>
      </file>
      <file attr="config" category="source" condition="STM32F40x" name="Device\Source\ARM\startup_stm32f40_41xxx.s" version="1.0.0">
        <instance index="0" removed="1">RTE\Device\STM32F407VG\startup_stm32f40_41xxx.s</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="Keil" Cversion="1.3.0" condition="STM32F4xx CMSIS Device"/>
        <package name="STM32F4xx_DFP" schemaVersion="1.2" url="http://www.keil.com/pack" vendor="Keil" version="1.0.6"/>
        <targetInfos/>
      </file>
      <file attr="config" category="source" condition="STM32F40x" name="Device\Source\ARM\startup_stm32f40xx.s">
        <instance index="0" removed="1">RTE\Device\STM32F407VG\startup_stm32f40xx.s</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="Keil" Cversion="1.0.0" Dname="STM32F407VG" condition="STM32F4xx CMSIS Device"/>
        <package name="STM32F4xx_DFP" schemaVersion="1.0" url="http://www.keil.com/pack" vendor="Keil" version="1.0.5"/>
        <targetInfos/>
      </file>
      <file attr="config" category="source" name="Device\Source\system_stm32f4xx.c">
        <instance index="0" removed="1">RTE\Device\STM32F407VG\system_stm32f4xx.c</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="Keil" Cversion="1.0.0" Dname="STM32F407VG" condition="STM32F4xx CMSIS Device"/>
        <package name="STM32F4xx_DFP" schemaVersion="1.0" url="http://www.keil.com/pack" vendor="Keil" version="1.0.5"/>
        <targetInfos/>
      </file>
    </files>
  </RTE>

</Project>
