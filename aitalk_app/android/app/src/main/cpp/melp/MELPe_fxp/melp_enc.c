/* ================================================================== */
/*                                                                    */ 
/*    Microsoft Speech coder     ANSI-C Source Code                   */
/*    SC1200 1200/2400 bps speech coder                               */
/*    Fixed Point Implementation      Version 7.0                     */
/*    Copyright (C) 2000, Microsoft Corp.                             */
/*    All rights reserved.                                            */
/*                                                                    */ 
/* ================================================================== */

/* ========================================= */
/* melp.c: Mixed Excitation LPC speech coder */
/* ========================================= */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>
#include <stdint.h> // Required for int16_t, int32_t

// Basic types
typedef int16_t Shortword;
typedef int32_t Longword;

#include "sc1200.h"
#include "mat_lib.h"
#include "global.h"
#include "macro.h"
#include "mathhalf.h"
#include "dsp_sub.h"
#include "melp_sub.h"
#include "constant.h"
#include "math_lib.h"
#include "math.h"
#include "transcode.h"

#if NPP
#include "npp.h"
#endif

#define X05_Q7				64         /* 0.5 * (1 << 7) */
#define THREE_Q7			384        /* 3 * (1 << 7) */

/* ====== External memory ====== */

Shortword	mode;
Shortword   chwordsize;

/* ========== Static definations ========== */

#define PROGRAM_NAME			"MELP 1200/2400 bps speech encoder"
#define PROGRAM_VERSION			"Version 7"
#define PROGRAM_DATE			"10/25/2000"

/* ========== Static Variables ========== */

char in_name[1024], out_name[1024];

/* ========== Local Private Prototypes ========== */

static void		parseCommandLine(int argc, char *argv[]);
static void		printHelpMessage(void);

/****************************************************************************
**
** Function:        main
**
** Description:     The main function of the speech coder
**
** Arguments:
**
**  int     argc    ---- number of command line parameters
**  char    *argv[] ---- command line parameters
**
** Return value:    None
**
*****************************************************************************/
int main(int argc, char *argv[])
{
	Longword	length;
	Shortword	speech_in[BLOCK], speech_out[BLOCK];
	Shortword	bitBufSize, bitBufSize12, bitBufSize24;
                                          /* size of the bitstream buffer */
	BOOLEAN		eof_reached = FALSE;
	FILE	*fp_in, *fp_out;

	/* ====== Get input parameters from command line ====== */
	parseCommandLine(argc, argv);

	/* ====== Open input, output, and parameter files ====== */
	if ((fp_in = fopen(in_name,"rb")) == NULL){
		exit(1);
	}
	if ((fp_out = fopen(out_name,"wb")) == NULL){
		exit(1);
	}

	/* ====== Initialize MELP analysis and synthesis ====== */
	if (rate == RATE2400)
		frameSize = (Shortword) FRAME;
	else
		frameSize = (Shortword) BLOCK;
	/* Computing bit=Num = rate * frameSize / FSAMP.  Note that bitNum        */
	/* computes the number of bytes written to the channel and it has to be   */
	/* exact.  We first carry out the division and then have the multiplica-  */
	/* tion with rounding.                                                    */
    bitNum12 = 81;
    bitNum24 = 54;
    if( chwordsize == 8 ){
        // packing the bitstream
        bitBufSize12 = 11;
        bitBufSize24 = 7;
    }else if( chwordsize == 6 ){
        bitBufSize12 = 14;
        bitBufSize24 = 9;
    }else{
        exit(-1);
    }

    if (rate == RATE2400){
		frameSize = FRAME;
		bitBufSize = bitBufSize24;
	} else {
		frameSize = BLOCK;
		bitBufSize = bitBufSize12;
	}

	if (mode != SYNTHESIS)
		melp_ana_init();
	if (mode != ANALYSIS)
		melp_syn_init();


	/* ====== Run MELP coder on input signal ====== */

	frame_count = 0;
	eof_reached = FALSE;
	while (!eof_reached){
		if (mode == DOWN_TRANS){
			/* --- Read 2.4 channel input --- */
            if( chwordsize == 8 ){
			    length = fread(chbuf, sizeof(unsigned char),
						   (bitBufSize24*NF), fp_in);
            }else{
    			int i, readNum;
				unsigned int bitData;
				for(i = 0; i < bitBufSize24*NF; i++){
					readNum = fread(&bitData,sizeof(unsigned int),1,fp_in);
					if( readNum != 1 )	break;
					chbuf[i] = (unsigned char)bitData;
				}	
				length = i;		
            }

			if (length < (bitBufSize24*NF)){
				eof_reached = TRUE;
				break;
			}
			transcode_down();
			/* --- Write 1.2 channel output --- */
            if( chwordsize == 8 ){
			    fwrite(chbuf, sizeof(unsigned char), bitBufSize12, fp_out);
            }else{
				int i;
				unsigned int bitData;
				for(i = 0; i < bitBufSize12; i++){
					bitData = (unsigned int)(chbuf[i]);
					fwrite(&bitData, sizeof(unsigned int), 1, fp_out);
				}
			}
		} else if (mode == UP_TRANS){
			/* --- Read 1.2 channel input --- */
            if( chwordsize == 8 ){
			    length = fread(chbuf, sizeof(unsigned char), bitBufSize12, fp_in);
            }else{
    			int i, readNum;
				unsigned int bitData;
				for(i = 0; i < bitBufSize12; i++){
					readNum = fread(&bitData,sizeof(unsigned int),1,fp_in);
					if( readNum != 1 )	break;
					chbuf[i] = (unsigned char)bitData;
				}	
				length = i;		
            }

			if (length < bitBufSize12){
				eof_reached = TRUE;
				break;
			}
			transcode_up();
			/* --- Write 2.4 channel output --- */
            if( chwordsize == 8 ){
			    fwrite(chbuf, sizeof(unsigned char), (bitBufSize24*NF), fp_out);
            }else{
				int i;
				unsigned int bitData;
				for(i = 0; i < bitBufSize24*NF; i++){
					bitData = (unsigned int)(chbuf[i]);
					fwrite(&bitData, sizeof(unsigned int), 1, fp_out);
				}
			}

		} else {
			/* Perform MELP analysis */
			if (mode != SYNTHESIS){
				/* read input speech */
				length = readbl(speech_in, fp_in, frameSize);
				if (length < frameSize){
					v_zap(&speech_in[length], (Shortword) (FRAME - length));
					eof_reached = TRUE;
				}

				/* ---- Noise Pre-Processor ---- */
#if NPP
				if (rate == RATE1200){
					npp(speech_in, speech_in);
					npp(&(speech_in[FRAME]), &(speech_in[FRAME]));
					npp(&(speech_in[2*FRAME]), &(speech_in[2*FRAME]));
				} else
					npp(speech_in, speech_in);
#endif
				analysis(speech_in, melp_par);

				/* ---- Write channel output if needed ---- */
                if (mode == ANALYSIS){
                    if( chwordsize == 8 ){
					    fwrite(chbuf, sizeof(unsigned char), bitBufSize, fp_out);
                    }else{
        				int i;
		        		unsigned int bitData;
				        for(i = 0; i < bitBufSize; i++){
					        bitData = (unsigned int)(chbuf[i]);
					        fwrite(&bitData, sizeof(unsigned int), 1, fp_out);
				        }
			        }
                }
			}

			/* ====== Perform MELP synthesis (skip first frame) ====== */
			if (mode != ANALYSIS){

				/* Read channel input if needed */
				if (mode == SYNTHESIS){
                    if( chwordsize == 8 ){
					    length = fread(chbuf, sizeof(unsigned char), bitBufSize,
						    		   fp_in);
                    }else{
    		        	int i, readNum;
        				unsigned int bitData;
		        		for(i = 0; i < bitBufSize; i++){
				        	readNum = fread(&bitData,sizeof(unsigned int),1,fp_in);
        					if( readNum != 1 )	break;
		        			chbuf[i] = (unsigned char)bitData;
				        }	
        				length = i;		
                    }
					if (length < bitBufSize){
						eof_reached = TRUE;
						break;
					}
				}
				synthesis(melp_par, speech_out);
				writebl(speech_out, fp_out, frameSize);
			}
		}
		frame_count ++;
	}

	fclose(fp_in);
	fclose(fp_out);

	return(0);
}


/****************************************************************************
**
** Function:        parseCommandLine
**
** Description:     Translate command line parameters
**
** Arguments:
**
**  int     argc    ---- number of command line parameters
**  char    *argv[] ---- command line parameters
**
** Return value:    None
**
*****************************************************************************/
static void		parseCommandLine(int argc, char *argv[])
{
	BOOLEAN		error_flag = FALSE;
	int i;

	/* Setting default values. */
	mode = ANALYSIS;
	rate = RATE1200;
    chwordsize = 8;         // this is for packed bitstream
	in_name[0] = '\0';
	out_name[0] = '\0';

	/* 处理命令行参数 */
	for (i = 1; i < argc; i++) {
		if (strcmp(argv[i], "-r") == 0) {
			/* 处理 -r 参数 */
			i++;
			if (i < argc) {
				if (strcmp(argv[i], "1200") == 0) {
					rate = RATE1200;
				} else if (strcmp(argv[i], "2400") == 0) {
					rate = RATE2400;
				} else {
					error_flag = TRUE;
				}
			} else {
				error_flag = TRUE;
			}
		} else if (in_name[0] == '\0') {
			strncpy(in_name, argv[i], sizeof(in_name)-1);
			in_name[sizeof(in_name)-1] = '\0'; // 确保字符串结束
		} else if (out_name[0] == '\0') {
			strncpy(out_name, argv[i], sizeof(out_name)-1);
			out_name[sizeof(out_name)-1] = '\0'; // 确保字符串结束
		} else {
			error_flag = TRUE;
		}
	}

    if ((in_name[0] == '\0') || (out_name[0] == '\0'))
		error_flag = TRUE;

	if (error_flag) {
		printHelpMessage();
		exit(1);
	}
}


/****************************************************************************
**
** Function:        printHelpMessage
**
** Description:     Print Command Line Usage
**
** Arguments:
**
** Return value:    None
**
*****************************************************************************/
static void		printHelpMessage(void)
{
	exit(1);
}
