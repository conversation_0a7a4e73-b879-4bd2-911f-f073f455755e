#include "melp_wrapper.h"

#include <stdint.h>
#include "sc1200.h"
#include "melp_sub.h"
#include <android/log.h>

#define LOG_TAG "MELP-JNI"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)

// 来自 MELP 源码的外部符号
extern void analysis(int16_t sp_in[], struct melp_param *par);
extern void synthesis(struct melp_param *par, int16_t sp_out[]);
extern void melp_ana_init(void);
extern void melp_syn_init(void);
extern struct melp_param melp_par[];
extern unsigned char chbuf[];

// MELP 打包位宽 (8 bit)
int16_t chwordsize = 8;

static int current_rate = MELP_RATE_2400;

static void apply_rate_parameters(int rate_bps) {
    extern short rate;
    extern int16_t frameSize;
    extern int16_t bitNum12, bitNum24, bitNum;

    if (rate_bps == MELP_RATE_2400) {
        rate = RATE2400;
        frameSize = FRAME;          // 180
        bitNum24 = 54;
        bitNum = bitNum24;
    } else {
        rate = RATE1200;
        frameSize = BLOCK;         // 540
        bitNum12 = 81;
        bitNum = bitNum12;
    }
}

int melp_init(int rate_bps) {
    if (rate_bps != MELP_RATE_2400 && rate_bps != MELP_RATE_1200) {
        return -1;
    }
    current_rate = rate_bps;
    apply_rate_parameters(current_rate);
    melp_ana_init();
    melp_syn_init();
    return 0;
}

int melp_set_rate(int rate_bps) {
    return melp_init(rate_bps);
}

int melp_encode_frame(const int16_t *pcm, uint8_t *bits) {
    if (!pcm || !bits) return -1;

    extern short rate;
    int samples = (rate == RATE2400) ? FRAME : BLOCK;
    int bytes   = (rate == RATE2400) ? 7 : 11;

    // Copy to local buffer (analysis modifies input)
    int16_t speech_in[BLOCK];
    for (int i = 0; i < samples; ++i) speech_in[i] = pcm[i];

    analysis(speech_in, melp_par);

    for (int i = 0; i < bytes; ++i) {
        bits[i] = chbuf[i];
    }

    // debug log
    LOGI("encode_frame bytes=%d first=%02X", bytes, bits[0]);
    return bytes;
}

int melp_decode_frame(const uint8_t *bits, int16_t *pcm) {
    if (!bits || !pcm) return -1;

    extern short rate;
    int samples = (rate == RATE2400) ? FRAME : BLOCK;
    int bytes   = (rate == RATE2400) ? 7 : 11;

    for (int i = 0; i < bytes; ++i) {
        chbuf[i] = bits[i];
    }

    int16_t speech_out[BLOCK];
    synthesis(melp_par, speech_out);

    for (int i = 0; i < samples; ++i) {
        pcm[i] = speech_out[i];
    }

    // debug log
    LOGI("decode_frame samples=%d first=%d", samples, pcm[0]);
    return 0;
} 