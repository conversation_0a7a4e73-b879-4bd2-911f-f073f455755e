import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:aitalk_app/core/ota/ota.dart';

// 生成Mock类
@GenerateMocks([BluetoothDevice])
import 'telink_ota_manager_test.mocks.dart';

void main() {
  group('TelinkOtaManager Tests', () {
    late TelinkOtaManager otaManager;
    late MockBluetoothDevice mockDevice;

    setUp(() {
      otaManager = TelinkOtaManager.instance;
      mockDevice = MockBluetoothDevice();
    });

    tearDown(() {
      otaManager.dispose();
    });

    test('should be singleton', () {
      final instance1 = TelinkOtaManager.instance;
      final instance2 = TelinkOtaManager.instance;
      expect(instance1, same(instance2));
    });

    test('should initialize with idle status', () {
      expect(otaManager.progressNotifier.value.status, OtaStatus.idle);
      expect(otaManager.progressNotifier.value.progress, 0.0);
    });

    test('should validate settings before starting OTA', () async {
      const invalidSettings = OtaSettingsModel(
        filePath: '', // 空路径应该失败
      );

      expect(
        () => otaManager.startOta(mockDevice, invalidSettings),
        throwsA(isA<OtaException>()),
      );
    });

    test('should handle file not found error', () async {
      const settings = OtaSettingsModel(
        filePath: '/non/existent/file.bin',
      );

      expect(
        () => otaManager.startOta(mockDevice, settings),
        throwsA(isA<OtaFileException>()),
      );
    });

    group('OtaSettingsModel Tests', () {
      test('should create with default values', () {
        const settings = OtaSettingsModel();
        expect(settings.protocol, OtaProtocol.legacy);
        expect(settings.versionCompare, true);
        expect(settings.securityBootEnable, false);
        expect(settings.maxRetryCount, 3);
        expect(settings.packetSize, 16);
      });

      test('should validate file path', () {
        const settings = OtaSettingsModel(filePath: '');
        expect(settings.isValid, false);
        expect(settings.validationErrors, contains('文件路径不能为空'));
      });

      test('should validate packet size', () {
        const settings = OtaSettingsModel(packetSize: 0);
        expect(settings.isValid, false);
        expect(settings.validationErrors, contains('数据包大小必须大于0'));
      });

      test('should copy with new values', () {
        const original = OtaSettingsModel();
        final copied = original.copyWith(
          protocol: OtaProtocol.extend,
          packetSize: 20,
        );
        
        expect(copied.protocol, OtaProtocol.extend);
        expect(copied.packetSize, 20);
        expect(copied.versionCompare, original.versionCompare); // 未改变的值应保持
      });
    });

    group('OtaProgress Tests', () {
      test('should create idle progress', () {
        final progress = OtaProgress.idle();
        expect(progress.status, OtaStatus.idle);
        expect(progress.progress, 0.0);
        expect(progress.stepDescription, '等待开始');
      });

      test('should create connecting progress', () {
        final progress = OtaProgress.connecting();
        expect(progress.status, OtaStatus.connecting);
        expect(progress.stepDescription, '连接设备中...');
      });

      test('should calculate percentage correctly', () {
        final progress = OtaProgress.transferring(
          bytesTransferred: 50,
          totalBytes: 100,
          stepDescription: '传输中',
        );
        expect(progress.progressPercentage, '50%');
      });

      test('should format transfer speed', () {
        final progress = OtaProgress.transferring(
          bytesTransferred: 1024,
          totalBytes: 2048,
          stepDescription: '传输中',
          transferSpeed: 1024, // 1KB/s
        );
        expect(progress.transferSpeedString, '1.0 KB/s');
      });
    });

    group('OtaResult Tests', () {
      test('should create success result', () {
        final result = OtaResult.success(
          totalBytes: 1024,
          duration: const Duration(seconds: 10),
        );
        expect(result.isSuccess, true);
        expect(result.shortDescription, '升级成功');
      });

      test('should create failure result', () {
        final result = OtaResult.failure(
          error: OtaResultCode.deviceDisconnected,
          message: '设备断开连接',
        );
        expect(result.isSuccess, false);
        expect(result.shortDescription, '设备断开连接');
      });
    });
  });
}
