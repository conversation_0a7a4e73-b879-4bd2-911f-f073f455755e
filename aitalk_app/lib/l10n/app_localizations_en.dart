// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get autoReconnect_cancel => 'Cancel';

  @override
  String get autoReconnect_message => 'Reconnecting to device...';

  @override
  String get bottomNav_call => 'Calls';

  @override
  String get bottomNav_chat => 'Chats';

  @override
  String get bottomNav_contacts => 'Contacts';

  @override
  String get bottomNav_profile => 'Me';

  @override
  String get callList_searchHint => 'Search';

  @override
  String get callList_title => 'Call History';

  @override
  String get callList_noMembersError => 'Insufficient group members to start a call';

  @override
  String get callList_loadMembersError => 'Failed to load group members';

  @override
  String get callList_selectCallType => 'Select Call Type';

  @override
  String get callList_selectCallTypeDesc => 'This group has 2 members, please select call type:';

  @override
  String get callList_pttTalk => 'PTT Talk';

  @override
  String get callList_voiceCall => 'Voice Call';

  @override
  String get callList_voiceCallNotImplemented => 'Voice call feature is under development...';

  @override
  String get callList_noRecords => 'No call records';

  @override
  String get callList_connected => 'Connected';

  @override
  String get callList_missed => 'Missed';

  @override
  String get callList_outgoing => 'Outgoing';

  @override
  String get callList_yesterday => 'Yesterday';

  @override
  String get voiceCall_calling => 'Calling...';

  @override
  String get voiceCall_ended => 'Call ended';

  @override
  String get voiceCall_demoMessage => 'Voice call feature is under development. This is a demo interface.';

  @override
  String get channelPicker_cancel => 'Cancel';

  @override
  String channelPicker_channel(Object channel) {
    return 'Channel - $channel';
  }

  @override
  String get channelPicker_title => 'Select Channel';

  @override
  String get chatList_connectDevice => 'Connect Device';

  @override
  String chatList_connectionFailed(Object error) {
    return 'Device connection failed: $error';
  }

  @override
  String chatList_connectionSuccess(Object device) {
    return 'Device connected: $device';
  }

  @override
  String get chatList_createGroup => 'Create Group';

  @override
  String get chatList_disconnectDevice => 'Disconnect';

  @override
  String chatList_disconnectFailed(Object error) {
    return 'Disconnect failed: $error';
  }

  @override
  String get chatList_disconnectSuccess => 'Device disconnected';

  @override
  String get chatList_joinGroup => 'Join Group';

  @override
  String get chatList_searchHint => 'Search';

  @override
  String chatList_unpairFailed(Object error) {
    return 'Unpair failed: $error';
  }

  @override
  String get chatList_unpairSuccess => 'Device unpaired';

  @override
  String get createGroup_cancel => 'Cancel';

  @override
  String get createGroup_done => 'Done';

  @override
  String get createGroup_selectChannel => 'Select Channel';

  @override
  String get createGroup_setGroupName => 'Group Name';

  @override
  String get createGroup_setPassword => 'Group Password';

  @override
  String get createGroup_title => 'Create Group';

  @override
  String get deviceInfo_activateTurMass => 'Activate TurMass™';

  @override
  String get deviceInfo_bleVersion => 'Bluetooth Firmware Version';

  @override
  String get deviceInfo_deviceBattery => 'Battery';

  @override
  String get deviceInfo_deviceId => 'Device ID';

  @override
  String get deviceInfo_deviceId_failed => 'Failed to get';

  @override
  String get deviceInfo_deviceId_loading => 'Loading…';

  @override
  String get deviceInfo_deviceInfo => 'Device Info';

  @override
  String get deviceInfo_deviceModel => 'Device Model';

  @override
  String get deviceInfo_deviceName => 'Device Name';

  @override
  String get deviceInfo_deviceOperation => 'Device Operation';

  @override
  String get deviceInfo_deviceVersion => 'Device Version';

  @override
  String get deviceInfo_disconnect => 'Disconnect Device';

  @override
  String get deviceInfo_hwVersion => 'Hardware Version';

  @override
  String get deviceInfo_title => 'Device Info';

  @override
  String get deviceInfo_turMassFirmwareVersion => 'TurMass™ Firmware Version';

  @override
  String get deviceInfo_unpair => 'Unpair Device';

  @override
  String get global_appTitle => 'aiTalk';

  @override
  String get global_cancel => 'Cancel';

  @override
  String get joinGroup_title => 'Join Group';

  @override
  String get notification_showContent => 'Show message content';

  @override
  String get notification_showContent_desc => 'Display message preview in notifications';

  @override
  String get notification_system => 'System notifications';

  @override
  String get notification_system_desc => 'Receive system push notifications';

  @override
  String get notification_voiceCall => 'Voice call notifications';

  @override
  String get notification_voiceCall_desc => 'Notify when there is an incoming voice call';

  @override
  String get profile_avatar => 'Avatar';

  @override
  String get profile_developerMode => 'Developer Mode';

  @override
  String profile_deviceId(Object id) {
    return 'Device ID: $id';
  }

  @override
  String get profile_deviceManagement => 'Device Management';

  @override
  String get profile_language => 'Language';

  @override
  String get profile_languageChinese => 'Chinese';

  @override
  String get profile_languageEnglish => 'English';

  @override
  String get profile_languageSystem => 'System Default';

  @override
  String get profile_myInfo => 'My Info';

  @override
  String get profile_nickname => 'Nickname';

  @override
  String get profile_nickname_default => 'Default User';

  @override
  String get profile_qrcode => 'QR Code';

  @override
  String get profile_qrcode_title => 'My QR Code';

  @override
  String get profile_qrcode_scanHint => 'Scan QR code to add friend';

  @override
  String get profile_qrcode_myInfo => 'My Information';

  @override
  String get profile_qrcode_nickname => 'Nickname';

  @override
  String get profile_qrcode_deviceId => 'Device ID';

  @override
  String get profile_qrcode_channelType => 'Channel Type';

  @override
  String get profile_qrcode_channelNumber => 'Channel Number';

  @override
  String get profile_qrcode_frequency => 'Frequency';

  @override
  String get developer_title => 'Developer Mode';

  @override
  String get developer_bluetoothOta => 'Bluetooth OTA';

  @override
  String get developer_bluetoothOta_desc => 'Device firmware upgrade';

  @override
  String get bluetoothOta_title => 'Bluetooth OTA Upgrade';

  @override
  String get bluetoothOta_selectFile => 'Select Firmware File';

  @override
  String get bluetoothOta_selectFile_desc => 'Please select a .bin firmware file';

  @override
  String get bluetoothOta_deviceInfo => 'Device Information';

  @override
  String get bluetoothOta_currentVersion => 'Current Version';

  @override
  String get bluetoothOta_targetVersion => 'Target Version';

  @override
  String get bluetoothOta_startUpgrade => 'Start Upgrade';

  @override
  String get bluetoothOta_upgrading => 'Upgrading...';

  @override
  String bluetoothOta_progress(Object percent) {
    return 'Progress: $percent%';
  }

  @override
  String get bluetoothOta_success => 'Upgrade Successful';

  @override
  String get bluetoothOta_failed => 'Upgrade Failed';

  @override
  String get bluetoothOta_noDevice => 'No Device Connected';

  @override
  String get bluetoothOta_noFile => 'Please select firmware file first';

  @override
  String get bluetoothOta_fileError => 'File read error';

  @override
  String get profile_qrcode_rateMode => 'Rate Mode';

  @override
  String get profile_qrcode_publicChannel => 'Public Channel';

  @override
  String get profile_qrcode_privateChannel => 'Private Channel';

  @override
  String get profile_qrcode_copyData => 'Copy QR Code Data';

  @override
  String get profile_qrcode_dataCopied => 'QR code data copied to clipboard';

  @override
  String get profile_qrcode_generateFailed => 'Failed to generate QR code';

  @override
  String get profile_qrcode_retry => 'Retry';

  @override
  String get profile_qrcode_noData => 'Unable to generate QR code';

  @override
  String get profile_qrcode_saveImage => 'Save QR Code';

  @override
  String get profile_qrcode_imageSaved => 'QR code saved to gallery';

  @override
  String get profile_qrcode_saveFailed => 'Save failed';

  @override
  String get profile_settings => 'Settings';

  @override
  String get profile_signature => 'Signature';

  @override
  String get profile_userGuide => 'User Guide';

  @override
  String get publicChat_inputHint => 'Type a message…';

  @override
  String get publicChat_pressHold => 'Press and hold to speak';

  @override
  String get publicChat_recording => 'Recording...';

  @override
  String get publicGroup_chatHistory => 'Chat History';

  @override
  String get publicGroup_chatInfo => 'Chat Info';

  @override
  String get publicGroup_deleteHistory => 'Delete History';

  @override
  String get publicGroup_detail_title => 'Group Details';

  @override
  String get publicGroup_editChannel => 'Edit Channel';

  @override
  String get publicGroup_groupName => 'Group Name';

  @override
  String publicGroup_name(Object channel) {
    return 'Public Group - $channel';
  }

  @override
  String get publicGroup_searchHistory => 'Search History';

  @override
  String get privateGroup_cancel => 'Cancel';

  @override
  String get privateGroup_changePassword => 'Change Password';

  @override
  String get privateGroup_chatHistory => 'Chat History';

  @override
  String get privateGroup_chatInfo => 'Chat Info';

  @override
  String get privateGroup_deleteHistory => 'Delete History';

  @override
  String get privateGroup_detail_title => 'Group Details';

  @override
  String get privateGroup_done => 'Done';

  @override
  String get privateGroup_editChannel => 'Edit Channel';

  @override
  String get privateGroup_groupChannel => 'Group Channel';

  @override
  String get privateGroup_groupMembers => 'Group Members';

  @override
  String get privateGroup_groupName => 'Group Name';

  @override
  String get privateGroup_groupPassword => 'Group Password';

  @override
  String get privateGroup_groupQRCode => 'Group QR Code';

  @override
  String get groupQR_title => 'Group QR Code';

  @override
  String get groupQR_scanHint => 'Scan QR code to join group';

  @override
  String get groupQR_groupInfo => 'Group Information';

  @override
  String get groupQR_groupName => 'Group Name';

  @override
  String get groupQR_groupId => 'Group ID';

  @override
  String get groupQR_channel => 'Channel';

  @override
  String get groupQR_password => 'Password';

  @override
  String get groupQR_memberCount => 'Member Count';

  @override
  String get groupQR_noPassword => 'No Password';

  @override
  String get groupQR_saveSuccess => 'Group QR code saved to gallery';

  @override
  String get groupQR_saveFailed => 'Save failed';

  @override
  String get groupQR_generateFailed => 'Failed to generate group QR code';

  @override
  String get groupQR_members => 'Members';

  @override
  String get groupQR_owner => 'Owner';

  @override
  String get groupQR_saveTooltip => 'Save QR Code';

  @override
  String get groupQR_needPermission => 'Gallery access permission required to save image';

  @override
  String get privateGroup_leaveGroup => 'Disband Group';

  @override
  String get privateGroup_searchHistory => 'Search History';

  @override
  String get settings_about => 'About aiTalk';

  @override
  String get settings_chatSettings => 'Chat Settings';

  @override
  String get settings_displaySettings => 'Display Settings';

  @override
  String get settings_fontSize => 'Font Size';

  @override
  String get settings_helpAndFeedback => 'Help & Feedback';

  @override
  String get settings_multilanguage => 'Language';

  @override
  String get settings_notifications => 'Notifications';

  @override
  String get settings_other => 'Others';

  @override
  String get settings_storageManagement => 'Storage Management';

  @override
  String get settings_storageSettings => 'Storage Settings';

  @override
  String get settings_themeDark => 'Dark';

  @override
  String get settings_themeLight => 'Light';

  @override
  String get settings_themeMode => 'Theme Mode';

  @override
  String get settings_themeSystem => 'System Default';

  @override
  String get settings_title => 'Settings';

  @override
  String settings_version(Object version) {
    return 'Version $version';
  }

  @override
  String get settings_voicePlayback => 'Voice Playback';

  @override
  String get voicePlayback_autoPlay => 'Auto play voice';

  @override
  String get voicePlayback_autoPlay_desc => 'Automatically play received voice messages';

  @override
  String get voicePlayback_backgroundMode => 'Background playback mode';

  @override
  String get voicePlayback_backgroundMode_desc => 'Continue playing voice messages when app is in background';

  @override
  String get chatAction_ptt => 'PTT Talk';

  @override
  String get chatAction_voiceCall => 'Voice Call';

  @override
  String get voiceCall_waitingAnswer => 'Waiting for answer...';

  @override
  String get voiceCall_hangUp => 'Hang Up';

  @override
  String get voiceCall_speaker => 'Speaker';

  @override
  String get voiceCall_earpiece => 'Earpiece';

  @override
  String get voiceCall_incomingCall => 'Incoming Call';

  @override
  String get voiceCall_realTimeCall => 'Real-time Call';

  @override
  String get voiceCall_accept => 'Accept';

  @override
  String get voiceCall_reject => 'Reject';

  @override
  String get ptt_noMembers => 'No members';

  @override
  String get storage_totalTitle => 'aiTalk Data';

  @override
  String get storage_cache => 'Cache';

  @override
  String get storage_cacheDesc => 'Cache is used to temporarily store data generated during the app\'s operation, such as images, file previews, and temporary messages. Clearing the cache can free up storage space without affecting your chat history or personal settings.';

  @override
  String get storage_cacheClear => 'Clear';

  @override
  String get storage_cacheCleared => 'Cache cleared';

  @override
  String get storage_chathistory => 'Chat History';

  @override
  String get storage_chathistoryDesc => 'Chat History contains all your conversations with other users, including text, images, and files. Deleting chat history will permanently delete all conversation content and cannot be undone. Please proceed with caution.';

  @override
  String get storage_chathistoryManage => 'Manage';

  @override
  String get storage_appdata => 'App Data';

  @override
  String get storage_appdataDesc => 'App Data refers to the storage space occupied by the application itself, including essential runtime files and model parameters for voice algorithms. These data are required for the app to function properly and cannot be deleted.';

  @override
  String get settings_textSize => 'Text Size';

  @override
  String textSize_demoMessage(Object name) {
    return 'I\'m $name';
  }

  @override
  String get chatList_voicePreview => '[Voice]';

  @override
  String get chatList_locationPreview => '[Location]';

  @override
  String get chatList_messagePreview => '[Message]';

  @override
  String get addContact_title => 'Add Contact';

  @override
  String get addContact_scanGroupQR => 'Scan Group QR Code';

  @override
  String get addContact_scanContactQR => 'Scan Contact QR Code';

  @override
  String get addContact_scanGroupQRDesc => 'Scan group QR code to join group chat';

  @override
  String get addContact_scanContactQRDesc => 'Scan contact QR code to add friend';

  @override
  String get qrScanner_title => 'Scan QR Code';

  @override
  String get qrScanner_scanHint => 'Place QR code within the frame to scan';

  @override
  String get qrScanner_permissionDenied => 'Camera permission denied';

  @override
  String get qrScanner_permissionRequired => 'Camera permission is required to scan QR codes';

  @override
  String get qrScanner_scanFailed => 'Scan failed';

  @override
  String get qrScanner_invalidQRCode => 'Invalid QR code';
}
