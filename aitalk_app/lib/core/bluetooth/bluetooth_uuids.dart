import 'package:flutter_blue_plus/flutter_blue_plus.dart';

// ================== BLE 服务 & 特征 UUID 定义 ==================
// 说明：以下 Guid 与 iOS/Swift 侧保持一致，方便跨平台调用

// Passthrough 直通服务
final Guid PASSTHROUGH_SERVICE_UUID = Guid(
  "00010203-0405-0607-0809-0A0B0C0D1910",
);
// Passthrough – 通知 / 读特征
final Guid READ_NOTIFY_CHARACTERISTIC_UUID = Guid(
  "00010203-0405-0607-0809-0A0B0C0D2B10",
);
// Passthrough – 写特征（不需要响应）
final Guid WRITE_WITHOUT_RESPONSE_CHARACTERISTIC_UUID = Guid(
  "00010203-0405-0607-0809-0A0B0C0D2B11",
);

// 协议解析服务（Protocol Parsing Service）
final Guid PROTOCOL_PARSING_SERVICE_UUID = Guid(
  "11223344-**************-************",
);
// 协议解析 – 写特征（不需要响应）
final Guid PROTOCOL_PARSING_WRITE_CHAR_UUID = Guid(
  "00112233-**************-0A0B0C0D2B11",
);
// 协议解析 – 通知 / 读特征
final Guid PROTOCOL_PARSING_READ_NOTIFY_CHAR_UUID = Guid(
  "00112233-**************-0A0B0C0D2B10",
);

// OTA升级服务（Telink Generic OTA）
final Guid OTA_SERVICE_UUID = Guid("00010203-0405-0607-0809-0a0b0c0d1912");
// OTA特征（读写通知）
final Guid OTA_CHARACTERISTIC_UUID = Guid(
  "00010203-0405-0607-0809-0a0b0c0d2b12",
);

// ---------------- 16-bit Service UUIDs（广播中出现） ----------------
// 0x2022 对应全 128 位形式
final Guid SERVICE_2022_UUID = Guid("00002022-0000-1000-8000-00805F9B34FB");
// 0x18FF 对应全 128 位形式
final Guid SERVICE_18FF_UUID = Guid("000018FF-0000-1000-8000-00805F9B34FB");
