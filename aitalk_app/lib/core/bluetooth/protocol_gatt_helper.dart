// Helper utilities for aiTalk Protocol Parsing GATT interactions
// Handles discovery, notification enabling, frame parsing & caching
// Spec references: SDD §4.1.3.4 and bluetooth_uuids.dart
// Author: Auto-generated

import 'dart:async';
import 'dart:typed_data';

import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:flutter/foundation.dart';

import 'bluetooth_uuids.dart';
import '../protocol/device_control_protocol.dart';

/// Internal cache wrapper per connected device
class _ProtocolGatt {
  _ProtocolGatt({
    required this.writeChar,
    required this.readChar,
    required this.frameStream,
  });

  final BluetoothCharacteristic writeChar;
  final BluetoothCharacteristic readChar;
  final Stream<BTDeviceControlFrame> frameStream; // broadcast stream
}

/// Global helper for all BLE protocol-frame operations.
class ProtocolGattHelper {
  ProtocolGattHelper._();

  // Map<deviceId, _ProtocolGatt>
  static final Map<String, _ProtocolGatt> _cache = {};

  /// Ensure Protocol Parsing service & characteristics are available and
  /// notifications enabled. Returns a cached wrapper for subsequent calls.
  static Future<_ProtocolGatt> _ensure(BluetoothDevice device) async {
    final key = device.remoteId.toString();
    if (_cache.containsKey(key)) return _cache[key]!;

    final services = await device.discoverServices();

    final svc = services.firstWhere(
      (s) => s.uuid == PROTOCOL_PARSING_SERVICE_UUID,
      orElse: () => throw Exception('Protocol Parsing service not found'),
    );

    final writeChar = svc.characteristics.firstWhere(
      (c) => c.uuid == PROTOCOL_PARSING_WRITE_CHAR_UUID,
      orElse: () => throw Exception('Protocol write characteristic not found'),
    );

    final readChar = svc.characteristics.firstWhere(
      (c) => c.uuid == PROTOCOL_PARSING_READ_NOTIFY_CHAR_UUID,
      orElse: () => throw Exception('Protocol notify characteristic not found'),
    );

    await readChar.setNotifyValue(true);

    // 给固件留一点时间真正启用通知，避免紧接着写入时首帧被丢失
    await Future.delayed(const Duration(milliseconds: 150));

    // Broadcast stream of parsed frames.
    final frames = readChar.onValueReceived
        .map((data) {
          if (kDebugMode) {
            final hex = data.map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ');
            debugPrint('⬅️  Protocol frame raw: $hex');
          }
          return BTDeviceControlParser.tryParse(Uint8List.fromList(data));
        })
        .where((f) => f != null) // filter nulls
        .cast<BTDeviceControlFrame>()
        .map((f) {
          if (kDebugMode) {
            debugPrint('⬅️  Parsed frame: $f');
          }
          return f;
        })
        .asBroadcastStream();

    final wrapper = _ProtocolGatt(
      writeChar: writeChar,
      readChar: readChar,
      frameStream: frames,
    );

    // Clean cache on disconnect
    device.connectionState.listen((s) {
      if (s == BluetoothConnectionState.disconnected) {
        _cache.remove(key);
      }
    });

    _cache[key] = wrapper;
    return wrapper;
  }

  /// Send a raw protocol frame to device
  static Future<void> sendFrame(
    BluetoothDevice device,
    Uint8List frame, {
    bool withoutResponse = false,
  }) async {
    final gatt = await _ensure(device);
    // Determine characteristic capabilities
    final props = gatt.writeChar.properties;
    bool canWriteWithRsp = props.write;
    bool canWriteNoRsp = props.writeWithoutResponse;

    // 选择写入模式：
    // 1) 调用方显式指定 withoutResponse -> 强制 NO_RSP
    // 2) 若未指定，且特征支持 writeWithoutResponse，则默认使用 NO_RSP
    // 3) 否则退回到带响应写

    bool useWithoutRsp;
    if (withoutResponse) {
      useWithoutRsp = true;
    } else if (canWriteNoRsp) {
      useWithoutRsp = true;
    } else {
      useWithoutRsp = false;
    }

    if (kDebugMode) {
      final hex = frame.map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ');
      debugPrint('➡️  Send frame (${useWithoutRsp ? 'WR_NO_RSP' : 'WR_RSP'}): $hex');
    }

    await gatt.writeChar.write(frame, withoutResponse: useWithoutRsp);
  }

  /// Stream of parsed protocol frames for a device (broadcast)
  static Future<Stream<BTDeviceControlFrame>> frameStream(
      BluetoothDevice device) async {
    final gatt = await _ensure(device);
    return gatt.frameStream;
  }
} 