import 'package:shared_preferences/shared_preferences.dart';

/// 用于持久化保存“最近一次成功连接的蓝牙设备 ID”
/// 
/// 由于只是一个字符串，这里使用 SharedPreferences 即可满足需求。
class LastDeviceStorage {
  // 保存键名
  static const String _keyLastDeviceId = 'last_device_id';

  /// 保存设备 ID（如 MAC 地址 / UUID）
  static Future<void> save(String deviceId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_keyLastDeviceId, deviceId);
  }

  /// 读取已保存的设备 ID；若不存在则返回 null
  static Future<String?> load() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyLastDeviceId);
  }

  /// 清除记录（例如用户主动“忘记设备”时调用）
  static Future<void> clear() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_keyLastDeviceId);
  }
} 