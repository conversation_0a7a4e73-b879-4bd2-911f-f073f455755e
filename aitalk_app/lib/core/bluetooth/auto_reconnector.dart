import 'dart:async';

import 'package:flutter_blue_plus/flutter_blue_plus.dart';

import 'bluetooth_manager.dart';
import 'last_device_storage.dart';
import 'bluetooth_uuids.dart';

/// 负责 App 启动后的自动重连逻辑，避免 `bluetooth_manager.dart` 体积过大。
/// 
/// 用法示例：
/// ```dart
/// final dev = await AutoReconnector.tryReconnect();
/// if (dev != null) {
///   // 已自动重连成功
/// }
/// ```
class AutoReconnector {
  const AutoReconnector._();

  /// 尝试重连上一次已连接的设备，成功返回对应 [BluetoothDevice]，否则返回 null。
  static Future<BluetoothDevice?> tryReconnect({
    int maxRetries = 5,
    Duration scanTimeout = const Duration(seconds: 4),
    Duration retryDelay = const Duration(seconds: 2),
  }) async {
    // 1. 读取上次设备
    final lastId = await LastDeviceStorage.load();
    if (lastId == null) {
      return null;
    }

    // 2. 确保蓝牙已开启
    await _waitUntilBluetoothOn();

    // 3. 多次扫描并尝试连接
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      final device = await _scanForDevice(lastId, scanTimeout);
      if (device != null) {
        try {
          await BluetoothManager.connect(device);
        } catch (_) {
          // 忽略“已经连接”等异常
        }

        // 连接成功后初始化服务、监听通知等
        await BluetoothManager.initializeDevice(device);
        return device;
      }
      if (attempt < maxRetries) {
        await Future.delayed(retryDelay);
      }
    }
    return null;
  }

  /// 扫描或从系统已连接设备中查找目标设备
  static Future<BluetoothDevice?> _scanForDevice(
      String targetId, Duration scanTimeout) async {
    // (0) 检查当前 App 已连接设备
    final current = await FlutterBluePlus.connectedDevices;
    for (final d in current) {
      if (d.remoteId.toString() == targetId) return d;
    }

    // (0.b) 检查系统层已连接设备
    try {
      final sys = await FlutterBluePlus.systemDevices([PASSTHROUGH_SERVICE_UUID]);
      for (final d in sys) {
        if (d.remoteId.toString() == targetId) return d;
      }
    } catch (_) {}

    BluetoothDevice? foundDevice;
    late final StreamSubscription<List<ScanResult>> sub;
    final completer = Completer<void>();

    sub = BluetoothManager.scanResults.listen((results) {
      for (final r in results) {
        if (r.device.remoteId.toString() == targetId) {
          foundDevice = r.device;
          if (!completer.isCompleted) completer.complete();
          break;
        }
      }
    });

    await BluetoothManager.startScan(timeout: scanTimeout);

    try {
      await completer.future.timeout(scanTimeout);
    } catch (_) {}

    await sub.cancel();
    return foundDevice;
  }

  /// 等待蓝牙适配器开启
  static Future<void> _waitUntilBluetoothOn() async {
    var state = await FlutterBluePlus.adapterStateNow;
    if (state == BluetoothAdapterState.on) return;

    final completer = Completer<void>();
    late final StreamSubscription sub;
    sub = FlutterBluePlus.adapterState.listen((s) {
      if (s == BluetoothAdapterState.on) {
        completer.complete();
        sub.cancel();
      }
    });
    await completer.future;
  }
} 