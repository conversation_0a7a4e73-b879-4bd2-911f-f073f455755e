/// 负责在运行时保持蓝牙连接的后台服务。
///
/// 功能：
/// 1. 监听 [BluetoothManager.currentDevice] 变化；
/// 2. 当设备意外断开时，后台尝试自动重连（可配置最大重试次数等）；
/// 3. 当重新连接成功或用户手动连接时，停止重连逻辑。
///
/// 注意：目前没有区分“用户主动断开”与“意外断开”，
/// 若需要可在 [BluetoothManager.disconnect] 中调用 [ConnectionKeeper.markManualDisconnect]。
///
/// 作者: Auto-generated by AI
import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'bluetooth_manager.dart';
import 'auto_reconnector.dart';

class ConnectionKeeper {
  // ------------------ 单例 ------------------ //
  ConnectionKeeper._internal();
  static final ConnectionKeeper _instance = ConnectionKeeper._internal();
  factory ConnectionKeeper() => _instance;

  // ------------------ Public ------------------ //

  /// 初始化并启动后台监听。应在应用启动时尽早调用一次。
  static void ensureInitialized() {
    _instance._start();
  }

  /// 标记一次“用户主动断开”，使得下一次断开事件不触发自动重连。
  static void markManualDisconnect() {
    _instance._autoReconnectDisabled = true;
  }

  /// 外部可监听此通知器以显示重连提示
  static final ValueNotifier<bool> autoReconnecting = ValueNotifier<bool>(false);

  // ------------------ Internal ------------------ //

  StreamSubscription<BluetoothConnectionState>? _connSub;
  bool _isReconnecting = false;
  // 当用户手动断开后, 禁用自动重连, 直到下一次手动连接成功
  bool _autoReconnectDisabled = false;

  void _start() {
    // 监听 currentDevice 变化
    BluetoothManager.currentDevice.addListener(_onCurrentDeviceChanged);
    // 若启动时就已连接设备，也要订阅其 connectionState
    _onCurrentDeviceChanged();
  }

  void _onCurrentDeviceChanged() {
    // 先清理旧订阅
    _connSub?.cancel();
    _connSub = null;

    final device = BluetoothManager.currentDevice.value;
    if (device == null) return;

    // 一旦有新连接(无论手动或自动), 重新启用自动重连逻辑
    _autoReconnectDisabled = false;

    _connSub = device.connectionState.listen((state) {
      if (state == BluetoothConnectionState.disconnected) {
        _handleDisconnected(device);
      }
    });
  }

  Future<void> _handleDisconnected(BluetoothDevice device) async {
    // 若人为断开，则暂时禁用自动重连, 直到下一次手动连接
    if (_autoReconnectDisabled) {
      return;
    }

    if (_isReconnecting) return; // 已在重连中
    _isReconnecting = true;
    autoReconnecting.value = true;

    try {
      await AutoReconnector.tryReconnect(
        maxRetries: 5,
        scanTimeout: const Duration(seconds: 4),
        retryDelay: const Duration(seconds: 2),
      );
      // 成功与否都会走到 finally, 把标志位清掉
    } finally {
      _isReconnecting = false;
      autoReconnecting.value = false;
    }
  }

  /// 取消当前自动重连并在本次会话内禁用后续自动重连。
  static void cancelAutoReconnect() {
    _instance._autoReconnectDisabled = true;
    if (autoReconnecting.value) {
      autoReconnecting.value = false;
    }
  }
} 