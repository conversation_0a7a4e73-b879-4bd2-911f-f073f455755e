// Helper utilities for PASSTHROUGH GATT service interactions
// Handles AT commands and TK8620 protocol frames communication
// Author: Auto-generated

import 'dart:async';
import 'dart:typed_data';

import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:flutter/foundation.dart';

import 'bluetooth_uuids.dart';

/// Internal cache wrapper per connected device for PASSTHROUGH service
class _PassthroughGatt {
  _PassthroughGatt({
    required this.writeChar,
    required this.readChar,
    required this.responseStream,
  });

  final BluetoothCharacteristic writeChar;
  final BluetoothCharacteristic readChar;
  final Stream<List<int>> responseStream; // broadcast stream for responses
}

/// Global helper for PASSTHROUGH service operations (AT commands & TK8620 frames).
class PassthroughGattHelper {
  PassthroughGattHelper._();

  // Map<deviceId, _PassthroughGatt>
  static final Map<String, _PassthroughGatt> _cache = {};

  /// Ensure PASSTHROUGH service & characteristics are available and
  /// notifications enabled. Returns a cached wrapper for subsequent calls.
  static Future<_PassthroughGatt> _ensure(BluetoothDevice device) async {
    final key = device.remoteId.toString();
    if (_cache.containsKey(key)) return _cache[key]!;

    final services = await device.discoverServices();

    final svc = services.firstWhere(
      (s) => s.uuid == PASSTHROUGH_SERVICE_UUID,
      orElse: () => throw Exception('PASSTHROUGH服务未找到'),
    );

    final writeChar = svc.characteristics.firstWhere(
      (c) => c.uuid == WRITE_WITHOUT_RESPONSE_CHARACTERISTIC_UUID,
      orElse: () => throw Exception('PASSTHROUGH写特征未找到'),
    );

    final readChar = svc.characteristics.firstWhere(
      (c) => c.uuid == READ_NOTIFY_CHARACTERISTIC_UUID,
      orElse: () => throw Exception('PASSTHROUGH读特征未找到'),
    );

    // Enable notifications to receive responses
    await readChar.setNotifyValue(true);

    // Create broadcast stream for responses
    final responseStream = readChar.lastValueStream.asBroadcastStream();

    final gatt = _PassthroughGatt(
      writeChar: writeChar,
      readChar: readChar,
      responseStream: responseStream,
    );

    _cache[key] = gatt;
    return gatt;
  }

  /// Send raw bytes to PASSTHROUGH service write characteristic
  static Future<void> sendBytes(
    BluetoothDevice device,
    List<int> data, {
    bool withoutResponse = true,
  }) async {
    final gatt = await _ensure(device);

    await gatt.writeChar.write(data, withoutResponse: withoutResponse);
  }

  /// Send TK8620 protocol frame to PASSTHROUGH service
  static Future<void> sendFrame(
    BluetoothDevice device,
    Uint8List frame, {
    bool withoutResponse = true,
  }) async {
    await sendBytes(device, frame, withoutResponse: withoutResponse);
    debugPrint('✅ TK8620协议帧已发送到PASSTHROUGH服务');
  }

  /// Send AT command bytes to PASSTHROUGH service
  static Future<void> sendAtCommand(
    BluetoothDevice device,
    Uint8List commandBytes, {
    bool withoutResponse = true,
  }) async {
    await sendBytes(device, commandBytes, withoutResponse: withoutResponse);
    debugPrint('✅ AT指令已发送到PASSTHROUGH服务');
  }

  /// Get response stream for receiving data from PASSTHROUGH service
  static Future<Stream<List<int>>> getResponseStream(
    BluetoothDevice device,
  ) async {
    final gatt = await _ensure(device);
    return gatt.responseStream;
  }

  /// Clean up cached GATT wrapper when device disconnects
  static void cleanup(BluetoothDevice device) {
    final key = device.remoteId.toString();
    _cache.remove(key);
    debugPrint('🧹 已清理设备 $key 的PASSTHROUGH GATT缓存');
  }

  /// Clean up all cached GATT wrappers
  static void cleanupAll() {
    _cache.clear();
    debugPrint('🧹 已清理所有PASSTHROUGH GATT缓存');
  }
}
