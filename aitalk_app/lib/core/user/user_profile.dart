import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 管理当前应用用户资料（目前仅包含昵称）。
class UserProfile {
  UserProfile._() {
    _load();
  }

  static final UserProfile instance = UserProfile._();

  static const String _keyNickname = 'user_nickname';

  /// 昵称通知器；null 表示尚未设置
  final ValueNotifier<String?> nicknameNotifier = ValueNotifier<String?>(null);

  Future<void> _load() async {
    final prefs = await SharedPreferences.getInstance();
    nicknameNotifier.value = prefs.getString(_keyNickname);
  }

  /// 更新昵称并持久化到 SharedPreferences
  Future<void> setNickname(String nickname) async {
    nicknameNotifier.value = nickname;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_keyNickname, nickname);
  }
} 