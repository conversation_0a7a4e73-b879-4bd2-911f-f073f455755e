import 'package:flutter/foundation.dart';

/// 全局会话显示名称管理器，用于在多个页面之间同步会话标题（包含活跃人数等动态信息）。
class ConversationDisplayService {
  ConversationDisplayService._();
  static final ConversationDisplayService instance =
      ConversationDisplayService._();

  /// conversationId -> displayName 映射表的可监听对象。
  final ValueNotifier<Map<String, String>> _namesNotifier = ValueNotifier(
    <String, String>{},
  );

  ValueNotifier<Map<String, String>> get namesNotifier => _namesNotifier;

  /// 更新某个会话的显示名。
  void setDisplayName(String conversationId, String displayName) {
    final Map<String, String> newMap = Map<String, String>.from(
      _namesNotifier.value,
    );
    if (newMap[conversationId] == displayName) return; // 未变化
    newMap[conversationId] = displayName;
    _namesNotifier.value = newMap;
  }

  /// 获取某个会话当前的显示名，若不存在则返回 [fallback]。
  String getDisplayName(String conversationId, String fallback) {
    return _namesNotifier.value[conversationId] ?? fallback;
  }
}
