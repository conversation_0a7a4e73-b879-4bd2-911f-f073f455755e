import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 全局本地通知服务。
///
/// 调用 `NotificationService.instance.init()` 进行初始化；
/// 全局只需初始化一次。后续可调用 [showGroupMessageNotification]
/// 来展示群组消息通知。
class NotificationService {
  NotificationService._();

  static final NotificationService instance = NotificationService._();

  // flutter_local_notifications 插件实例
  final FlutterLocalNotificationsPlugin _plugin = FlutterLocalNotificationsPlugin();

  // 通知开关持久化键
  static const String _kEnabledKey = 'notifications_enabled';

  /// 当前通知开关状态，外部可监听
  final ValueNotifier<bool> enabled = ValueNotifier<bool>(true);

  bool _initialized = false;

  /// 初始化通知服务。
  ///
  /// Android 端会创建频道 `msg_channel` 用于消息通知。
  /// 若已初始化将直接返回。
  Future<void> init() async {
    if (_initialized) return;

    const AndroidInitializationSettings androidInit = AndroidInitializationSettings('@mipmap/ic_launcher');
    const DarwinInitializationSettings iosInit = DarwinInitializationSettings();
    const InitializationSettings initSettings = InitializationSettings(android: androidInit, iOS: iosInit);

    // 初始化本地通知插件
    await _plugin.initialize(initSettings);

    // 读取持久化的开关状态
    final prefs = await SharedPreferences.getInstance();
    enabled.value = prefs.getBool(_kEnabledKey) ?? true;

    // Android: 创建消息通知频道（iOS 不需要显式创建）
    const AndroidNotificationChannel androidChannel = AndroidNotificationChannel(
      'msg_channel',
      'Messages',
      description: 'aiTalk 消息通知通道',
      importance: Importance.high,
    );
    await _plugin.resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()?.
        createNotificationChannel(androidChannel);

    _initialized = true;
  }

  /// 更新通知开关并持久化
  Future<void> setEnabled(bool value) async {
    enabled.value = value;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_kEnabledKey, value);
  }

  /// 展示公共群组消息通知。
  ///
  /// [groupId] 群组 ID，用于生成唯一的通知 ID，避免重复通知。
  /// [message] 消息内容。
  Future<void> showGroupMessageNotification({required String groupId, required String message}) async {
    if (!_initialized) {
      // 若调用方忘记初始化则兜底初始化一次
      try {
        await init();
      } catch (e) {
        debugPrint('[NotificationService] 初始化失败: $e');
        return;
      }
    }

    // 若全局已关闭通知，则直接返回
    if (!enabled.value) return;

    const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      'msg_channel',
      'Messages',
      channelDescription: 'aiTalk 消息通知通道',
      importance: Importance.high,
      priority: Priority.high,
      ticker: 'ticker',
    );
    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails();
    const NotificationDetails platformDetails = NotificationDetails(android: androidDetails, iOS: iosDetails);

    // 使用 groupId 的哈希值作为通知 ID，避免重复创建大量通知导致系统合并
    final int notificationId = groupId.hashCode & 0x7FFFFFFF;

    await _plugin.show(notificationId, 'aiTalk', message, platformDetails);
  }
} 