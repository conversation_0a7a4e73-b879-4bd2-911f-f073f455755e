import 'package:shared_preferences/shared_preferences.dart';

/// 用于持久化保存“当前激活的群组”相关信息。
///
/// 目前仅保存两个字段：
/// 1. [groupId]   群组唯一 ID
/// 2. [creatorId] 群主（创建者）的 deviceId
///
/// 如需扩展，可在此类中继续添加键名并暴露相应的读写接口。
class ActiveGroupStorage {
  ActiveGroupStorage._();

  static const String _keyGroupId = 'active_group_id';
  static const String _keyCreatorId = 'active_group_creator_id';

  /// 保存当前激活的群组信息
  static Future<void> save({
    required String groupId,
    required String creatorId,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_keyGroupId, groupId);
    await prefs.setString(_keyCreatorId, creatorId);
  }

  /// 读取已保存的激活群组信息。
  /// 若没有数据，则返回 null。
  static Future<(String groupId, String creatorId)?> load() async {
    final prefs = await SharedPreferences.getInstance();
    final String? gid = prefs.getString(_keyGroupId);
    final String? cid = prefs.getString(_keyCreatorId);
    if (gid == null || cid == null) return null;
    return (gid, cid);
  }

  /// 获取当前激活的群组ID
  static Future<String?> getGroupId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyGroupId);
  }

  /// 获取当前激活的群组创建者ID
  static Future<String?> getCreatorId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyCreatorId);
  }

  /// 清除记录（例如用户退出登录或主动切换群组时调用）
  static Future<void> clear() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_keyGroupId);
    await prefs.remove(_keyCreatorId);
  }
}
