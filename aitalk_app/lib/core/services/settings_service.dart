import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Handles persisting and retrieving simple UI preferences such as theme mode
/// and text scale factor.
class SettingsService {
  static const _keyThemeMode = 'ui_theme_mode';
  static const _keyTextScale = 'ui_text_scale';
  static const _keyLocale = 'ui_locale';
  // 是否自动播放语音 - 用户可配置
  static const _keyAutoPlayVoice = 'ui_auto_play_voice';
  // 通话音频设置 - 这些设置在App初始化时设为默认值，不再作为用户可配置项
  // static const _keyCallUseEarpieceMode = 'call_use_earpiece_mode';
  // static const _keyCallEnableEchoCancellation = 'call_enable_echo_cancellation';
  // static const _keyCallEnableNoiseSuppression = 'call_enable_noise_suppression';

  SettingsService._();

  /// Saves [mode] to persistent storage.
  static Future<void> saveThemeMode(ThemeMode mode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_keyThemeMode, mode.name);
  }

  /// Saves the chosen text [scale] (e.g. 1.0) to storage.
  static Future<void> saveTextScale(double scale) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble(_keyTextScale, scale);
  }

  /// Reads stored theme mode, returns null if not set.
  static Future<ThemeMode?> loadThemeMode() async {
    final prefs = await SharedPreferences.getInstance();
    final str = prefs.getString(_keyThemeMode);
    if (str == null) return null;
    return ThemeMode.values.firstWhere(
      (e) => e.name == str,
      orElse: () => ThemeMode.system,
    );
  }

  /// Reads stored text scale factor, returns null if not set.
  static Future<double?> loadTextScale() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getDouble(_keyTextScale);
  }

  /// Saves selected locale; null indicates follow system.
  static Future<void> saveLocale(Locale? locale) async {
    final prefs = await SharedPreferences.getInstance();
    if (locale == null) {
      await prefs.remove(_keyLocale);
    } else {
      await prefs.setString(_keyLocale, locale.languageCode);
    }
  }

  /// Loads stored locale. Returns null to indicate follow system or not set.
  static Future<Locale?> loadLocale() async {
    final prefs = await SharedPreferences.getInstance();
    final code = prefs.getString(_keyLocale);
    if (code == null) return null;
    return Locale(code);
  }

  /// Saves whether to auto-play voice messages.
  static Future<void> saveAutoPlayVoice(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_keyAutoPlayVoice, enabled);
  }

  /// Loads stored auto-play voice preference. Returns null if not set.
  static Future<bool?> loadAutoPlayVoice() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_keyAutoPlayVoice);
  }

  // ==================== 通话音频设置 ====================
  // 这些设置已移除，改为在App初始化时使用固定的默认值
  // 不再提供用户配置接口

  /// 获取通话音频的默认设置
  /// 这些设置在App初始化时固定，不再允许用户修改
  static CallAudioSettings getDefaultCallAudioSettings() {
    return const CallAudioSettings(
      useEarpieceMode: true, // 默认使用听筒模式
      enableEchoCancellation: true, // 默认启用回音消除
      enableNoiseSuppression: true, // 默认启用噪音抑制
    );
  }
}

/// 通话音频设置数据类
class CallAudioSettings {
  final bool useEarpieceMode;
  final bool enableEchoCancellation;
  final bool enableNoiseSuppression;

  const CallAudioSettings({
    required this.useEarpieceMode,
    required this.enableEchoCancellation,
    required this.enableNoiseSuppression,
  });
}
