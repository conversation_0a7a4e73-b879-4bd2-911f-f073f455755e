/// 通话历史存储
/// 使用 SharedPreferences 记录哪些群组已通过PTT对讲或实时通话（视为"已接通"）。
/// 支持以下通话类型的记录：
/// 1. 收到过PTT的语音消息就算是进行过通话
/// 2. 发起过实时语音邀请就算是进行过通话
/// 3. 接收过实时语音邀请就算是进行过通话
/// 仅保存会话 conversationId 列表，满足跨页面/跨启动持久化需求。

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 通话类型枚举
enum CallHistoryType {
  pttReceived, // 收到PTT语音消息
  pttSent, // 发送PTT语音消息
  voiceInviteSent, // 发起实时语音邀请
  voiceInviteReceived, // 接收实时语音邀请
}

class CallHistoryStorage {
  CallHistoryStorage._();

  static final CallHistoryStorage instance = CallHistoryStorage._();

  static const String _prefsKey = 'connected_conversation_ids';

  SharedPreferences? _prefs;

  // 当数据变动时递增，用于通知监听者刷新 UI。
  static final ValueNotifier<int> changedNotifier = ValueNotifier<int>(0);

  Future<void> _ensurePrefs() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// 获取已"接通"的会话 ID 集合。
  Future<Set<String>> getConnectedIds() async {
    await _ensurePrefs();
    return _prefs!.getStringList(_prefsKey)?.toSet() ?? <String>{};
  }

  /// 标记会话 [conversationId] 已接通。
  /// [callType] 指定通话类型，用于日志记录
  Future<void> markConnected(
    String conversationId, {
    CallHistoryType? callType,
  }) async {
    await _ensurePrefs();
    final current = await getConnectedIds();

    if (current.add(conversationId)) {
      // 新增群组到通话记录
      await _prefs!.setStringList(_prefsKey, current.toList());
    }

    // 无论是新增还是已存在，都要通知刷新，因为时间可能更新了
    changedNotifier.value++;
  }

  /// 标记收到PTT语音消息的群组为已通话
  Future<void> markPttReceived(String conversationId) async {
    await markConnected(conversationId, callType: CallHistoryType.pttReceived);
  }

  /// 标记发送PTT语音消息的群组为已通话
  Future<void> markPttSent(String conversationId) async {
    await markConnected(conversationId, callType: CallHistoryType.pttSent);
  }

  /// 标记发起实时语音邀请的群组为已通话
  Future<void> markVoiceInviteSent(String conversationId) async {
    await markConnected(
      conversationId,
      callType: CallHistoryType.voiceInviteSent,
    );
  }

  /// 标记接收实时语音邀请的群组为已通话
  Future<void> markVoiceInviteReceived(String conversationId) async {
    await markConnected(
      conversationId,
      callType: CallHistoryType.voiceInviteReceived,
    );
  }
}
