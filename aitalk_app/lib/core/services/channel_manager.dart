/// 信道全局状态管理器
///
/// 用于追踪当前公共广播信道 (1-16)。
/// 所有需要获取/修改当前信道的地方均应通过此类。
/// 注意：私有群或其他业务若使用独立信道，可自行扩展。

import 'package:flutter/foundation.dart';

class ChannelManager {
  ChannelManager._();

  /// 当前信道 (1-16)。默认 1。
  static final ValueNotifier<int> currentChannel = ValueNotifier<int>(1);

  /// 设置当前信道。
  static void setChannel(int channel) {
    if (channel < 1 || channel > 16) return;
    if (currentChannel.value != channel) {
      currentChannel.value = channel;
    }
  }
} 