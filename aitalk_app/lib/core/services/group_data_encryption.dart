import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:pointycastle/export.dart';

import 'group_encryption_key_service.dart';

/// 群组数据加密/解密服务
/// 使用AES-256-CTR模式，确保密文长度与明文长度完全相同
/// 使用确定性计数器，不在密文中包含IV
class GroupDataEncryption {
  GroupDataEncryption._();

  /// 加密群组数据
  ///
  /// [groupId] 群组ID
  /// [plaintext] 要加密的明文数据
  ///
  /// 返回加密后的数据，长度与明文完全相同
  /// 如果加密失败返回null
  static Future<Uint8List?> encrypt(String groupId, Uint8List plaintext) async {
    try {
      // 获取群组加密密钥
      Uint8List? key = await GroupEncryptionKeyService.getEncryptionKey(
        groupId,
      );
      if (key == null) {
        debugPrint('❌ [GroupDataEncryption] 群组密钥不存在: $groupId');

        // 尝试为缺失密钥的群组生成密钥
        final success = await GroupEncryptionKeyService.generateMissingKey(
          groupId,
        );
        if (success) {
          // 重新获取密钥
          key = await GroupEncryptionKeyService.getEncryptionKey(groupId);
        }

        if (key == null) {
          debugPrint('❌ [GroupDataEncryption] 无法获取或生成群组密钥: $groupId');
          return null;
        }
      }

      // 使用AES-CTR模式加密
      final ciphertext = _performAesCtrEncryption(key, plaintext, groupId);
      if (ciphertext == null) {
        debugPrint('❌ [GroupDataEncryption] AES-CTR加密失败');
        return null;
      }

      return ciphertext;
    } catch (e) {
      debugPrint('❌ [GroupDataEncryption] 加密过程异常: $e');
      return null;
    }
  }

  /// 解密群组数据
  ///
  /// [groupId] 群组ID
  /// [ciphertext] 要解密的密文数据
  ///
  /// 返回解密后的明文数据，长度与密文完全相同
  /// 如果解密失败返回null
  static Future<Uint8List?> decrypt(
    String groupId,
    Uint8List ciphertext,
  ) async {
    try {
      // 获取群组加密密钥
      Uint8List? key = await GroupEncryptionKeyService.getEncryptionKey(
        groupId,
      );
      if (key == null) {
        debugPrint('❌ [GroupDataEncryption] 群组密钥不存在: $groupId');

        // 尝试为缺失密钥的群组生成密钥
        final success = await GroupEncryptionKeyService.generateMissingKey(
          groupId,
        );
        if (success) {
          // 重新获取密钥
          key = await GroupEncryptionKeyService.getEncryptionKey(groupId);
        }

        if (key == null) {
          debugPrint('❌ [GroupDataEncryption] 无法获取或生成群组密钥: $groupId');
          return null;
        }
      }

      // 使用AES-CTR模式解密（CTR模式加密和解密是相同操作）
      final plaintext = _performAesCtrEncryption(key, ciphertext, groupId);
      if (plaintext == null) {
        debugPrint('❌ [GroupDataEncryption] AES-CTR解密失败');
        return null;
      }

      return plaintext;
    } catch (e) {
      debugPrint('❌ [GroupDataEncryption] 解密过程异常: $e');
      return null;
    }
  }

  /// 检查数据是否已加密
  ///
  /// 由于加密后长度不变，无法通过长度判断，总是返回false
  /// 需要通过其他方式（如协议标志）来判断数据是否加密
  static bool isEncrypted(Uint8List data) {
    // 长度不变的加密无法通过数据本身判断是否加密
    return false;
  }

  /// 执行AES-CTR加密/解密
  ///
  /// [key] 256位加密密钥
  /// [data] 要处理的数据
  /// [groupId] 群组ID（用于生成确定性计数器）
  ///
  /// 返回处理后的数据，长度与输入相同，失败返回null
  static Uint8List? _performAesCtrEncryption(
    Uint8List key,
    Uint8List data,
    String groupId,
  ) {
    try {
      if (data.isEmpty) return Uint8List(0);

      // 使用群组ID生成确定性计数器（确保相同群组使用相同的计数器）
      final counter = _generateDeterministicCounter(groupId);
      debugPrint(
        '🔢 [CTR] 群组ID: $groupId, 计数器: ${counter.map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ')}',
      );

      // 创建AES-CTR加密器
      final cipher = CTRStreamCipher(AESEngine());
      final params = ParametersWithIV(KeyParameter(key), counter);

      // 初始化加密器
      cipher.init(true, params);

      // 执行加密/解密（CTR模式加密和解密是相同操作）
      final result = Uint8List(data.length);
      cipher.processBytes(data, 0, data.length, result, 0);

      return result;
    } catch (e) {
      debugPrint('❌ [GroupDataEncryption] AES-CTR处理异常: $e');
      return null;
    }
  }

  /// 生成确定性计数器
  ///
  /// 基于群组ID生成固定的计数器，确保相同群组使用相同的计数器
  static Uint8List _generateDeterministicCounter(String groupId) {
    // 使用群组ID的哈希作为计数器
    final groupIdBytes = Uint8List.fromList(groupId.codeUnits);
    final digest = SHA256Digest();
    final hash = digest.process(groupIdBytes);

    // 取前16字节作为计数器
    return hash.sublist(0, 16);
  }

  /// 加密文本消息
  ///
  /// [groupId] 群组ID
  /// [message] 文本消息
  ///
  /// 返回加密后的字节数据，长度与原始UTF-8字节相同，失败返回null
  static Future<Uint8List?> encryptTextMessage(
    String groupId,
    String message,
  ) async {
    try {
      final plaintext = Uint8List.fromList(utf8.encode(message));
      return await encrypt(groupId, plaintext);
    } catch (e) {
      debugPrint('❌ [GroupDataEncryption] 文本消息加密失败: $e');
      return null;
    }
  }

  /// 解密文本消息
  ///
  /// [groupId] 群组ID
  /// [ciphertext] 加密的字节数据
  ///
  /// 返回解密后的文本消息，失败返回null
  static Future<String?> decryptTextMessage(
    String groupId,
    Uint8List ciphertext,
  ) async {
    try {
      final plaintext = await decrypt(groupId, ciphertext);
      if (plaintext == null) return null;

      return utf8.decode(plaintext);
    } catch (e) {
      debugPrint('❌ [GroupDataEncryption] 文本消息解密失败: $e');
      return null;
    }
  }

  /// 加密语音数据
  ///
  /// [groupId] 群组ID
  /// [voiceData] 语音数据字节
  ///
  /// 返回加密后的字节数据，长度与原始数据相同，失败返回null
  static Future<Uint8List?> encryptVoiceData(
    String groupId,
    Uint8List voiceData,
  ) async {
    return await encrypt(groupId, voiceData);
  }

  /// 解密语音数据
  ///
  /// [groupId] 群组ID
  /// [ciphertext] 加密的语音数据
  ///
  /// 返回解密后的语音数据，长度与密文相同，失败返回null
  static Future<Uint8List?> decryptVoiceData(
    String groupId,
    Uint8List ciphertext,
  ) async {
    return await decrypt(groupId, ciphertext);
  }
}
