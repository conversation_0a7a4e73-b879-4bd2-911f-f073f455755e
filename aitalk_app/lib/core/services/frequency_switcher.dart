import '../bluetooth/bluetooth_manager.dart';
import '../constants/frequencies.dart';
import '../protocol/at_commands.dart';
import '../bluetooth/passthrough_gatt_helper.dart';
import 'package:flutter/foundation.dart';

/// 频点切换工具，封装发送 AT+FREQ 指令逻辑。
class FrequencySwitcher {
  FrequencySwitcher._();

  /// 切换到公共群信道 [channel] (1-16)。
  static Future<void> switchPublic(int channel) async {
    final freq = Frequencies.publicChannelFreq(channel);
    await _send(freq, channel);
  }

  /// 切换到私有群信道 [channel] (1-16)。
  static Future<void> switchPrivate(int channel) async {
    final freq = Frequencies.privateChannelFreq(channel);
    await _send(freq, channel, isPublic: false);
  }

  static Future<void> _send(
    int freq,
    int channel, {
    bool isPublic = true,
  }) async {
    final device = BluetoothManager.currentDevice.value;
    if (device == null) {
      debugPrint('[FrequencySwitcher] 未连接设备，无法切换频点');
      return;
    }
    try {
      final bytes = getAtCommandBytes(
        AtCommandType.setFreq,
        params: {
          'txDataFreq': freq,
          'rxDataFreq': freq,
          'txBcnFreq': freq,
          'rxBcnFreq': freq,
        },
      );
      await PassthroughGattHelper.sendAtCommand(
        device,
        bytes,
        withoutResponse: true,
      );
      debugPrint(
        '[FrequencySwitcher] 已发送切换${isPublic ? '公共' : '私有'}频点指令 channel=$channel freq=$freq',
      );
    } catch (e) {
      debugPrint('[FrequencySwitcher] 发送频点指令失败: $e');
    }
  }
}
