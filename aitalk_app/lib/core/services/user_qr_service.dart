import '../models/user_qr_data.dart';
import '../user/user_profile.dart';
import '../device/device_manager.dart';
import '../protocol/at_commands.dart';
import '../protocol/at_response_handler.dart';
import '../bluetooth/bluetooth_manager.dart';
import '../bluetooth/passthrough_gatt_helper.dart';
import '../constants/rate_mode_payload.dart';
import 'dart:async';
import 'package:flutter/foundation.dart';

/// 用户二维码服务
/// 负责生成包含用户频点和速率信息的二维码数据
class UserQrService {
  UserQrService._();

  /// 获取当前用户的二维码数据
  ///
  /// 返回包含以下信息的二维码数据：
  /// - 当前频点（通过AT+FREQ?查询）
  /// - 当前速率模式（通过AT+RATE?查询）
  /// - 用户昵称
  /// - 设备ID
  static Future<UserQrData?> getCurrentUserQrData() async {
    try {
      // 获取用户昵称
      final nickname = UserProfile.instance.nicknameNotifier.value ?? '未知用户';

      // 获取设备ID
      final deviceId = DeviceManager.instance.deviceIdNotifier.value ?? '未知设备';

      // 查询当前频点和速率
      // 优先尝试通过AT指令查询，如果失败则使用fallback值
      final frequency =
          await _queryCurrentFrequency() ?? _getFallbackFrequency();
      final rateMode = await _queryCurrentRate() ?? _getFallbackRateMode();

      return UserQrData(
        frequency: frequency,
        rateMode: rateMode,
        nickname: nickname,
        deviceId: deviceId,
      );
    } catch (e) {
      debugPrint('❌ 获取二维码数据失败: $e');
      return null;
    }
  }

  /// 查询当前频点
  /// 发送AT+FREQ?指令并解析响应
  static Future<int?> _queryCurrentFrequency() async {
    try {
      final device = BluetoothManager.currentDevice.value;
      if (device == null) {
        debugPrint('❌ 未连接设备，无法查询频点');
        return null;
      }

      // 创建等待器
      final completer = Completer<int?>();
      late StreamSubscription sub;

      // 监听AT响应
      sub = AtResponseHandler.instance.responses.listen((res) {
        if (res.type == AtResponseType.freq) {
          // 收到频点响应
          final payload = res.payload;
          if (payload != null) {
            // 从payload中提取第一个频点值
            final txDataFreq = payload['txDataFreq'];
            if (txDataFreq != null) {
              final frequency = int.tryParse(txDataFreq.toString());
              if (frequency != null) {
                debugPrint('✅ 查询到当前频点: $frequency Hz');
                sub.cancel();
                if (!completer.isCompleted) completer.complete(frequency);
                return;
              }
            }
          }
          // 如果解析失败，返回null
          sub.cancel();
          if (!completer.isCompleted) completer.complete(null);
        }
      });

      // 发送AT+FREQ?查询指令
      final cmdBytes = getAtCommandBytes(AtCommandType.queryFreq);
      await PassthroughGattHelper.sendAtCommand(
        device,
        cmdBytes,
        withoutResponse: true,
      );

      // 等待响应或超时
      try {
        final result = await completer.future.timeout(
          const Duration(seconds: 5),
        );
        return result;
      } finally {
        await sub.cancel();
      }
    } catch (e) {
      debugPrint('❌ 查询频点失败: $e');
      return null;
    }
  }

  /// 查询当前速率模式
  /// 发送AT+RATE?指令并解析响应
  static Future<int?> _queryCurrentRate() async {
    try {
      final device = BluetoothManager.currentDevice.value;
      if (device == null) {
        debugPrint('❌ 未连接设备，无法查询速率');
        return null;
      }

      // 创建等待器
      final completer = Completer<int?>();
      late StreamSubscription sub;

      // 监听AT响应
      sub = AtResponseHandler.instance.responses.listen((res) {
        if (res.type == AtResponseType.rate) {
          // 收到速率响应
          final payload = res.payload;
          if (payload != null) {
            // 从payload中提取速率模式值
            final rateModeStr = payload['rateMode'];
            if (rateModeStr != null) {
              // 解析速率字符串，格式可能是 "6,6,6,6"，取第一个值
              final parts = rateModeStr.toString().split(',');
              if (parts.isNotEmpty) {
                final rateMode = int.tryParse(parts[0].trim());
                if (rateMode != null) {
                  debugPrint('✅ 查询到当前速率模式: $rateMode');
                  sub.cancel();
                  if (!completer.isCompleted) completer.complete(rateMode);
                  return;
                }
              }
            }
          }
          // 如果解析失败，返回null
          sub.cancel();
          if (!completer.isCompleted) completer.complete(null);
        }
      });

      // 发送AT+RATE?查询指令
      final cmdBytes = getAtCommandBytes(AtCommandType.queryRate);
      await PassthroughGattHelper.sendAtCommand(
        device,
        cmdBytes,
        withoutResponse: true,
      );

      // 等待响应或超时
      try {
        final result = await completer.future.timeout(
          const Duration(seconds: 5),
        );
        return result;
      } finally {
        await sub.cancel();
      }
    } catch (e) {
      debugPrint('❌ 查询速率失败: $e');
      return null;
    }
  }

  /// 生成二维码字符串
  static Future<String?> generateQrString() async {
    final qrData = await getCurrentUserQrData();
    return qrData?.toQrString();
  }

  /// 获取fallback频点值
  /// 当AT指令查询失败时使用默认值
  static int _getFallbackFrequency() {
    // 使用默认频点 483.6MHz (483600000 Hz)
    return 483600000;
  }

  /// 获取fallback速率模式值
  /// 当AT指令查询失败时使用默认值
  static int _getFallbackRateMode() {
    // 使用当前全局速率模式，如果没有则使用默认值6
    return RateModePayload.currentRateMode;
  }
}
