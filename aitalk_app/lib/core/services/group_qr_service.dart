import '../models/user_qr_data.dart';
import '../services/database_service.dart';
import 'package:flutter/foundation.dart';

/// 群组二维码服务
/// 负责生成包含群组完整信息的二维码数据
class GroupQrService {
  GroupQrService._();

  /// 获取指定群组的二维码数据
  /// [groupId] 群组ID
  static Future<GroupQrData?> getGroupQrData(String groupId) async {
    try {
      final db = await DatabaseService.instance.database;

      // 查询群组信息
      final groupRows = await db.query(
        'groups',
        where: 'group_id = ?',
        whereArgs: [groupId],
        limit: 1,
      );

      if (groupRows.isEmpty) {
        debugPrint('❌ 群组不存在: $groupId');
        return null;
      }

      final group = groupRows.first;
      final groupName = group['group_name'] as String;
      final password = group['password'] as int;
      final channel = group['channel'] as int;

      // 查询群组成员
      final memberRows = await db.query(
        'group_members',
        columns: ['member_id', 'device_id'],
        where: 'group_id = ?',
        whereArgs: [groupId],
        orderBy: 'joined_at ASC',
      );

      // 构建成员映射（memberId -> deviceId）
      final members = <int, String>{};
      for (final row in memberRows) {
        final memberId = row['member_id'] as int?;
        final deviceId = row['device_id'] as String;
        
        if (memberId != null) {
          members[memberId] = deviceId;
        }
      }

      return GroupQrData(
        groupId: groupId,
        groupName: groupName,
        password: password,
        channel: channel,
        members: members,
      );
    } catch (e) {
      debugPrint('❌ 获取群组二维码数据失败: $e');
      return null;
    }
  }

  /// 生成群组二维码字符串
  /// [groupId] 群组ID
  static Future<String?> generateGroupQrString(String groupId) async {
    final qrData = await getGroupQrData(groupId);
    return qrData?.toQrString();
  }

  /// 验证群组二维码数据的有效性
  /// [qrData] 群组二维码数据
  static bool validateGroupQrData(GroupQrData qrData) {
    // 检查必要字段
    if (qrData.groupId.isEmpty || qrData.groupName.isEmpty) {
      return false;
    }

    // 检查信道范围（1-16）
    if (qrData.channel < 1 || qrData.channel > 16) {
      return false;
    }

    // 检查密码范围（0-4294967295，即32位无符号整数）
    if (qrData.password < 0) {
      return false;
    }

    return true;
  }

  /// 从二维码字符串解析群组信息
  /// [qrString] 二维码字符串
  /// 返回解析后的群组数据，如果解析失败返回null
  static GroupQrData? parseGroupQrString(String qrString) {
    final qrData = GroupQrData.fromQrString(qrString);
    
    if (qrData == null) {
      debugPrint('❌ 二维码解析失败');
      return null;
    }

    if (!validateGroupQrData(qrData)) {
      debugPrint('❌ 群组二维码数据无效');
      return null;
    }

    return qrData;
  }

  /// 检查群组是否存在
  /// [groupId] 群组ID
  static Future<bool> groupExists(String groupId) async {
    try {
      final db = await DatabaseService.instance.database;
      final result = await db.query(
        'groups',
        columns: ['group_id'],
        where: 'group_id = ?',
        whereArgs: [groupId],
        limit: 1,
      );
      return result.isNotEmpty;
    } catch (e) {
      debugPrint('❌ 检查群组存在性失败: $e');
      return false;
    }
  }

  /// 获取群组基本信息（不包含成员列表）
  /// [groupId] 群组ID
  static Future<Map<String, dynamic>?> getGroupBasicInfo(String groupId) async {
    try {
      final db = await DatabaseService.instance.database;
      final result = await db.query(
        'groups',
        columns: ['group_name', 'channel', 'password', 'is_private'],
        where: 'group_id = ?',
        whereArgs: [groupId],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return result.first;
      }
      return null;
    } catch (e) {
      debugPrint('❌ 获取群组基本信息失败: $e');
      return null;
    }
  }
}
