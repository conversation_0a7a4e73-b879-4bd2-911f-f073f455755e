/// 频点常量定义
///
/// 约定：
///   • 公共群共有 16 个信道 (1-16)
///   • 私有群共有 16 个信道 (1-16)
///   • 频点范围 470 MHz – 510 MHz，并与公共/私有群交错配置，避免互相干扰。
///
///   分配方案（单位 MHz）：
///   公共群：470.00, 472.50, 475.00, 477.50, …, 507.50
///   私有群：471.25, 473.75, 476.25, 478.75, …, 508.75
///
/// 使用示例：
/// ```dart
/// final freq = Frequencies.publicChannelFreq(3); // 第 3 公共信道 → 475.0
/// ```
class Frequencies {
  Frequencies._();

  /// 公共群 16 个频点（index 0-15 对应信道 1-16）
  static const List<int> publicChannel = [
    470000000,
    472500000,
    475000000,
    477500000,
    480000000,
    482500000,
    485000000,
    487500000,
    490000000,
    492500000,
    495000000,
    497500000,
    500000000,
    502500000,
    505000000,
    507500000,
  ];

  /// 私有群 16 个频点（index 0-15 对应信道 1-16）
  static const List<int> privateChannel = [
    471250000,
    473750000,
    476250000,
    478750000,
    481250000,
    483750000,
    486250000,
    488750000,
    491250000,
    493750000,
    496250000,
    498750000,
    501250000,
    503750000,
    506250000,
    508750000,
  ];

  /// 获取指定公共信道 (1-16) 的频点 (MHz)。
  static int publicChannelFreq(int channel) {
    if (channel < 1 || channel > 16) {
      throw ArgumentError('公共信道必须在 1-16 范围');
    }
    return publicChannel[channel - 1];
  }

  /// 获取指定私有信道 (1-16) 的频点 (MHz)。
  static int privateChannelFreq(int channel) {
    if (channel < 1 || channel > 16) {
      throw ArgumentError('私有信道必须在 1-16 范围');
    }
    return privateChannel[channel - 1];
  }
} 