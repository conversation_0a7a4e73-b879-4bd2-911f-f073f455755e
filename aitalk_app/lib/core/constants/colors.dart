import 'package:flutter/material.dart';

/// 应用颜色定义
class AppColors {
  // 主色和强调色
  static const Color primary = Color(0xFF0070c0); // 主色：蓝色
  static const Color accent = Color(0xFFFF4081); // 强调色：粉色

  // 文本颜色
  static const Color textPrimary = Color(0xFF515151); // 主要文本：深灰
  static const Color textSecondary = Color(0xFF757575); // 次要文本：中灰
  static const Color textHint = Color(0xFF9E9E9E); // 提示文本：浅灰

  // 背景色
  static const Color backgroundPrimary = Color(0xFFFFFFFF); // 主背景：白色
  static const Color backgroundSecondary = Color(0xFFF5F5F5); // 次背景：浅灰

  // 状态颜色
  static const Color success = Color(0xFF548235); // 成功：绿色
  static const Color error = Color(0xFFC40700); // 错误：红色
  static const Color warning = Color(0xFFED7D31); // 警告：橙色
  // 电量黄色
  static const Color batteryMediumHigh = Color(0xFFFFC000); // 黄色

  // 通话相关颜色
  static const Color callIncoming = Color(0xFF00A01D); // 已接：绿色
  static const Color callMissed = Color(0xFFC40700); // 未接：红色
  static const Color callOutgoing = Color(0xFF00067D); // 已拨：蓝色

  // 边框、分割线等
  static const Color divider = Color(0xFFE0E0E0);
  static const Color border = Color(0xFFBDBDBD);

  // 阴影颜色
  static const Color shadow = Color(0x1A000000); // 10%透明度的黑色
}

extension AppThemeColors on BuildContext {
  Color get bgPrimary => Theme.of(this).colorScheme.background;
  Color get bgSecondary {
    final brightness = Theme.of(this).brightness;
    return brightness == Brightness.dark
        ? const Color(0xFF121212)
        : const Color(0xFFF5F5F5);
  }

  Color get tileBackground {
    final theme = Theme.of(this);
    if (theme.brightness == Brightness.dark) {
      // 在暗黑模式下使用纯黑提供与次背景的对比
      return const Color(0xFF000000);
    }
    return theme.colorScheme.surface;
  }

  Color get textPrimaryCol =>
      Theme.of(this).textTheme.bodyMedium?.color ?? AppColors.textPrimary;
  Color get textSecondaryCol =>
      Theme.of(this).textTheme.bodySmall?.color ?? AppColors.textSecondary;

  /// 再浅一级的提示/数值颜色
  Color get textTertiaryCol => textSecondaryCol.withOpacity(0.7);

  /// 根据主题亮度返回品牌主色；在暗黑模式下会适当调亮
  Color get brandPrimary {
    final brightness = Theme.of(this).brightness;
    if (brightness == Brightness.dark) {
      final hsl = HSLColor.fromColor(AppColors.primary);
      // 提高亮度，使其在暗黑主题下更加显眼
      final lightened = hsl.withLightness(
        (hsl.lightness + 0.3).clamp(0.0, 1.0),
      );
      return lightened.toColor();
    }
    return AppColors.primary;
  }
}
