/// TK8620 各速率模式下单包最大协议帧长度
///
/// 参考射频链路规格：
///   - 速率模式范围 3 ~ 7
///   - 数值可根据后续硬件测试/协议文档进行微调
///
/// 注意：此表包含 **整个协议帧** 长度，包括协议头、载荷、CRC等所有开销。
/// 若找不到对应速率模式，将回退到模式 5 的配置。
class RateModePayload {
  RateModePayload._();

  /// 速率模式 → 最大协议帧字节数（包含帧头）
  ///
  /// 备注：根据最新规格，整个协议帧最大160字节
  ///   - Mode 4 : 44B
  ///   - Mode ≥5: 160B
  static const Map<int, int> maxFrameBytes = {4: 44, 5: 160, 6: 160, 7: 160};

  /// 速率模式 → 建议包间隔 (ms)
  static const Map<int, int> interFrameDelayMs = {
    4: 700,
    5: 500,
    6: 400,
    7: 200,
  };

  // ---------------- 当前全局设置 ----------------

  /// 默认速率模式（应用启动后初始化流程会覆盖）
  static int _currentRateMode = 5;

  /// 获取当前速率模式
  static int get currentRateMode => _currentRateMode;

  /// 获取当前模式的建议包间隔 (ms)，默认为 30ms
  static int get currentInterFrameDelay =>
      interFrameDelayMs[_currentRateMode] ?? 30;

  /// 设置当前速率模式，同时返回对应最大帧长度
  static int setCurrentRateMode(int rateMode) {
    if (maxFrameBytes.containsKey(rateMode)) {
      _currentRateMode = rateMode;
    } else {
      // 若非法值则保持不变
    }
    return get(_currentRateMode);
  }

  /// 获取当前模式下的最大协议帧长度
  static int get currentMaxPayload => get(_currentRateMode);

  /// 获取指定速率模式下的最大协议帧长度
  static int get(int rateMode) {
    return maxFrameBytes[rateMode] ?? maxFrameBytes[5]!;
  }
}
