import 'dart:typed_data';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';

/// 语音活动检测器（Voice Activity Detector）
///
/// 用于检测音频帧中是否包含有效的语音信号，避免传输静音或环境噪音
class VoiceActivityDetector {
  VoiceActivityDetector._();

  static final VoiceActivityDetector instance = VoiceActivityDetector._();

  // VAD 参数配置（调整为更敏感的设置，便于正常音量录音）
  static const double _energyThreshold = 300.0; // 能量阈值（降低以提高敏感度）
  static const double _zeroCrossingThreshold = 0.05; // 过零率阈值（降低以更容易检测）
  static const int _minVoiceFrames = 2; // 连续语音帧数阈值（减少以更快响应）
  static const int _minSilenceFrames = 6; // 连续静音帧数阈值（保持不变）
  static const int _hangoverFrames = 3; // 语音结束后的延续帧数（保持不变）

  // 状态变量
  int _consecutiveVoiceFrames = 0;
  int _consecutiveSilenceFrames = 0;
  int _hangoverCounter = 0;
  bool _isVoiceActive = false;

  // 自适应阈值相关
  double _backgroundNoise = 100.0; // 降低初始背景噪音估计
  double _adaptiveEnergyThreshold = _energyThreshold * 0.8; // 初始阈值设置为更敏感
  final List<double> _recentEnergies = [];
  static const int _energyHistorySize = 50;

  // 设备适应性相关
  double _deviceSensitivityFactor = 1.0; // 设备敏感度因子
  int _lowEnergyFrameCount = 0; // 连续低能量帧计数
  int _totalFrameCount = 0; // 总帧数计数

  /// 检测音频帧是否包含语音活动
  ///
  /// [pcmFrame] PCM音频帧数据
  /// 返回true表示检测到语音活动，false表示静音
  bool detectVoiceActivity(Int16List pcmFrame) {
    // 计算音频特征
    final energy = _calculateEnergy(pcmFrame);
    final zeroCrossingRate = _calculateZeroCrossingRate(pcmFrame);

    // 更新设备适应性检测
    _updateDeviceAdaptation(energy);

    // 更新自适应阈值
    _updateAdaptiveThreshold(energy);

    // 基于能量和过零率的语音检测（调整为更严格的条件）
    final hasVoiceFeatures =
        energy > _adaptiveEnergyThreshold &&
        zeroCrossingRate > _zeroCrossingThreshold;

    // 状态机逻辑
    if (hasVoiceFeatures) {
      _consecutiveVoiceFrames++;
      _consecutiveSilenceFrames = 0;
      _hangoverCounter = 0;

      // 连续语音帧数达到阈值，激活语音状态
      if (_consecutiveVoiceFrames >= _minVoiceFrames) {
        _isVoiceActive = true;
      }
    } else {
      _consecutiveVoiceFrames = 0;

      if (_isVoiceActive) {
        // 语音活动中，开始hangover计数
        if (_hangoverCounter < _hangoverFrames) {
          _hangoverCounter++;
          // 在hangover期间仍然认为是语音活动
        } else {
          // hangover结束，开始计算静音帧
          _consecutiveSilenceFrames++;
          if (_consecutiveSilenceFrames >= _minSilenceFrames) {
            _isVoiceActive = false;
            _consecutiveSilenceFrames = 0;
            _hangoverCounter = 0;
          }
        }
      }
    }

    // 调试信息（每5帧输出一次，更频繁的反馈）
    if (_consecutiveVoiceFrames % 5 == 0 ||
        _consecutiveSilenceFrames % 5 == 0 ||
        (_consecutiveVoiceFrames <= 3 && hasVoiceFeatures)) {
      debugPrint(
        '🎤 VAD: 能量=${energy.toStringAsFixed(1)}, '
        '阈值=${_adaptiveEnergyThreshold.toStringAsFixed(1)}, '
        '背景噪音=${_backgroundNoise.toStringAsFixed(1)}, '
        '过零率=${zeroCrossingRate.toStringAsFixed(3)}, '
        '特征=${hasVoiceFeatures ? "检测到" : "无"}, '
        '语音=${_isVoiceActive ? "是" : "否"}',
      );
    }

    return _isVoiceActive;
  }

  /// 计算音频帧的能量
  double _calculateEnergy(Int16List pcmFrame) {
    double energy = 0.0;
    for (int sample in pcmFrame) {
      energy += sample * sample;
    }
    return energy / pcmFrame.length;
  }

  /// 计算过零率（Zero Crossing Rate）
  double _calculateZeroCrossingRate(Int16List pcmFrame) {
    int zeroCrossings = 0;
    for (int i = 1; i < pcmFrame.length; i++) {
      if ((pcmFrame[i] >= 0) != (pcmFrame[i - 1] >= 0)) {
        zeroCrossings++;
      }
    }
    return zeroCrossings / (pcmFrame.length - 1);
  }

  /// 更新自适应能量阈值（每帧都更新，快速适应环境噪音变化）
  void _updateAdaptiveThreshold(double energy) {
    _recentEnergies.add(energy);

    // 保持历史能量数据在指定大小内
    if (_recentEnergies.length > _energyHistorySize) {
      _recentEnergies.removeAt(0);
    }

    // 计算背景噪音水平（需要10帧数据保证稳定性）
    if (_recentEnergies.length >= 10) {
      final sortedEnergies = List<double>.from(_recentEnergies)..sort();

      // 使用更多的低能量样本来估计背景噪音（从1/4增加到1/3）
      final backgroundSampleCount = math.max(1, sortedEnergies.length ~/ 3);
      final backgroundSamples = sortedEnergies
          .take(backgroundSampleCount)
          .toList();
      final newBackgroundNoise =
          backgroundSamples.reduce((a, b) => a + b) / backgroundSamples.length;

      // 使用指数移动平均来平滑背景噪音变化，快速适应但避免突变
      // 如果新噪音比当前噪音大很多，使用更大的平滑因子快速适应
      final noiseDiff = newBackgroundNoise - _backgroundNoise;
      final alpha = noiseDiff > _backgroundNoise * 0.2
          ? 0.3
          : 0.15; // 噪音增大时更快适应
      _backgroundNoise =
          _backgroundNoise * (1 - alpha) + newBackgroundNoise * alpha;

      // 自适应阈值 = 背景噪音 * 倍数 + 基础阈值（调整为更敏感）
      _adaptiveEnergyThreshold = math.max(
        _backgroundNoise * 2.5 + 150.0, // 降低倍数和基础值，提高敏感度
        _energyThreshold * 0.6 * _deviceSensitivityFactor, // 应用设备敏感度因子
      );
    }
  }

  /// 更新设备适应性检测
  void _updateDeviceAdaptation(double energy) {
    _totalFrameCount++;

    // 检测是否为低能量帧（可能表示设备输入音量低）
    if (energy < _energyThreshold * 0.3) {
      _lowEnergyFrameCount++;
    }

    // 每100帧检查一次设备特性
    if (_totalFrameCount % 100 == 0) {
      final lowEnergyRatio = _lowEnergyFrameCount / 100.0;

      // 如果低能量帧比例过高（>80%），可能是设备音量低或麦克风敏感度低
      if (lowEnergyRatio > 0.8) {
        _deviceSensitivityFactor = math.max(
          0.3,
          _deviceSensitivityFactor * 0.9,
        );
        debugPrint(
          '🎤 检测到设备音频输入较低，降低检测阈值，敏感度因子: ${_deviceSensitivityFactor.toStringAsFixed(2)}',
        );
      } else if (lowEnergyRatio < 0.3) {
        // 如果低能量帧比例较低，可能是设备音量正常或较高
        _deviceSensitivityFactor = math.min(
          1.5,
          _deviceSensitivityFactor * 1.05,
        );
      }

      // 重置计数器
      _lowEnergyFrameCount = 0;
    }
  }

  /// 重置VAD状态
  void reset() {
    _consecutiveVoiceFrames = 0;
    _consecutiveSilenceFrames = 0;
    _hangoverCounter = 0;
    _isVoiceActive = false;
    _backgroundNoise = 100.0;
    _adaptiveEnergyThreshold = _energyThreshold * 0.8;
    _recentEnergies.clear();

    // 重置设备适应性相关变量
    _deviceSensitivityFactor = 1.0;
    _lowEnergyFrameCount = 0;
    _totalFrameCount = 0;

    debugPrint('🎤 VAD状态已重置');
  }

  /// 获取当前语音活动状态
  bool get isVoiceActive => _isVoiceActive;

  /// 获取当前自适应阈值
  double get currentThreshold => _adaptiveEnergyThreshold;

  /// 获取背景噪音水平
  double get backgroundNoiseLevel => _backgroundNoise;

  /// 手动设置敏感度（调整基础阈值）
  /// [sensitivity] 敏感度，范围0.1-2.0，值越小越敏感
  void setSensitivity(double sensitivity) {
    sensitivity = sensitivity.clamp(0.1, 2.0);
    _adaptiveEnergyThreshold = _energyThreshold * sensitivity;
    debugPrint('🎤 VAD敏感度已设置为: $sensitivity');
  }
}
