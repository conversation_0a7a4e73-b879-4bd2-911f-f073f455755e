import 'dart:async';

import 'package:flutter/material.dart';

import 'voice_message_processor.dart';

/// PTT 录音统一入口，封装 [VoiceMessageProcessor]，方便跨页面复用。
class PttRecorder {
  PttRecorder._();

  /// 全局单例
  static final PttRecorder instance = PttRecorder._();

  VoiceMessageProcessor? _processor;
  final ValueNotifier<bool> isRecording = ValueNotifier(false);

  Future<void> _ensureProcessor() async {
    _processor ??= await VoiceMessageProcessor.create();
  }

  /// 开始实时录音（若已在录音则忽略）
  Future<void> start() async {
    await _ensureProcessor();
    if (isRecording.value) return;
    await _processor!.startStreamingRecording(autoPlay: false);
    isRecording.value = true;
  }

  /// 停止录音（若未录音则忽略）
  Future<void> stop() async {
    if (!isRecording.value) return;
    await _processor?.stopStreamingRecording();
    isRecording.value = false;
  }

  /// 释放资源
  Future<void> dispose() async {
    await _processor?.dispose();
    _processor = null;
    isRecording.value = false;
  }
}
