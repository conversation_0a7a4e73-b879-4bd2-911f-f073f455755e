/// MELP编解码器使用示例
/// 
/// 展示如何使用MELP编解码器进行音频编解码操作
library melp_example;

import 'dart:typed_data';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'melp_codec.dart';

/// MELP编解码器使用示例类
class MelpExample {
  late MelpAudioCodec _codec;
  bool _initialized = false;

  /// 初始化示例，使用2.4kbps编解码器
  Future<void> init({int rate = 2400}) async {
    try {
      _codec = MelpAudioCodec(rate: rate);
      await _codec.init();
      _initialized = true;
      debugPrint('MELP Example initialized with rate: $rate');
    } catch (e) {
      debugPrint('Failed to initialize MELP Example: $e');
      rethrow;
    }
  }

  /// 基本编解码示例
  Future<void> basicEncodeDecodeExample() async {
    if (!_initialized) {
      throw StateError('MELP Example not initialized');
    }

    debugPrint('=== MELP Basic Encode/Decode Example ===');
    
    // 生成测试音频数据（正弦波）
    final pcmData = _generateSineWave(
      _codec.frameSize, 
      440.0, // 440Hz A音
      8000   // 8kHz采样率
    );
    
    debugPrint('Generated PCM data: ${pcmData.length} samples');
    debugPrint('First 10 samples: ${pcmData.take(10).toList()}');

    // 编码
    final startEncode = DateTime.now();
    final encoded = _codec.encode(pcmData);
    final encodeTime = DateTime.now().difference(startEncode);
    
    debugPrint('Encoded to ${encoded.length} bytes in ${encodeTime.inMicroseconds}μs');
    debugPrint('Encoded data: ${encoded.map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ')}');

    // 解码
    final startDecode = DateTime.now();
    final decoded = _codec.decode(encoded);
    final decodeTime = DateTime.now().difference(startDecode);
    
    debugPrint('Decoded to ${decoded.length} samples in ${decodeTime.inMicroseconds}μs');
    debugPrint('First 10 decoded samples: ${decoded.take(10).toList()}');

    // 计算压缩比
    final originalBytes = pcmData.length * 2; // 16-bit samples
    final compressedBytes = encoded.length;
    final compressionRatio = originalBytes / compressedBytes;
    
    debugPrint('Compression ratio: ${compressionRatio.toStringAsFixed(2)}:1');
    debugPrint('Original size: $originalBytes bytes, Compressed: $compressedBytes bytes');
  }

  /// 比特率切换示例
  Future<void> rateSwitchingExample() async {
    if (!_initialized) {
      throw StateError('MELP Example not initialized');
    }

    debugPrint('=== MELP Rate Switching Example ===');

    // 测试2.4kbps
    debugPrint('Testing 2.4kbps mode:');
    _codec.setRate(2400);
    await _testCurrentRate();

    // 切换到1.2kbps
    debugPrint('Switching to 1.2kbps mode:');
    _codec.setRate(1200);
    await _testCurrentRate();

    // 切换回2.4kbps
    debugPrint('Switching back to 2.4kbps mode:');
    _codec.setRate(2400);
    await _testCurrentRate();
  }

  /// 测试当前比特率设置
  Future<void> _testCurrentRate() async {
    final frameSize = _codec.frameSize;
    final encodedBytes = _codec.encodedBytesPerFrame;
    
    debugPrint('  Frame size: $frameSize samples');
    debugPrint('  Encoded bytes per frame: $encodedBytes');

    // 生成测试数据并编解码
    final pcmData = _generateSineWave(frameSize, 800.0, 8000);
    final encoded = _codec.encode(pcmData);
    final decoded = _codec.decode(encoded);

    debugPrint('  Encode/decode successful: ${encoded.length} bytes -> ${decoded.length} samples');
  }

  /// 性能测试示例
  Future<void> performanceTest({int iterations = 100}) async {
    if (!_initialized) {
      throw StateError('MELP Example not initialized');
    }

    debugPrint('=== MELP Performance Test ($iterations iterations) ===');

    final pcmData = _generateSineWave(_codec.frameSize, 1000.0, 8000);
    
    // 编码性能测试
    final encodeStartTime = DateTime.now();
    List<Uint8List> encodedFrames = [];
    
    for (int i = 0; i < iterations; i++) {
      encodedFrames.add(_codec.encode(pcmData));
    }
    
    final encodeEndTime = DateTime.now();
    final totalEncodeTime = encodeEndTime.difference(encodeStartTime);
    final avgEncodeTime = totalEncodeTime.inMicroseconds / iterations;
    
    debugPrint('Encoding: ${totalEncodeTime.inMilliseconds}ms total, ${avgEncodeTime.toStringAsFixed(1)}μs average');

    // 解码性能测试
    final decodeStartTime = DateTime.now();
    List<Int16List> decodedFrames = [];
    
    for (final encoded in encodedFrames) {
      decodedFrames.add(_codec.decode(encoded));
    }
    
    final decodeEndTime = DateTime.now();
    final totalDecodeTime = decodeEndTime.difference(decodeStartTime);
    final avgDecodeTime = totalDecodeTime.inMicroseconds / iterations;
    
    debugPrint('Decoding: ${totalDecodeTime.inMilliseconds}ms total, ${avgDecodeTime.toStringAsFixed(1)}μs average');

    // 计算实时性能指标
    final frameDurationMs = (_codec.frameSize / 8000) * 1000; // 8kHz采样率
    final encodeRealTimeRatio = avgEncodeTime / 1000 / frameDurationMs;
    final decodeRealTimeRatio = avgDecodeTime / 1000 / frameDurationMs;
    
    debugPrint('Frame duration: ${frameDurationMs.toStringAsFixed(1)}ms');
    debugPrint('Encode real-time ratio: ${encodeRealTimeRatio.toStringAsFixed(3)}');
    debugPrint('Decode real-time ratio: ${decodeRealTimeRatio.toStringAsFixed(3)}');
  }

  /// 错误处理示例
  Future<void> errorHandlingExample() async {
    debugPrint('=== MELP Error Handling Example ===');

    try {
      // 测试未初始化的编解码器
      final uninitCodec = MelpAudioCodec(rate: 2400);
      final testData = Int16List(180);
      uninitCodec.encode(testData);
    } catch (e) {
      debugPrint('Caught expected error for uninitialized codec: $e');
    }

    try {
      // 测试错误的PCM大小
      final wrongSizeData = Int16List(100); // 应该是180或540
      _codec.encode(wrongSizeData);
    } catch (e) {
      debugPrint('Caught expected error for wrong PCM size: $e');
    }

    try {
      // 测试错误的编码数据大小
      final wrongSizeEncoded = Uint8List(5); // 应该是7或11
      _codec.decode(wrongSizeEncoded);
    } catch (e) {
      debugPrint('Caught expected error for wrong encoded size: $e');
    }

    try {
      // 测试无效的比特率
      MelpAudioCodec(rate: 3000);
    } catch (e) {
      debugPrint('Caught expected error for invalid rate: $e');
    }
  }

  /// 运行所有示例
  Future<void> runAllExamples() async {
    await basicEncodeDecodeExample();
    await rateSwitchingExample();
    await performanceTest();
    await errorHandlingExample();
  }

  /// 释放资源
  Future<void> dispose() async {
    if (_initialized) {
      await _codec.dispose();
      _initialized = false;
      debugPrint('MELP Example disposed');
    }
  }

  /// 生成正弦波测试数据
  Int16List _generateSineWave(int length, double frequency, int sampleRate) {
    final pcm = Int16List(length);
    const amplitude = 16384; // 约为最大值的一半
    
    for (int i = 0; i < length; i++) {
      final t = i / sampleRate;
      final sample = amplitude * sin(2 * pi * frequency * t);
      pcm[i] = sample.round();
    }
    
    return pcm;
  }
}

/// 运行MELP示例的便捷函数
Future<void> runMelpExample({int rate = 2400}) async {
  final example = MelpExample();
  
  try {
    await example.init(rate: rate);
    await example.runAllExamples();
  } finally {
    await example.dispose();
  }
}
