/// 音频编解码器统一接口，所有具体实现需继承此类。
///
/// 约定：
/// 1. 输入 PCM 数据格式为 16-bit 单声道整型。
/// 2. `encode` 返回单个音频帧对应的编码后字节包。
/// 3. `decode` 将编码字节包还原成 PCM 数据（帧）。
/// 4. 使用完必须调用 [dispose] 释放原生资源。
///
/// 该文件仅提供接口定义，实际实现见 `opus_codec.dart`.
library audio_codec;

import 'dart:typed_data';

abstract class AudioCodec {
  /// 编码单帧 PCM (Int16) 数据。返回编码后字节序列。
  Uint8List encode(Int16List pcm);

  /// 解码单帧编码数据，返回 PCM (Int16) 数据。
  Int16List decode(Uint8List data);

  /// 释放底层资源。
  Future<void> dispose();
} 