/// 公共群组 ID 相关工具
/// 
/// 约定：
///   公共群使用 4 字节 16 进制标识，
///   范围 0x10000001 - 0x10000010 对应信道 1-16。
///
/// 使用示例：
/// ```dart
/// final id = GroupUtil.publicGroupId(1);      // "10000001"
/// final ch = GroupUtil.channelFromGroupId(id); // 1
/// final isPub = GroupUtil.isPublicGroup(id);   // true
/// ```
class GroupUtil {
  GroupUtil._();

  /// 返回指定信道(1-16)对应的公共群 ID（8 位 16 进制字符串，前缀 0x 可选）。
  static String publicGroupId(int channel, {bool withPrefix = false}) {
    if (channel < 1 || channel > 16) {
      throw ArgumentError('channel must be between 1 and 16');
    }
    final value = 0x10000000 + channel;
    final hex = value.toRadixString(16).padLeft(8, '0').toUpperCase();
    return withPrefix ? '0x$hex' : hex;
  }

  /// 尝试从公共群 ID 解析出信道号。解析失败返回 null。
  static int? channelFromGroupId(String groupId) {
    String id = groupId;
    if (id.startsWith('0x') || id.startsWith('0X')) {
      id = id.substring(2);
    }
    if (id.length != 8) return null;
    // 仅处理以 10 00 00 xx 开头的 ID
    if (!id.startsWith('100000')) return null;
    final lastByteStr = id.substring(6);
    try {
      final val = int.parse(lastByteStr, radix: 16);
      if (val >= 1 && val <= 16) return val;
    } catch (_) {}
    return null;
  }

  /// 判断给定 groupId 是否公共群 ID
  static bool isPublicGroup(String groupId) => channelFromGroupId(groupId) != null;
} 