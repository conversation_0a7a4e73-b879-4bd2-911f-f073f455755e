/// 通话记录数据模型
class CallRecord {
  final String id;
  final String contactName;
  final String? contactAvatar;
  final CallType callType;
  final DateTime callTime;
  final Duration? duration;
  final bool isAnswered;

  const CallRecord({
    required this.id,
    required this.contactName,
    this.contactAvatar,
    required this.callType,
    required this.callTime,
    this.duration,
    required this.isAnswered,
  });

  /// 从Map创建CallRecord实例
  factory CallRecord.fromMap(Map<String, dynamic> map) {
    return CallRecord(
      id: map['id'] as String,
      contactName: map['contactName'] as String,
      contactAvatar: map['contactAvatar'] as String?,
      callType: CallType.values[map['callType'] as int],
      callTime: DateTime.fromMillisecondsSinceEpoch(map['callTime'] as int),
      duration: map['duration'] != null 
          ? Duration(seconds: map['duration'] as int)
          : null,
      isAnswered: map['isAnswered'] as bool,
    );
  }

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'contactName': contactName,
      'contactAvatar': contactAvatar,
      'callType': callType.index,
      'callTime': callTime.millisecondsSinceEpoch,
      'duration': duration?.inSeconds,
      'isAnswered': isAnswered,
    };
  }

  /// 获取通话状态显示文本
  String getCallStatusText() {
    switch (callType) {
      case CallType.incoming:
        return isAnswered ? '已接听' : '未接听';
      case CallType.outgoing:
        return '已拨出';
      case CallType.missed:
        return '未接听';
    }
  }

  /// 获取通话时长显示文本
  String getDurationText() {
    if (duration == null || !isAnswered) {
      return '';
    }
    
    final minutes = duration!.inMinutes;
    final seconds = duration!.inSeconds % 60;
    
    if (minutes > 0) {
      return '${minutes}分${seconds}秒';
    } else {
      return '${seconds}秒';
    }
  }
}

/// 通话类型枚举
enum CallType {
  incoming,  // 来电
  outgoing,  // 去电
  missed,    // 未接
}