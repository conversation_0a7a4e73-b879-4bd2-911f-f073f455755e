import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:async';

import '../services/active_group_storage.dart';
import '../services/database_service.dart';
import '../services/conversation_manager.dart';
import 'tk8620_protocol.dart';
import 'tk8620_frame_decoder.dart';
import '../../views/calls/voice_call_incoming_screen.dart';
import '../../views/calls/voice_call_active_screen.dart';
import '../../main.dart';
import 'at_commands.dart';
import '../bluetooth/bluetooth_manager.dart';
import '../bluetooth/passthrough_gatt_helper.dart';
import 'work_mode_constants.dart';

/// 处理实时通话相关的协议消息
class VoiceCallHandler {
  VoiceCallHandler._();

  /// 处理建立通话请求
  static Future<void> handleCreateTalkRequest(
    TK8620Frame frame,
    TK8620CreateTalkRequest request,
  ) async {
    try {
      debugPrint('📞 处理建立通话请求开始');
      debugPrint(
        '📞 请求方SrcId: 0x${frame.srcId.toRadixString(16).padLeft(2, '0')}',
      );
      debugPrint(
        '📞 目标DstId: 0x${frame.dstId.toRadixString(16).padLeft(8, '0')}',
      );
      debugPrint(
        '📞 会话SessionId: 0x${request.sessionId.toRadixString(16).padLeft(8, '0')}',
      );

      // 1. 检查是否是当前激活的群组
      final activeGroupId = await ActiveGroupStorage.getGroupId();
      if (activeGroupId == null) {
        debugPrint('❌ 没有激活的群组，忽略通话请求');
        return;
      }

      // 将激活群组ID转换为数字进行比较
      int activeGroupIdInt = 0;
      try {
        activeGroupIdInt = int.parse(activeGroupId, radix: 16);
      } catch (e) {
        debugPrint('❌ 激活群组ID格式错误: $activeGroupId');
        return;
      }

      // 检查目标ID是否匹配当前激活群组
      if (frame.dstId != activeGroupIdInt) {
        debugPrint('❌ 目标群组不匹配，忽略通话请求');
        debugPrint(
          '   当前激活群组: 0x${activeGroupIdInt.toRadixString(16).padLeft(8, '0')}',
        );
        debugPrint(
          '   请求目标群组: 0x${frame.dstId.toRadixString(16).padLeft(8, '0')}',
        );
        return;
      }

      // 检查会话ID是否匹配当前激活群组
      if (request.sessionId != activeGroupIdInt) {
        debugPrint('❌ 会话ID不匹配，忽略通话请求');
        debugPrint(
          '   当前激活群组: 0x${activeGroupIdInt.toRadixString(16).padLeft(8, '0')}',
        );
        debugPrint(
          '   请求会话ID: 0x${request.sessionId.toRadixString(16).padLeft(8, '0')}',
        );
        return;
      }

      debugPrint('✅ 通话请求验证通过，准备显示来电界面');

      // 2. 获取群组信息
      final groupName = await _getGroupName(activeGroupId);

      // 3. 显示来电界面
      await _showIncomingCallScreen(
        conversationId: activeGroupId,
        groupName: groupName,
        sessionId: request.sessionId,
        callerSrcId: frame.srcId,
      );
    } catch (e) {
      debugPrint('❌ 处理建立通话请求失败: $e');
    }
  }

  /// 处理建立通话响应
  static Future<void> handleCreateTalkResponse(
    TK8620Frame frame,
    TK8620CreateTalkResponse response,
  ) async {
    try {
      debugPrint('📞 处理建立通话响应开始');
      debugPrint(
        '📞 响应方SrcId: 0x${frame.srcId.toRadixString(16).padLeft(2, '0')}',
      );
      debugPrint('📞 响应码: ${response.responseCode}');
      debugPrint(
        '📞 会话SessionId: 0x${response.sessionId.toRadixString(16).padLeft(8, '0')}',
      );

      // 处理通话响应逻辑
      if (response.responseCode == 0) {
        debugPrint('✅ 对方接听了通话');
        // 跳转到实时通话界面
        await _showActiveCallScreen(
          sessionId: response.sessionId,
          callerSrcId: frame.srcId,
        );
      } else {
        debugPrint('❌ 对方拒绝了通话');
        // 显示拒绝提示并关闭拨通界面
        await _showCallRejectedDialog();
      }
    } catch (e) {
      debugPrint('❌ 处理建立通话响应失败: $e');
    }
  }

  /// 获取群组名称
  static Future<String> _getGroupName(String groupId) async {
    try {
      debugPrint('🔍 查询群组名称: groupId=$groupId');
      final db = await DatabaseService.instance.database;
      final result = await db.query(
        'groups',
        columns: ['group_name'],
        where: 'group_id = ?',
        whereArgs: [groupId],
        limit: 1,
      );

      debugPrint('🔍 查询结果: ${result.length} 条记录');
      if (result.isNotEmpty) {
        final groupName = result.first['group_name'] as String? ?? '未知群组';
        debugPrint('✅ 找到群组名称: $groupName');
        return groupName;
      } else {
        debugPrint('⚠️ 未找到群组记录，使用默认名称');
      }
    } catch (e) {
      debugPrint('❌ 获取群组名称失败: $e');
    }
    return '未知群组';
  }

  /// 显示来电界面
  static Future<void> _showIncomingCallScreen({
    required String conversationId,
    required String groupName,
    required int sessionId,
    required int callerSrcId,
  }) async {
    try {
      // 获取当前导航器上下文
      final context = navigatorKey.currentContext;
      if (context == null) {
        debugPrint('❌ 无法获取导航器上下文，无法显示来电界面');
        return;
      }

      // 使用全屏对话框显示来电界面
      await showDialog(
        context: context,
        barrierDismissible: false, // 不允许点击外部关闭
        builder: (context) => VoiceCallIncomingScreen(
          conversationId: conversationId,
          groupName: groupName,
          sessionId: sessionId,
          callerSrcId: callerSrcId,
        ),
      );
    } catch (e) {
      debugPrint('❌ 显示来电界面失败: $e');
    }
  }

  /// 显示实时通话界面（发送方接听后）
  static Future<void> _showActiveCallScreen({
    required int sessionId,
    required int callerSrcId,
  }) async {
    try {
      // 获取当前激活群组信息
      final activeGroupId = await ActiveGroupStorage.getGroupId();
      if (activeGroupId == null) {
        debugPrint('❌ 没有激活的群组，无法显示通话界面');
        return;
      }

      final groupName = await _getGroupName(activeGroupId);

      // 配置发起方时隙：txdata 30字节 + rxdata 30字节
      await _configureCallerSlots();

      // 获取当前导航器上下文
      final context = navigatorKey.currentContext;
      if (context == null) {
        debugPrint('❌ 无法获取导航器上下文，无法显示通话界面');
        return;
      }

      // 关闭当前可能存在的拨通界面，然后跳转到通话界面
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(
          builder: (context) => VoiceCallActiveScreen(
            conversationId: activeGroupId,
            groupName: groupName,
            sessionId: sessionId,
            callerSrcId: callerSrcId,
          ),
        ),
        (route) => route.isFirst, // 保留根页面
      );
    } catch (e) {
      debugPrint('❌ 显示通话界面失败: $e');
    }
  }

  /// 显示通话被拒绝的对话框
  static Future<void> _showCallRejectedDialog() async {
    try {
      // 获取当前导航器上下文
      final context = navigatorKey.currentContext;
      if (context == null) {
        debugPrint('❌ 无法获取导航器上下文，无法显示拒绝对话框');
        return;
      }

      // 关闭拨通界面
      Navigator.of(context).pop();

      // 显示拒绝提示
      await showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('通话被拒绝'),
          content: const Text('对方拒绝了您的通话请求'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        ),
      );
    } catch (e) {
      debugPrint('❌ 显示拒绝对话框失败: $e');
    }
  }

  /// 配置发起方时隙、工作模式和速率：txdata 30字节 + rxdata 30字节 + 同步主模式 + 速率11
  static Future<void> _configureCallerSlots() async {
    try {
      debugPrint('📡 配置发起方时隙结构、工作模式和速率');

      final device = BluetoothManager.currentDevice.value;
      if (device == null) {
        debugPrint('❌ 没有连接的设备，无法配置时隙');
        return;
      }

      // 1. 配置速率模式为实时通话专用速率
      final rateBytes = getAtCommandBytes(
        AtCommandType.setRate,
        params: {'rateMode': RateMode.realTimeCall},
      );

      await PassthroughGattHelper.sendAtCommand(
        device,
        rateBytes,
        withoutResponse: true,
      );

      debugPrint(
        '✅ 发起方速率模式配置完成: ${RateMode.getDescription(RateMode.realTimeCall)}',
      );

      // 等待速率配置生效
      await Future.delayed(const Duration(milliseconds: 300));

      // 2. 配置工作模式为同步主模式
      final workModeBytes = getAtCommandBytes(
        AtCommandType.workMode,
        params: {'mode': RealTimeCallWorkMode.caller}, // 同步主工作模式
      );

      await PassthroughGattHelper.sendAtCommand(
        device,
        workModeBytes,
        withoutResponse: true,
      );

      debugPrint(
        '✅ 发起方工作模式配置完成: ${WorkMode.getDescription(RealTimeCallWorkMode.caller)}',
      );

      // 等待工作模式配置生效
      await Future.delayed(const Duration(milliseconds: 300));

      // 3. 配置时隙结构：发送数据时隙30字节 + 接收数据时隙30字节
      final slots = [
        {'type': 7, 'length': 48}, // 发送数据时隙，120字节
        {'type': 8, 'length': 48}, // 接收数据时隙，120字节
      ];

      final frameConfigBytes = getAtCommandBytes(
        AtCommandType.frameConfig,
        params: {'slots': slots},
      );

      await PassthroughGattHelper.sendAtCommand(
        device,
        frameConfigBytes,
        withoutResponse: true,
      );

      debugPrint('✅ 发起方时隙配置完成: txdata 30字节, rxdata 30字节');

      // 等待时隙配置生效
      await Future.delayed(const Duration(milliseconds: 300));
    } catch (e) {
      debugPrint('❌ 配置发起方时隙和工作模式失败: $e');
    }
  }
}
