import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import '../services/database_service.dart';
import 'tk8620_frame_decoder.dart';

/// 处理加入会话通知的工具类
/// 当收到其他成员发送的加入会话通知时，更新本地数据库中的群组成员信息
class JoinNotificationHandler {
  JoinNotificationHandler._();

  /// 处理加入会话通知
  /// [notification] 解析后的加入会话通知数据
  static Future<void> handle(TK8620JoinNotification notification) async {
    try {
      debugPrint('👥 处理加入会话通知开始...');
      debugPrint('  - 新成员ID: ${notification.memberId}');
      debugPrint(
        '  - 设备ID: 0x${notification.deviceId.toRadixString(16).padLeft(8, '0')}',
      );
      debugPrint(
        '  - 群组ID: 0x${notification.sessionId.toRadixString(16).padLeft(8, '0')}',
      );

      final db = await DatabaseService.instance.database;
      final int nowMs = DateTime.now().millisecondsSinceEpoch;

      // 将SessionID转换为群组ID格式（使用大写以匹配数据库格式）
      final groupId = notification.sessionId
          .toRadixString(16)
          .padLeft(8, '0')
          .toUpperCase();
      final deviceIdStr =
          '0x${notification.deviceId.toRadixString(16).padLeft(8, '0').toUpperCase()}';

      // 检查群组是否存在
      final groupExists = await _checkGroupExists(db, groupId);
      if (!groupExists) {
        debugPrint('❌ 群组 $groupId 不存在，忽略加入通知');
        return;
      }

      // 检查成员是否已存在
      final memberExists = await _checkMemberExists(db, groupId, deviceIdStr);
      if (memberExists) {
        debugPrint('ℹ️ 成员 $deviceIdStr 已存在于群组 $groupId 中');
        return;
      }

      // 添加新成员到contacts表
      await db.insert('contacts', {
        'device_id': deviceIdStr,
        'nickname': '群成员 - ${notification.memberId}',
        'avatar_index': 0,
        'created_at': nowMs,
        'updated_at': nowMs,
      }, conflictAlgorithm: ConflictAlgorithm.ignore);

      // 添加新成员到group_members表
      await db.insert('group_members', {
        'group_id': groupId,
        'device_id': deviceIdStr,
        'member_id': notification.memberId,
        'joined_at': nowMs,
      }, conflictAlgorithm: ConflictAlgorithm.replace);

      debugPrint('✅ 成功添加新成员到群组:');
      debugPrint('  - 群组: $groupId');
      debugPrint('  - 成员ID: ${notification.memberId}');
      debugPrint('  - 设备ID: $deviceIdStr');

      // 通知UI刷新群组成员列表
      DatabaseService.groupChangedNotifier.value++;
    } catch (e) {
      debugPrint('❌ 处理加入会话通知失败: $e');
    }
  }

  /// 检查群组是否存在
  static Future<bool> _checkGroupExists(Database db, String groupId) async {
    try {
      // 先查询所有群组ID用于调试比较
      final allGroups = await db.query('groups', columns: ['group_id']);
      debugPrint('🔍 数据库中所有群组ID:');
      for (final group in allGroups) {
        final dbGroupId = group['group_id'] as String;
        debugPrint('  - 数据库群组ID: "$dbGroupId" (长度: ${dbGroupId.length})');
      }
      debugPrint('🎯 要查找的群组ID: "$groupId" (长度: ${groupId.length})');

      // 执行具体查询
      final result = await db.query(
        'groups',
        where: 'group_id = ?',
        whereArgs: [groupId],
        limit: 1,
      );

      final exists = result.isNotEmpty;
      debugPrint('📊 查询结果: ${exists ? "找到" : "未找到"} 群组 $groupId');

      return exists;
    } catch (e) {
      debugPrint('❌ 检查群组存在性失败: $e');
      return false;
    }
  }

  /// 检查成员是否已存在
  static Future<bool> _checkMemberExists(
    Database db,
    String groupId,
    String deviceId,
  ) async {
    try {
      final result = await db.query(
        'group_members',
        where: 'group_id = ? AND device_id = ?',
        whereArgs: [groupId, deviceId],
        limit: 1,
      );
      return result.isNotEmpty;
    } catch (e) {
      debugPrint('❌ 检查成员存在性失败: $e');
      return false;
    }
  }
}
