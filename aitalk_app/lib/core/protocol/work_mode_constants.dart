/// TK8620芯片工作模式常量定义
///
/// 该文件定义了AT+WORKMODE指令支持的所有工作模式常量
class WorkMode {
  /// 异步工作模式

  /// 异步只发工作模式
  static const int asyncTransmitOnly = 20;

  /// 异步收发工作模式（默认模式）
  static const int asyncTransceive = 21;

  /// 异步任意速率工作模式
  static const int asyncAnyRate = 22;

  /// 异步终端工作模式
  static const int asyncTerminal = 23;

  /// 异步中继工作模式
  static const int asyncRelay = 24;

  /// 异步带确认工作模式
  static const int asyncWithAck = 25;

  /// 同步工作模式

  /// 同步主工作模式（发起实时通话方使用）
  static const int syncMaster = 31;

  /// 同步从工作模式（接收实时通话方使用）
  static const int syncSlave = 32;

  /// 测试模式

  /// 单TONE测试模式，频率采用BCN发送频率
  static const int singleToneTest = 71;

  /// 灵敏度测试模式，频率采用BCN接收频率，速率采用BCN接收速率
  static const int sensitivityTest = 72;

  /// 透传模式

  /// 开启透传模式
  static const int passthroughEnable = 81;

  /// 关闭透传模式，并进入AT命令模式
  static const int passthroughDisable = 82;

  /// 获取工作模式描述
  static String getDescription(int mode) {
    switch (mode) {
      case asyncTransmitOnly:
        return '异步只发工作模式';
      case asyncTransceive:
        return '异步收发工作模式';
      case asyncAnyRate:
        return '异步任意速率工作模式';
      case asyncTerminal:
        return '异步终端工作模式';
      case asyncRelay:
        return '异步中继工作模式';
      case asyncWithAck:
        return '异步带确认工作模式';
      case syncMaster:
        return '同步主工作模式';
      case syncSlave:
        return '同步从工作模式';
      case singleToneTest:
        return '单TONE测试模式';
      case sensitivityTest:
        return '灵敏度测试模式';
      case passthroughEnable:
        return '开启透传模式';
      case passthroughDisable:
        return '关闭透传模式';
      default:
        return '未知工作模式($mode)';
    }
  }

  /// 检查工作模式是否有效
  static bool isValid(int mode) {
    return [
      asyncTransmitOnly,
      asyncTransceive,
      asyncAnyRate,
      asyncTerminal,
      asyncRelay,
      asyncWithAck,
      syncMaster,
      syncSlave,
      singleToneTest,
      sensitivityTest,
      passthroughEnable,
      passthroughDisable,
    ].contains(mode);
  }

  /// 获取所有有效的工作模式
  static List<int> getAllValidModes() {
    return [
      asyncTransmitOnly,
      asyncTransceive,
      asyncAnyRate,
      asyncTerminal,
      asyncRelay,
      asyncWithAck,
      syncMaster,
      syncSlave,
      singleToneTest,
      sensitivityTest,
      passthroughEnable,
      passthroughDisable,
    ];
  }

  /// 检查是否为异步模式
  static bool isAsyncMode(int mode) {
    return mode >= 20 && mode <= 25;
  }

  /// 检查是否为同步模式
  static bool isSyncMode(int mode) {
    return mode >= 31 && mode <= 32;
  }

  /// 检查是否为测试模式
  static bool isTestMode(int mode) {
    return mode >= 71 && mode <= 72;
  }

  /// 检查是否为透传模式
  static bool isPassthroughMode(int mode) {
    return mode >= 81 && mode <= 82;
  }
}

/// 实时通话相关的工作模式配置
class RealTimeCallWorkMode {
  /// 发起方（主叫）使用的工作模式
  static const int caller = WorkMode.syncMaster;

  /// 接收方（被叫）使用的工作模式
  static const int receiver = WorkMode.syncSlave;

  /// 默认的异步收发模式（非实时通话时使用）
  static const int defaultMode = WorkMode.asyncTransceive;
}

/// 速率模式配置常量
class RateMode {
  /// 默认速率模式（PTT通信使用）
  static const int defaultRate = 6;

  /// 实时通话速率模式
  static const int realTimeCall = 7;

  /// 获取速率模式描述
  static String getDescription(int rate) {
    switch (rate) {
      case defaultRate:
        return '默认速率模式(10)';
      case realTimeCall:
        return '实时通话速率模式(11)';
      default:
        return '速率模式($rate)';
    }
  }
}
