import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'binary_escape.dart';
import 'work_mode_constants.dart';

/// 8620芯片 AT 指令集合
/// 本文件用于集中管理所有发送给 8620 的 AT 指令

/// 支持的AT指令类型
enum AtCommandType {
  /// 版本查询
  versionQuery,

  /// 设备序列号查询
  serialNumberQuery,

  /// 复位
  reset,

  /// 设置频点
  setFreq,

  /// 设置速率
  setRate,

  /// 查询频点
  queryFreq,

  /// 查询速率
  queryRate,

  /// 设置BCNID
  setBcnId,

  /// 设置发射功率
  setTxPower,

  /// 设置附加位
  setAddtl,

  /// LBT指令
  setListen,

  /// OTA写指令
  otaFlash,

  /// OTA升级指令
  otaStart,

  /// 数据发送指令
  sendBinary,

  /// CHALLENGE 指令（安全挑战）
  challenge,

  /// 设置/查询同步工作模式的时隙结构
  frameConfig,

  /// 设置/查询工作模式
  workMode,
}

// ------------------------------------------------------------
// XOR 加密 / 解密相关
// ------------------------------------------------------------

/// 固定16字节密钥（与 iOS/Swift 侧保持一致）
const List<int> _xorKey = <int>[
  0x78,
  0x91,
  0x4A,
  0x18,
  0x8F,
  0xFA,
  0x16,
  0x23,
  0x5F,
  0xFC,
  0x33,
  0x35,
  0xEC,
  0xA7,
  0xC3,
  0x85,
];

/// 对 [data] 做异或加/解密（同一方法既可加密也可解密）。
List<int> _xor(List<int> data) {
  final keyLen = _xorKey.length;
  return List<int>.generate(data.length, (i) => data[i] ^ _xorKey[i % keyLen]);
}

/// 根据命令类型获取**已加密**的字节序列。
/// 对于 [AtCommandType.sendBinary]，不进行加密，直接返回数据
/// （调用方需自行保证 binaryData 已为字节列表）。
Uint8List getAtCommandBytes(
  AtCommandType type, {
  Map<String, dynamic>? params,
}) {
  final cmd = getAtCommand(type, params: params);

  if (type == AtCommandType.sendBinary) {
    // 数据发送指令特殊处理：binaryData 为 Uint8List (纯二进制数据)
    final binaryData = params?['binaryData'];
    if (binaryData == null || binaryData is! Uint8List) {
      throw ArgumentError('sendBinary 需要 binaryData 参数，且必须为 Uint8List');
    }

    // 构造完整字节序列: "AT+SENDB=" + escaped(binaryData) + "\r\n"
    final escapedData = BinaryEscape.escape(binaryData);

    final builder = BytesBuilder()
      ..add('AT+SENDB='.codeUnits)
      ..add(escapedData)
      ..add([0x0D, 0x0A]); // \r\n
    final bytes = builder.toBytes();

    // 打印调试信息（只展示前64字节，避免日志过长）
    final previewLen = bytes.length > 32 ? 32 : bytes.length;
    debugPrint(
      '➡️  AT Command (${type.name}) (preview ${previewLen}B/${bytes.length}B): '
      '${bytes.sublist(0, previewLen).map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ')}${bytes.length > 32 ? ' ...' : ''}',
    );

    return bytes;
  }

  // 打印明文指令
  debugPrint('➡️  AT Command (${type.name}): $cmd');

  // 加密
  final encrypted = _xor(cmd.codeUnits);

  return Uint8List.fromList(encrypted);
}

/// 根据命令类型和参数获取对应的AT指令字符串
/// [params] 仅对有参数的指令有效，具体参数见下方注释
String getAtCommand(AtCommandType type, {Map<String, dynamic>? params}) {
  switch (type) {
    case AtCommandType.versionQuery:
      // 版本查询：查询设备固件版本信息
      // 格式：AT+VER?\r\n
      return 'AT+VER?\r\n';

    case AtCommandType.serialNumberQuery:
      // 设备序列号查询：查询设备唯一序列号
      // 格式：AT+EFUSESN?\r\n
      return 'AT+EFUSESN?\r\n';

    case AtCommandType.reset:
      // 复位：重启设备
      // 格式：AT+RST\r\n
      return 'AT+RST\r\n';

    case AtCommandType.setFreq:
      // 设置频点：配置设备的发送和接收频率
      // 格式：AT+FREQ=(txDataFreq),(rxDataFreq),(txBcnFreq),(rxBcnFreq)\r\n
      final txDataFreq = params?['txDataFreq'];
      final rxDataFreq = params?['rxDataFreq'];
      final txBcnFreq = params?['txBcnFreq'];
      final rxBcnFreq = params?['rxBcnFreq'];
      if ([txDataFreq, rxDataFreq, txBcnFreq, rxBcnFreq].contains(null)) {
        throw ArgumentError(
          'setFreq 需要 txDataFreq, rxDataFreq, txBcnFreq, rxBcnFreq 参数',
        );
      }
      return 'AT+FREQ=$txDataFreq,$rxDataFreq,$txBcnFreq,$rxBcnFreq\r\n';

    case AtCommandType.setRate:
      // 设置速率：配置数据传输速率模式
      // 格式：AT+RATE=(rateMode)\r\n
      final rateMode = params?['rateMode'];
      if (rateMode == null) {
        throw ArgumentError('setRate 需要 rateMode 参数');
      }
      return 'AT+RATE=$rateMode\r\n';

    case AtCommandType.queryFreq:
      // 查询频点：查询当前设备的频率配置
      // 格式：AT+FREQ?\r\n
      return 'AT+FREQ?\r\n';

    case AtCommandType.queryRate:
      // 查询速率：查询当前设备的速率模式
      // 格式：AT+RATE?\r\n
      return 'AT+RATE?\r\n';

    case AtCommandType.setBcnId:
      // 设置BCNID：配置设备的信标ID
      // 格式：AT+BCNID=(bcnID)\r\n
      final bcnID = params?['bcnID'];
      if (bcnID == null) {
        throw ArgumentError('setBcnId 需要 bcnID 参数');
      }
      return 'AT+BCNID=$bcnID\r\n';

    case AtCommandType.setTxPower:
      // 设置发射功率：调整设备的信号发射强度
      // 格式：AT+TXP=(powerIndex)\r\n
      final powerIndex = params?['powerIndex'];
      if (powerIndex == null) {
        throw ArgumentError('setTxPower 需要 powerIndex 参数');
      }
      return 'AT+TXP=$powerIndex\r\n';

    case AtCommandType.setAddtl:
      // 设置附加位：配置附加数据位模式
      // 格式：AT+ADDTL=(mode)\r\n
      final mode = params?['mode'];
      if (mode == null) {
        throw ArgumentError('setAddtl 需要 mode 参数');
      }
      return 'AT+ADDTL=$mode\r\n';

    case AtCommandType.setListen:
      // LBT指令：配置侦听前发射功能参数
      // 格式：AT+LISTEN=(startFrequency),(scanMode),(scanTimes),(channelNumber)\r\n
      final startFrequency = params?['startFrequency'];
      final scanMode = params?['scanMode'];
      final scanTimes = params?['scanTimes'];
      final channelNumber = params?['channelNumber'];
      if ([startFrequency, scanMode, scanTimes, channelNumber].contains(null)) {
        throw ArgumentError(
          'setListen 需要 startFrequency, scanMode, scanTimes, channelNumber 参数',
        );
      }
      return 'AT+LISTEN=$startFrequency,$scanMode,$scanTimes,$channelNumber\r\n';

    case AtCommandType.otaFlash:
      // OTA写指令：向设备写入OTA升级数据
      // 格式：AT+OTAFLASH=(index),(length),(cleanedHexData)\r\n
      final index = params?['index'];
      final length = params?['length'];
      final cleanedHexData = params?['cleanedHexData'];
      if ([index, length, cleanedHexData].contains(null)) {
        throw ArgumentError('otaFlash 需要 index, length, cleanedHexData 参数');
      }
      return 'AT+OTAFLASH=$index,$length,$cleanedHexData\r\n';

    case AtCommandType.otaStart:
      // OTA升级指令：启动OTA升级流程
      // 格式：AT+OTASTART=(major),(minor),(revision),(cleanedCrcHex),(length)\r\n
      final major = params?['major'];
      final minor = params?['minor'];
      final revision = params?['revision'];
      final cleanedCrcHex = params?['cleanedCrcHex'];
      final length = params?['length'];
      if ([major, minor, revision, cleanedCrcHex, length].contains(null)) {
        throw ArgumentError(
          'otaStart 需要 major, minor, revision, cleanedCrcHex, length 参数',
        );
      }
      return 'AT+OTASTART=$major,$minor,$revision,$cleanedCrcHex,$length\r\n';

    case AtCommandType.challenge:
      // CHALLENGE 指令：发送安全挑战十六进制字符串
      // 格式：AT+CHALLENGE=(hexString)\r\n
      final challengeHex = params?['challengeHex'];
      if (challengeHex == null) {
        throw ArgumentError('challenge 需要 challengeHex 参数');
      }
      return 'AT+CHALLENGE=$challengeHex\r\n';

    case AtCommandType.frameConfig:
      // 设置/查询同步工作模式的时隙结构
      // 查询格式：AT+FRAMECFG?\r\n
      // 设置格式：AT+FRAMECFG=<时隙类型>,<时隙长度>,<时隙类型>,<时隙长度>,...\r\n
      final isQuery = params?['isQuery'] ?? false;
      if (isQuery) {
        return 'AT+FRAMECFG?\r\n';
      }

      final slots = params?['slots'] as List<Map<String, int>>?;
      if (slots == null || slots.isEmpty) {
        throw ArgumentError(
          'frameConfig 设置模式需要 slots 参数，格式为 List<Map<String, int>>',
        );
      }

      // 验证时隙参数
      final slotParams = <String>[];
      for (final slot in slots) {
        final type = slot['type'];
        final length = slot['length'];

        if (type == null || length == null) {
          throw ArgumentError('每个时隙必须包含 type 和 length 参数');
        }

        // 验证时隙类型
        if (![6, 7, 8].contains(type)) {
          throw ArgumentError('时隙类型必须为 6(空闲)、7(发送)、8(接收) 之一');
        }

        // 验证时隙长度
        if (type == 6) {
          // 空闲时隙：1024~4294967295 微秒
          if (length < 1024 || length > 4294967295) {
            throw ArgumentError('空闲时隙长度必须在 1024~4294967295 微秒范围内');
          }
        } else {
          // 数据时隙：1~600 字节
          if (length < 1 || length > 600) {
            throw ArgumentError('数据时隙长度必须在 1~600 字节范围内');
          }
        }

        slotParams.add('$type,$length');
      }

      return 'AT+FRAMECFG=${slotParams.join(',')}\r\n';

    case AtCommandType.workMode:
      // 设置/查询工作模式
      // 查询格式：AT+WORKMODE?\r\n
      // 设置格式：AT+WORKMODE=<工作模式>\r\n
      final isQuery = params?['isQuery'] ?? false;
      if (isQuery) {
        return 'AT+WORKMODE?\r\n';
      }

      final mode = params?['mode'];
      if (mode == null) {
        throw ArgumentError('workMode 设置模式需要 mode 参数');
      }

      // 验证工作模式参数
      if (!WorkMode.isValid(mode)) {
        final validModes = WorkMode.getAllValidModes();
        throw ArgumentError('无效的工作模式: $mode，有效值为: $validModes');
      }

      return 'AT+WORKMODE=$mode\r\n';

    case AtCommandType.sendBinary:
      // 数据发送指令：发送二进制数据
      // 格式：AT+SENDB=<二进制数据>\r\n
      final binaryData = params?['binaryData'];
      if (binaryData == null) {
        throw ArgumentError('sendBinary 需要 binaryData 参数');
      }
      // 注意：这里假设binaryData已经是正确格式的二进制数据
      return 'AT+SENDB=$binaryData\r\n';

    default:
      throw UnimplementedError('暂不支持的AT指令类型: $type');
  }
}
