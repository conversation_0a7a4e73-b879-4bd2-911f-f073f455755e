import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';

import 'package:pointycastle/export.dart';
import 'package:convert/convert.dart' as convert;

import 'at_commands.dart';

/// Helper for building and verifying TK8620 +CHALLENGE AT command.
/// Algorithm implementation is kept 1:1 with the reference Python script
/// (doc/challenge(1).py) to guarantee interoperability.
class ChallengeCommand {
  /// Fixed public key used by TK8620 firmware (SEC P-256).
  /// This is the same 64-byte big-endian value as in the Python script.
  static const String _pkHex =
      'ACB211BD84A8C15C7399B581AA772F4F290589F26AB8FADDFD2BC96DDA101E42'
      'B64C000BA41953225BB3AF67A72A38E7BC98DC1BD2DE6DE5C60B25C893CF9AC6';

  /// PK bytes cached (uncompressed raw X||Y = 64 bytes).
  static final Uint8List _pkBytes = Uint8List.fromList(convert.hex.decode(_pkHex));

  /// Default timezone offset for timestamp (UTC+8).
  static const int _tzOffsetSeconds = 8 * 3600;

  /// Generates the 32-byte challenge payload.
  /// Structure: (24 random bytes XOR pk[0:24]) | little-endian 64-bit timestamp.
  /// [nowSeconds] and [offsetSeconds] are primarily for unit-testing.
  static Uint8List buildChallengeBytes({
    int? nowSeconds,
    int offsetSeconds = _tzOffsetSeconds,
    Random? random,
  }) {
    // 24 secure random bytes
    final rnd = Uint8List(24);
    final rng = random ?? Random.secure();
    for (int i = 0; i < 24; i++) {
      rnd[i] = rng.nextInt(256);
    }

    // XOR with first 24 bytes of fixed public key
    final xorRnd = Uint8List(24);
    for (int i = 0; i < 24; i++) {
      xorRnd[i] = rnd[i] ^ _pkBytes[i];
    }

    // Timestamp (seconds since epoch, little-endian)
    final ts = (nowSeconds ?? DateTime.now().millisecondsSinceEpoch ~/ 1000) +
        offsetSeconds;
    final tsBytes = _uint64LittleEndian(ts);

    return Uint8List.fromList([...xorRnd, ...tsBytes]);
  }

  /// Returns hexadecimal uppercase string of challenge bytes.
  static String buildChallengeHex({
    int? nowSeconds,
    int offsetSeconds = _tzOffsetSeconds,
    Random? random,
  }) {
    final bytes = buildChallengeBytes(
      nowSeconds: nowSeconds,
      offsetSeconds: offsetSeconds,
      random: random,
    );
    return _toHex(bytes);
  }

  /// Produces full AT command string: 'AT+CHALLENGE=<hex>\r\n'.
  static Uint8List buildEncryptedBytes({
    int? nowSeconds,
    int offsetSeconds = _tzOffsetSeconds,
    Random? random,
  }) {
    final hexStr = buildChallengeHex(
      nowSeconds: nowSeconds,
      offsetSeconds: offsetSeconds,
      random: random,
    );
    // 利用统一的 AT 指令加密流程
    return getAtCommandBytes(
      AtCommandType.challenge,
      params: {'challengeHex': hexStr},
    );
  }


  /// Verifies signature returned by TK8620.
  /// [sig] is expected to be 64 raw bytes (r|s) big-endian.
  /// [challenge] must be the exact 32-byte payload that was sent.
  static bool verifySignature(Uint8List sig, Uint8List challenge) {
    if (sig.length != 64) return false;
    if (challenge.length > 32) {
      throw ArgumentError('Challenge length >32 not supported');
    }

    // Firmware pads/zero-extends challenge to 32 bytes before hashing;
    // we replicate this behaviour and feed "hash" directly in verify.
    final hashBuf = Uint8List(32)..setRange(0, challenge.length, challenge);

    final ecDomain = ECDomainParameters('secp256r1');
    final q = ecDomain.curve.decodePoint(Uint8List.fromList([0x04, ..._pkBytes]))!;
    final pubKey = ECPublicKey(q, ecDomain);

    final signer = ECDSASigner(null, HMac(SHA256Digest(), 64));
    signer.init(false, PublicKeyParameter<ECPublicKey>(pubKey));

    final r = BigInt.parse(_toHex(sig.sublist(0, 32)), radix: 16);
    final s = BigInt.parse(_toHex(sig.sublist(32)), radix: 16);

    return signer.verifySignature(hashBuf, ECSignature(r, s));
  }

  /// Parses modem reply lines and extracts signature/chipinfo bytes.
  /// Returns (sigBytes, chipinfoBytes). If not present they will be null.
  static ({Uint8List? sig, Uint8List? chipinfo}) parseReplyLines(Iterable<String> lines) {
    Uint8List? sig;
    Uint8List? chipinfo;
    for (final l in lines) {
      if (l.startsWith('+CHALLENGE:')) {
        final hexStr = l.split(':')[1].trim();
        sig = Uint8List.fromList(convert.hex.decode(hexStr));
      } else if (l.startsWith('+CHIPINFO:')) {
        final hexStr = l.split(':')[1].trim();
        chipinfo = Uint8List.fromList(convert.hex.decode(hexStr));
      }
    }
    return (sig: sig, chipinfo: chipinfo);
  }

  // --------------------------------- Helpers --------------------------------
  static Uint8List _uint64LittleEndian(int v) {
    final b = ByteData(8);
    b.setUint64(0, v, Endian.little);
    return b.buffer.asUint8List();
  }

  static String _toHex(Uint8List bytes) =>
      bytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join().toUpperCase();
}
