import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'dart:typed_data';
import 'sn_parser.dart';
import 'binary_escape.dart';

/// 8620 AT 指令响应类别
enum AtResponseType {
  version,
  serialNumber,
  deviceId,
  freq,
  rate,
  bcnId,
  addtl,
  listen,
  frameConfig,
  workMode,
  diData,
  ok,
  error,
  unknown,
}

/// 解析后的响应对象
class AtResponse {
  AtResponse({required this.type, required this.raw, this.payload});

  final AtResponseType type;
  final String raw;
  final Map<String, dynamic>? payload;
}

/// 负责解析并广播 8620 指令响应
class AtResponseHandler {
  AtResponseHandler._();
  static final AtResponseHandler instance = AtResponseHandler._();

  final StreamController<AtResponse> _controller =
      StreamController<AtResponse>.broadcast();

  // (保持单一职责，不在此处解析 TK8620 协议)

  Stream<AtResponse> get responses => _controller.stream;

  /// 入口：传入一行解密后的响应字符串
  void handle(String msg) {
    if (msg.trim().isEmpty) return;

    final res = _parse(msg.trim());
    _controller.add(res);

    // 调试输出 (对 diData 仅打印十六进制字符串, 隐藏 raw Uint8List)
    String display;
    if (res.type == AtResponseType.diData) {
      display = res.payload?['data'] ?? '<no data>';
      debugPrint('📥 Parsed Response: ${res.type} -> $display');
      debugPrint('[AtResponseHandler] 广播diData响应到监听器');
    } else {
      display = (res.payload ?? res.raw).toString();
      debugPrint('📥 Parsed Response: ${res.type} -> $display');
    }
  }

  /// 新增：直接处理原始字节数据的+DI消息，避免字符串转换导致的数据丢失
  void handleDiRawBytes(Uint8List rawBytes) {
    // 查找 "Data " 的字节序列位置
    final dataPattern = [0x44, 0x61, 0x74, 0x61, 0x20]; // "Data "
    int dataStartIdx = -1;

    for (int i = 0; i <= rawBytes.length - dataPattern.length; i++) {
      bool match = true;
      for (int j = 0; j < dataPattern.length; j++) {
        if (rawBytes[i + j] != dataPattern[j]) {
          match = false;
          break;
        }
      }
      if (match) {
        dataStartIdx = i + dataPattern.length;
        break;
      }
    }

    if (dataStartIdx == -1) {
      debugPrint('[AtResponseHandler] 未找到Data字段');
      return;
    }

    // 提取数据部分，移除末尾的\r\n
    Uint8List dataBytes = rawBytes.sublist(dataStartIdx);

    // 移除末尾的\r\n字节
    while (dataBytes.isNotEmpty &&
        (dataBytes.last == 0x0D || dataBytes.last == 0x0A)) {
      dataBytes = dataBytes.sublist(0, dataBytes.length - 1);
    }

    // 反转义
    final unescaped = BinaryEscape.unescape(dataBytes);

    final dataHex = unescaped
        .map((b) => b.toRadixString(16).padLeft(2, '0'))
        .join(' ')
        .toUpperCase();

    final res = AtResponse(
      type: AtResponseType.diData,
      raw: latin1.decode(rawBytes, allowInvalid: true),
      payload: {'data': dataHex, 'raw': unescaped},
    );

    _controller.add(res);
    debugPrint('📥 Parsed Response (Raw): ${res.type} -> $dataHex');
  }

  AtResponse _parse(String msg) {
    if (msg == 'AT_OK') {
      return AtResponse(type: AtResponseType.ok, raw: msg);
    }
    if (msg.startsWith('AT_ERROR')) {
      return AtResponse(type: AtResponseType.error, raw: msg);
    }
    if (msg.startsWith('+VER')) {
      String? version;
      final idx = msg.indexOf(':');
      if (idx != -1 && msg.length > idx + 1) {
        // 从冒号后开始，截取到首个空白/控制字符或字符串结尾
        final rest = msg.substring(idx + 1);
        final match = RegExp(r'^[^\s\r\n]+').firstMatch(rest);
        if (match != null) {
          version = match.group(0);
        }
      }
      return AtResponse(
        type: AtResponseType.version,
        raw: msg,
        payload: version != null ? {'version': version} : null,
      );
    }
    if (msg.startsWith('+EFUSESN')) {
      return AtResponse(type: AtResponseType.serialNumber, raw: msg);
    }
    if (msg.startsWith('+SN ')) {
      final deviceId = parseDeviceId(msg);
      return AtResponse(
        type: AtResponseType.deviceId,
        raw: msg,
        payload: {'deviceId': deviceId},
      );
    }
    if (msg.startsWith('+FREQ:')) {
      final parts = msg.substring(6).split(',');
      return AtResponse(
        type: AtResponseType.freq,
        raw: msg,
        payload: {
          'txDataFreq': parts[0],
          'rxDataFreq': parts[1],
          'txBcnFreq': parts[2],
          'rxBcnFreq': parts[3],
        },
      );
    }
    if (msg.startsWith('+RATE:')) {
      return AtResponse(
        type: AtResponseType.rate,
        raw: msg,
        payload: {'rateMode': msg.substring(6)},
      );
    }
    if (msg.startsWith('+BCNID:')) {
      return AtResponse(
        type: AtResponseType.bcnId,
        raw: msg,
        payload: {'bcnId': msg.substring(7)},
      );
    }
    if (msg.startsWith('+ADDTL:')) {
      return AtResponse(
        type: AtResponseType.addtl,
        raw: msg,
        payload: {'mode': msg.substring(7)},
      );
    }
    if (msg.startsWith('+FRAMECFG:')) {
      // 解析时隙配置响应
      // 格式：+FRAMECFG:<时隙类型>,<时隙长度>,<时隙类型>,<时隙长度>,...
      final configStr = msg.substring(10);
      final parts = configStr.split(',');

      // 解析时隙配置
      final slots = <Map<String, int>>[];
      for (int i = 0; i < parts.length; i += 2) {
        if (i + 1 < parts.length) {
          final type = int.tryParse(parts[i]);
          final length = int.tryParse(parts[i + 1]);
          if (type != null && length != null) {
            slots.add({'type': type, 'length': length});
          }
        }
      }

      return AtResponse(
        type: AtResponseType.frameConfig,
        raw: msg,
        payload: {'slots': slots},
      );
    }
    if (msg.startsWith('+WORKMODE:')) {
      // 解析工作模式响应
      // 格式：+WORKMODE:<工作模式>
      final modeStr = msg.substring(10);
      final mode = int.tryParse(modeStr);

      return AtResponse(
        type: AtResponseType.workMode,
        raw: msg,
        payload: {'mode': mode},
      );
    }

    // +DI: LEN 50, SLOT 0, SNR 11, RSSI -60, Data <escaped bytes>
    if (msg.startsWith('+DI:')) {
      // 直接查找 'Data ' (包含空格)，定位到数据开始位置
      final dataIdx = msg.indexOf('Data ');
      if (dataIdx != -1) {
        // 从 'Data ' 后面开始提取数据，跳过 'Data ' (5个字符)
        String dataStr = msg.substring(dataIdx + 5);

        // 移除末尾的 \r\n 等控制字符
        // 注意：只移除真正的控制字符，避免误删除数据字节
        while (dataStr.isNotEmpty) {
          final lastByte = dataStr.codeUnitAt(dataStr.length - 1);
          if (lastByte == 0x0D || lastByte == 0x0A) {
            dataStr = dataStr.substring(0, dataStr.length - 1);
          } else {
            break; // 遇到非控制字符就停止
          }
        }

        // Data 字段应始终是二进制(已转义)字节流，按原始字节处理。
        final rawEscaped = Uint8List.fromList(dataStr.codeUnits);

        // 解码并转换为十六进制字符串，便于日志查看
        final unescaped = BinaryEscape.unescape(rawEscaped);

        final dataHex = unescaped
            .map((b) => b.toRadixString(16).padLeft(2, '0'))
            .join(' ') // 以空格分隔提升可读性
            .toUpperCase();

        // 同时把未转义的原始字节放入 payload，供上层模块做 TK8620 解析
        return AtResponse(
          type: AtResponseType.diData,
          raw: msg,
          payload: {
            'data': dataHex, // 便于日志查看
            'raw': unescaped, // Uint8List，供后续协议解析
          },
        );
      }
    }
    // 未识别
    return AtResponse(type: AtResponseType.unknown, raw: msg);
  }

  /// 确保资源释放（可在应用关闭时调用）
  Future<void> dispose() async {
    await _controller.close();
  }
}
