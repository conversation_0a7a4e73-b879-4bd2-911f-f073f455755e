/// 文本数据多包组装器
/// 将 TK8620 多包文本帧重组为完整字符串后返回 TK8620TextData

import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'tk8620_frame_decoder.dart';
import 'tk8620_protocol.dart';

class _TextAssembly {
  _TextAssembly(this.total)
    : chunks = List<Uint8List?>.filled(total, null, growable: false);

  final int total;
  final List<Uint8List?> chunks;

  bool get isComplete => !chunks.contains(null);

  Uint8List merge() {
    final builder = BytesBuilder();
    for (final part in chunks) {
      builder.add(part!);
    }
    return builder.toBytes();
  }
}

/// 提供 `handleTextFrame`，在收齐所有分包时返回解析后的 TK8620TextData。
class TextPacketAssembler {
  final Map<String, _TextAssembly> _cache = {};

  /// 处理一帧文本数据包，必要时进行组装
  Future<TK8620TextData?> handleTextFrame(TK8620Frame frame) async {
    final total = frame.subPkgNum;
    final idx = frame.subPkgNo;

    // 从目标ID获取群组ID用于解密
    final groupId = frame.dstId.toRadixString(16).padLeft(8, '0').toUpperCase();

    // 单包直接返回
    if (total <= 1) {
      return await TK8620PayloadParser.parseTextData(
        frame.payload,
        groupId: groupId,
      );
    }

    // 使用 srcId+frameCnt 作为组装 key
    final key = '${frame.srcId}-${frame.frameCnt}';
    var assembly = _cache[key];
    if (assembly == null) {
      assembly = _TextAssembly(total);
      _cache[key] = assembly;
    }

    // 每个分包都包含数据类型码，需要去掉数据类型码只保留文本数据
    Uint8List chunkData;
    if (idx == 0) {
      // 第一包：保留完整载荷（包含数据类型码0x00），用于最终解析
      chunkData = frame.payload;
    } else {
      // 后续包：去掉第一个字节（数据类型码），只保留实际文本数据
      if (frame.payload.length > 1) {
        chunkData = frame.payload.sublist(1);
      } else {
        // 如果载荷长度不足，记录错误并返回null
        debugPrint('⚠️ 分包$idx载荷长度不足: ${frame.payload.length}');
        return null;
      }
    }

    assembly.chunks[idx] = chunkData;

    if (assembly.isComplete) {
      final merged = assembly.merge();
      _cache.remove(key);
      return await TK8620PayloadParser.parseTextData(merged, groupId: groupId);
    }

    return null; // 未组装完成
  }
}
