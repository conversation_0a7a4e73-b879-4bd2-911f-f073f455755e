import 'dart:typed_data';

import 'package:flutter_blue_plus/flutter_blue_plus.dart';

import 'device_control_protocol.dart';
import '../bluetooth/protocol_gatt_helper.dart';

/// Helper utilities to build & send Device-Control protocol frames (SDD §4.1.3.4).
/// 與 [DeviceControlResponseParser] 相对，负责『请求』侧逻辑。
class DeviceControlRequestSender {
  DeviceControlRequestSender._();

  /// 构建一个完整协议帧 (含 0x55AA 头)，返回待发送字节序列。
  static Uint8List buildFrame({
    required BTDeviceFrameType type,
    required int code,
    Uint8List? payload,
  }) {
    return BTDeviceControlParser.build(type: type, code: code, data: payload);
  }

  /// 直接发送协议帧到指定 [device]。
  static Future<void> send(
    BluetoothDevice device, {
    required BTDeviceFrameType type,
    required int code,
    Uint8List? payload,
    bool withoutResponse = true,
  }) async {
    final frame = buildFrame(type: type, code: code, payload: payload);
    await ProtocolGattHelper.sendFrame(
      device,
      frame,
      withoutResponse: withoutResponse,
    );
  }

  // ---------------- Convenience Shortcuts ----------------

  /// 发送『查询电量』帧。
  static Future<void> sendBatteryQuery(BluetoothDevice device) async {
    await send(
      device,
      type: BTDeviceFrameType.query,
      code: BTDeviceQueryCode.batteryLevel,
      withoutResponse: true,
    );
  }

  /// 发送『断开BLE』命令帧。
  static Future<void> sendDisconnectCommand(BluetoothDevice device) async {
    await send(
      device,
      type: BTDeviceFrameType.command,
      code: BTDeviceCommandCode.disconnectBLE,
      withoutResponse: true,
    );
  }

  /// 发送『查询固件版本』帧。
  static Future<void> sendFirmwareVersionQuery(BluetoothDevice device) async {
    await send(
      device,
      type: BTDeviceFrameType.query,
      code: BTDeviceQueryCode.firmwareVersion,
      withoutResponse: true,
    );
  }

  /// 发送『查询硬件版本』帧。
  static Future<void> sendHardwareVersionQuery(BluetoothDevice device) async {
    await send(
      device,
      type: BTDeviceFrameType.query,
      code: BTDeviceQueryCode.hardwareVersion,
      withoutResponse: true,
    );
  }

  /// 发送『查询设备名称』帧。
  static Future<void> sendDeviceNameQuery(BluetoothDevice device) async {
    await send(
      device,
      type: BTDeviceFrameType.query,
      code: BTDeviceQueryCode.deviceName,
      withoutResponse: true,
    );
  }
}
