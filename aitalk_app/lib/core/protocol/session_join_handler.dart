import 'dart:async';
import 'package:flutter/foundation.dart';

import '../services/database_service.dart';
import '../services/group_encryption_key_service.dart';
import '../bluetooth/bluetooth_manager.dart';
import '../device/device_manager.dart';
import '../utils/group_util.dart';
import '../services/active_group_storage.dart';
import '../services/conversation_manager.dart';
import 'tk8620_protocol.dart';
import 'tk8620_request_sender.dart';
import 'tk8620_frame_decoder.dart';

/// 处理【会话加入请求】的工具类，负责：
/// 1. 验证是否为私有群（公共群不使用加入机制）；
/// 2. 验证当前设备是否为群主（只有群主才能批准私有群加入请求）；
/// 3. 校验群组密码；
/// 4. 生成 SessionID；
/// 5. 发送加入会话响应帧。
class SessionJoinHandler {
  SessionJoinHandler._();

  /// 根据请求帧与解析结果进行处理。
  /// [frame] 为原始 TK8620 帧；
  /// [joinReq] 为已解析出的请求载荷。
  static Future<void> handle(
    TK8620Frame frame,
    TK8620SessionJoinRequest joinReq,
  ) async {
    // 使用当前激活群组 ID，而非帧中的 dstId
    String? activeGroupId = ConversationManager.lastConversationId.value;
    if (activeGroupId == null) {
      final tuple = await ActiveGroupStorage.load();
      if (tuple != null) activeGroupId = tuple.$1; // 取 groupId
    }

    if (activeGroupId == null) {
      debugPrint('[SessionJoinHandler] 无激活群，忽略加入请求');
      return;
    }

    final String groupId = activeGroupId;

    // 判断是否公共群：公共群密码恒为 0
    final bool isPublic = GroupUtil.isPublicGroup(groupId);

    // 对于私有群，验证当前设备是否为群主（只有群主才能批准加入请求）
    if (!isPublic) {
      final String? currentDeviceId =
          DeviceManager.instance.deviceIdNotifier.value;
      if (currentDeviceId == null) {
        debugPrint('[SessionJoinHandler] 无法获取当前设备ID，忽略私有群加入请求');
        return;
      }

      // 查询群组创建者
      try {
        final db = await DatabaseService.instance.database;
        final rows = await db.query(
          'groups',
          columns: ['creator_id'],
          where: 'group_id = ?',
          whereArgs: [groupId],
          limit: 1,
        );

        if (rows.isEmpty) {
          debugPrint('[SessionJoinHandler] 私有群 $groupId 不存在，忽略加入请求');
          return;
        }

        final String creatorId = rows.first['creator_id'] as String;
        if (creatorId != currentDeviceId) {
          debugPrint(
            '[SessionJoinHandler] 当前设备 $currentDeviceId 非群主 $creatorId，无权处理私有群加入请求',
          );
          return;
        }

        debugPrint('[SessionJoinHandler] 私有群群主身份验证通过，处理加入请求');
      } catch (e) {
        debugPrint('[SessionJoinHandler] 查询私有群群主信息失败: $e，忽略加入请求');
        return;
      }
    } else {
      debugPrint('[SessionJoinHandler] 公共群加入请求，无需群主验证');
    }

    // 查询群名称，若失败则退化为 groupId 作为占位
    String groupName = groupId;
    try {
      final db = await DatabaseService.instance.database;
      final rows = await db.query(
        'groups',
        columns: ['group_name'],
        where: 'group_id = ?',
        whereArgs: [groupId],
        limit: 1,
      );
      if (rows.isNotEmpty && rows.first['group_name'] != null) {
        groupName = rows.first['group_name'] as String;
      }
    } catch (e) {
      debugPrint('[SessionJoinHandler] 查询群名称失败: $e');
    }

    int expectedPassword = 0;
    if (!isPublic) {
      // 私有群：查询数据库获取 password 字段
      try {
        final db = await DatabaseService.instance.database;
        final rows = await db.query(
          'groups',
          columns: ['password'],
          where: 'group_id = ?',
          whereArgs: [groupId],
          limit: 1,
        );
        if (rows.isNotEmpty && rows.first['password'] is int) {
          expectedPassword = rows.first['password'] as int; // 默认为0
        }
      } catch (e) {
        debugPrint('[SessionJoinHandler] 查询群密码失败: $e');
      }
    }

    // 校验密码
    final bool ok = joinReq.sessionPassword == expectedPassword;
    final int responseCode = ok ? 0 : 1;

    // 生成 SessionID：直接使用群组 ID (4 字节) 本身，确保双方统一
    final int sessionId = int.parse(groupId, radix: 16);

    // 构造成员列表（简单查询 groups_members 表）
    List<int> memberDeviceIds = [];
    int memberId = 0;
    try {
      final db = await DatabaseService.instance.database;
      final rows = await db.rawQuery(
        'SELECT device_id FROM group_members WHERE group_id = ? ORDER BY joined_at ASC',
        [groupId],
      );
      memberDeviceIds = rows.map((r) {
        final didStr = r['device_id'] as String;
        // assume stored as 0xXXXX format
        return int.parse(
          didStr.startsWith('0x') ? didStr.substring(2) : didStr,
          radix: 16,
        );
      }).toList();

      // 新加入者 deviceId is frame.srcId (4bytes)
      if (!memberDeviceIds.contains(frame.srcId)) {
        memberDeviceIds.add(frame.srcId);

        // 如果验证成功，将新成员保存到数据库
        if (ok) {
          // 计算新成员的memberId（基于索引）
          final newMemberId = memberDeviceIds.length - 1;
          await DatabaseService.instance.ensureGroupMember(
            groupId,
            frame.srcId,
            memberId: newMemberId,
          );

          // 为新加入的私有群成员生成加密密钥
          if (!isPublic) {
            final success =
                await GroupEncryptionKeyService.generatePrivateGroupKey(
                  groupId,
                  expectedPassword,
                  DateTime.now().millisecondsSinceEpoch,
                );
            if (success) {
              debugPrint('[SessionJoinHandler] 为新成员生成加密密钥成功: $groupId');
            } else {
              debugPrint('[SessionJoinHandler] 为新成员生成加密密钥失败: $groupId');
            }
          }

          debugPrint(
            '[SessionJoinHandler] 新成员 ${frame.srcId} (memberId: $newMemberId) 已加入群组 $groupId',
          );
        }
      }
      memberId = memberDeviceIds.indexOf(frame.srcId);
    } catch (e) {
      debugPrint('[SessionJoinHandler] 查询成员列表失败: $e');
    }

    // 获取当前已连接设备
    final device = BluetoothManager.currentDevice.value;
    if (device == null) {
      debugPrint('[SessionJoinHandler] 当前无连接设备，无法回复加入响应');
      return;
    }

    // 解析本机 DeviceID
    int localDeviceId = 0;
    final deviceIdHex = DeviceManager.instance.deviceIdNotifier.value;
    if (deviceIdHex != null && deviceIdHex.startsWith('0x')) {
      try {
        localDeviceId =
            int.parse(deviceIdHex.substring(2), radix: 16) & 0xFFFFFFFF;
      } catch (_) {}
    }

    try {
      await TK8620RequestSender.sendJoinGroupResponse(
        device,
        responseCode: responseCode,
        memberId: memberId,
        sessionId: sessionId,
        groupName: groupName,
        memberDeviceIds: memberDeviceIds,
        srcId: localDeviceId,
        dstId: frame.srcId,
      );
      debugPrint(
        '[SessionJoinHandler] 已发送入群响应: code=$responseCode, '
        'sessionId=$sessionId, group=$groupId, srcId=$localDeviceId, dstId=${frame.srcId}',
      );
    } catch (e) {
      debugPrint('[SessionJoinHandler] 发送入群响应失败: $e');
    }
  }
}
