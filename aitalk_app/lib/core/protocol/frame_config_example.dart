import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'at_commands.dart';
import 'at_response_handler.dart';

/// AT+FRAMECFG 指令使用示例
/// 
/// 该文件展示了如何使用新增的 AT+FRAMECFG 指令来设置和查询
/// 同步工作模式的时隙结构配置
class FrameConfigExample {
  
  /// 查询当前时隙配置
  /// 
  /// 发送 AT+FRAMECFG? 指令查询当前的时隙结构配置
  static Uint8List queryFrameConfig() {
    debugPrint('📡 查询时隙配置');
    
    return getAtCommandBytes(
      AtCommandType.frameConfig,
      params: {'isQuery': true},
    );
  }
  
  /// 设置基本时隙配置
  /// 
  /// 示例：设置4个时隙结构
  /// - BCN时隙（默认，不需要配置）
  /// - 发送数据时隙：30字节
  /// - 空闲时隙：2000微秒
  /// - 接收数据时隙：40字节
  static Uint8List setBasicFrameConfig() {
    debugPrint('📡 设置基本时隙配置');
    
    final slots = [
      {'type': 7, 'length': 30},    // 发送数据时隙，30字节
      {'type': 6, 'length': 2000},  // 空闲时隙，2000微秒
      {'type': 8, 'length': 40},    // 接收数据时隙，40字节
    ];
    
    return getAtCommandBytes(
      AtCommandType.frameConfig,
      params: {'slots': slots},
    );
  }
  
  /// 设置复杂时隙配置
  /// 
  /// 示例：设置多个时隙的复杂结构
  static Uint8List setComplexFrameConfig() {
    debugPrint('📡 设置复杂时隙配置');
    
    final slots = [
      {'type': 7, 'length': 50},     // 发送数据时隙，50字节
      {'type': 6, 'length': 1500},   // 空闲时隙，1500微秒
      {'type': 8, 'length': 60},     // 接收数据时隙，60字节
      {'type': 6, 'length': 3000},   // 空闲时隙，3000微秒
      {'type': 7, 'length': 100},    // 发送数据时隙，100字节
      {'type': 8, 'length': 80},     // 接收数据时隙，80字节
    ];
    
    return getAtCommandBytes(
      AtCommandType.frameConfig,
      params: {'slots': slots},
    );
  }
  
  /// 设置最大长度时隙配置
  /// 
  /// 测试边界值：最大数据时隙长度和最大空闲时隙长度
  static Uint8List setMaxLengthFrameConfig() {
    debugPrint('📡 设置最大长度时隙配置');
    
    final slots = [
      {'type': 7, 'length': 600},        // 最大发送数据时隙，600字节
      {'type': 6, 'length': 4294967295}, // 最大空闲时隙，4294967295微秒
      {'type': 8, 'length': 600},        // 最大接收数据时隙，600字节
    ];
    
    return getAtCommandBytes(
      AtCommandType.frameConfig,
      params: {'slots': slots},
    );
  }
  
  /// 设置最小长度时隙配置
  /// 
  /// 测试边界值：最小数据时隙长度和最小空闲时隙长度
  static Uint8List setMinLengthFrameConfig() {
    debugPrint('📡 设置最小长度时隙配置');
    
    final slots = [
      {'type': 7, 'length': 1},    // 最小发送数据时隙，1字节
      {'type': 6, 'length': 1024}, // 最小空闲时隙，1024微秒
      {'type': 8, 'length': 1},    // 最小接收数据时隙，1字节
    ];
    
    return getAtCommandBytes(
      AtCommandType.frameConfig,
      params: {'slots': slots},
    );
  }
  
  /// 处理时隙配置响应
  /// 
  /// 解析并显示从设备返回的时隙配置信息
  static void handleFrameConfigResponse(AtResponse response) {
    if (response.type != AtResponseType.frameConfig) {
      debugPrint('❌ 响应类型不匹配，期望 frameConfig，实际 ${response.type}');
      return;
    }
    
    final payload = response.payload;
    if (payload == null) {
      debugPrint('❌ 时隙配置响应载荷为空');
      return;
    }
    
    final slots = payload['slots'] as List<Map<String, int>>?;
    if (slots == null || slots.isEmpty) {
      debugPrint('❌ 时隙配置数据为空');
      return;
    }
    
    debugPrint('📡 当前时隙配置：');
    debugPrint('   BCN时隙（默认，索引0）');
    
    for (int i = 0; i < slots.length; i++) {
      final slot = slots[i];
      final type = slot['type'];
      final length = slot['length'];
      
      String typeDesc;
      String lengthUnit;
      
      switch (type) {
        case 6:
          typeDesc = '空闲时隙';
          lengthUnit = '微秒';
          break;
        case 7:
          typeDesc = '发送数据时隙';
          lengthUnit = '字节';
          break;
        case 8:
          typeDesc = '接收数据时隙';
          lengthUnit = '字节';
          break;
        default:
          typeDesc = '未知时隙类型($type)';
          lengthUnit = '单位';
      }
      
      debugPrint('   时隙${i + 1}: $typeDesc, 长度: $length$lengthUnit');
    }
  }
  
  /// 验证时隙配置参数
  /// 
  /// 在发送指令前验证参数的有效性
  static bool validateSlotConfig(List<Map<String, int>> slots) {
    if (slots.isEmpty) {
      debugPrint('❌ 时隙配置不能为空');
      return false;
    }
    
    if (slots.length > 209) {
      debugPrint('❌ 时隙数量不能超过209个');
      return false;
    }
    
    for (int i = 0; i < slots.length; i++) {
      final slot = slots[i];
      final type = slot['type'];
      final length = slot['length'];
      
      if (type == null || length == null) {
        debugPrint('❌ 时隙${i + 1}缺少type或length参数');
        return false;
      }
      
      if (![6, 7, 8].contains(type)) {
        debugPrint('❌ 时隙${i + 1}类型无效: $type (必须为6、7、8之一)');
        return false;
      }
      
      if (type == 6) {
        // 空闲时隙长度验证
        if (length < 1024 || length > 4294967295) {
          debugPrint('❌ 时隙${i + 1}空闲时隙长度无效: $length (必须在1024~4294967295微秒范围内)');
          return false;
        }
      } else {
        // 数据时隙长度验证
        if (length < 1 || length > 600) {
          debugPrint('❌ 时隙${i + 1}数据时隙长度无效: $length (必须在1~600字节范围内)');
          return false;
        }
      }
    }
    
    debugPrint('✅ 时隙配置参数验证通过');
    return true;
  }
}

/// 时隙类型常量定义
class SlotType {
  /// 空闲时隙
  static const int idle = 6;
  
  /// 发送数据时隙
  static const int transmit = 7;
  
  /// 接收数据时隙
  static const int receive = 8;
}

/// 时隙长度限制常量
class SlotLimits {
  /// 空闲时隙最小长度（微秒）
  static const int idleMinLength = 1024;
  
  /// 空闲时隙最大长度（微秒）
  static const int idleMaxLength = 4294967295;
  
  /// 数据时隙最小长度（字节）
  static const int dataMinLength = 1;
  
  /// 数据时隙最大长度（字节）
  static const int dataMaxLength = 600;
  
  /// 最大时隙数量
  static const int maxSlotCount = 209;
}
