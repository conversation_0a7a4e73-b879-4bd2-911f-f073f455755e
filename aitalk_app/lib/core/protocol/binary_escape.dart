/// 二进制数据转义/反转义工具
/// 规范来源: aiTalk SDD §4.5.5
///
/// 转义规则:
///   0x0D -> 0xDB 0xDC
///   0x0A -> 0xDB 0xDD
///   0xDB -> 0xDB 0xDE
/// 其余字节保持不变。
///
/// 反转义规则与之对应。
///
/// 注意: 该规则类似 SLIP 协议, 以 0xDB 为 ESC 字节。
import 'dart:typed_data';

class BinaryEscape {
  BinaryEscape._();

  // 常量定义
  static const int _ESC = 0xDB;
  static const int _ESC_CR = 0xDC; // 替代 0x0D
  static const int _ESC_LF = 0xDD; // 替代 0x0A
  static const int _ESC_ESC = 0xDE; // 替代 0xDB

  /// 对 [data] 进行转义, 返回新的 Uint8List
  static Uint8List escape(Uint8List data) {
    final builder = BytesBuilder();
    for (final b in data) {
      switch (b) {
        case 0x0D:
          builder.add([_ESC, _ESC_CR]);
          break;
        case 0x0A:
          builder.add([_ESC, _ESC_LF]);
          break;
        case _ESC:
          builder.add([_ESC, _ESC_ESC]);
          break;
        default:
          builder.addByte(b);
      }
    }
    return builder.toBytes();
  }

  /// 将转义过的数据恢复为原始数据。
  /// 若检测到非法转义序列, 直接把 ESC 字节以及后续字节原样保留。
  static Uint8List unescape(Uint8List data) {
    final builder = BytesBuilder();
    for (int i = 0; i < data.length; i++) {
      final b = data[i];
      if (b == _ESC && i + 1 < data.length) {
        final next = data[i + 1];
        switch (next) {
          case _ESC_CR:
            builder.addByte(0x0D);
            i++; // 跳过 next
            break;
          case _ESC_LF:
            builder.addByte(0x0A);
            i++;
            break;
          case _ESC_ESC:
            builder.addByte(_ESC);
            i++;
            break;
          default:
            // 非法转义, 保留 ESC, 继续正常处理 next
            builder.addByte(b);
        }
      } else {
        builder.addByte(b);
      }
    }
    return builder.toBytes();
  }
}
