import 'package:flutter/foundation.dart';

/// 解析 8620 `+SN` 响应，计算 32-bit DeviceID
/// 返回 null 则表示解析失败。
int? parseDeviceId(String snLine) {
  if (!snLine.startsWith('+SN ')) return null;

  // 清理前缀/尾部
  var content = snLine.replaceFirst('+SN ', '').replaceAll('AT_OK', '').trim();
  final parts = content.split(':');
  if (parts.length != 8) {
    debugPrint('SN Parser: unexpected component count ${parts.length}');
    return null;
  }

  try {
    final year = int.parse(parts[2]); // e.g. 23
    final week = int.parse(parts[3]); // e.g. 49
    final ser = int.parse(parts[6], radix: 16); // hex serial

    final year5 = year & 0x1F;
    final week7 = week & 0x7F;
    final ser20 = ser & 0xFFFFF;

    final deviceId = (year5 << 27) | (week7 << 20) | ser20;
    return deviceId;
  } catch (e) {
    debugPrint('SN Parser: parse error $e');
    return null;
  }
} 