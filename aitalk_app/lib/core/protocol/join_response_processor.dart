import 'package:flutter/foundation.dart';
import 'dart:async';

import '../services/database_service.dart';
import '../services/group_encryption_key_service.dart';
import 'package:sqflite/sqflite.dart';
import '../services/active_group_storage.dart';
import '../services/conversation_manager.dart';
import '../utils/group_util.dart';
import '../services/conversation_display_service.dart';
import '../services/channel_manager.dart';
import 'tk8620_frame_decoder.dart';

/// 负责在收到 `joinResponse` 成功后：
/// 1. 将群信息写入本地数据库 (groups / group_members / conversations / contacts)。
/// 2. 更新 UI 高亮 (ConversationManager) 并持久化激活群。
/// 3. 触发会话列表刷新。
/// 调用方式：
/// ```dart
/// await JoinResponseProcessor.handle(groupId, response);
/// ```
class JoinResponseProcessor {
  JoinResponseProcessor._();

  /// 处理成功的入群响应
  static Future<void> handle(
    String groupId,
    TK8620SessionJoinResponse resp, {
    int? joinPassword, // 加入时使用的密码
  }) async {
    try {
      final db = await DatabaseService.instance.database;
      final int nowMs = DateTime.now().millisecondsSinceEpoch;

      // 1. groups 表 upsert
      await db.insert('groups', {
        'group_id': groupId,
        'group_name': resp.groupName.isEmpty ? groupId : resp.groupName,
        'channel': ChannelManager.currentChannel.value,
        'is_private': GroupUtil.isPublicGroup(groupId) ? 0 : 1,
        'password': joinPassword ?? 0, // 保存加入时使用的密码
        'creator_id': resp.members[0] != null
            ? '0x${resp.members[0]!.toRadixString(16).toUpperCase()}'
            : 'unknown',
        'created_at': nowMs,
        'updated_at': nowMs,
      }, conflictAlgorithm: ConflictAlgorithm.ignore);

      // 2. group_conversations 表
      await db.insert('group_conversations', {
        'conversation_id': groupId,
        'group_id': groupId,
        'unread_count': 0,
        'last_msg_time': nowMs ~/ 1000,
      }, conflictAlgorithm: ConflictAlgorithm.ignore);

      // 3. 写入成员信息
      for (final entry in resp.members.entries) {
        final memberId = entry.key; // 群内成员ID (1字节)
        final didInt = entry.value; // 设备ID (4字节)
        final deviceIdStr =
            '0x${didInt.toRadixString(16).toUpperCase().padLeft(8, '0')}';

        // contacts - 使用memberId作为显示标识
        await db.insert('contacts', {
          'device_id': deviceIdStr,
          'nickname': '匿名用户 - $memberId', // 使用memberId而不是deviceId
          'avatar_index': 0,
          'created_at': nowMs,
          'updated_at': nowMs,
        }, conflictAlgorithm: ConflictAlgorithm.ignore);

        // group_members - 保存memberId和deviceId的映射
        await db.insert('group_members', {
          'group_id': groupId,
          'device_id': deviceIdStr,
          'member_id': memberId, // 保存群内成员ID
          'joined_at': nowMs,
        }, conflictAlgorithm: ConflictAlgorithm.ignore);
      }

      // 4. 为私有群组生成加密密钥
      if (!GroupUtil.isPublicGroup(groupId) && joinPassword != null) {
        final success = await GroupEncryptionKeyService.generatePrivateGroupKey(
          groupId,
          joinPassword,
          nowMs,
        );
        if (success) {
          debugPrint('[JoinResponseProcessor] 成功为加入的私有群组生成密钥: $groupId');
        } else {
          debugPrint('[JoinResponseProcessor] 为加入的私有群组生成密钥失败: $groupId');
        }
      }

      // 5. 更新显示名称缓存
      ConversationDisplayService.instance.setDisplayName(
        groupId,
        resp.groupName,
      );

      // 6. 激活会话 & 持久化
      ConversationManager.enter(groupId);
      await ActiveGroupStorage.save(
        groupId: groupId,
        creatorId: resp.members[0] != null
            ? '0x${resp.members[0]!.toRadixString(16).toUpperCase()}'
            : 'unknown',
      );

      // 7. 通知列表刷新
      DatabaseService.groupChangedNotifier.value++;

      debugPrint('[JoinResponseProcessor] 已处理成功入群响应, 激活群: $groupId');
    } catch (e) {
      debugPrint('[JoinResponseProcessor] 处理入群响应失败: $e');
    }
  }
}
