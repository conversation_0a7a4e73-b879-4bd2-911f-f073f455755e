import 'dart:typed_data';

import 'device_control_protocol.dart';

/// Parsed response structure per SDD §4.1.3.4.
class BTDeviceControlResponse {
  BTDeviceControlResponse({
    required this.responseCode,
    required this.requestType,
    required this.requestCode,
    required this.payload,
  });

  /// Response status code. 0x00 表示 SUCCESS。
  final int responseCode;

  /// 原始请求类型字节 (00:config, 01:cmd, 10:query)
  final int requestType;

  /// 原始请求类型码 (例如 batteryLevel 为 0x05)
  final int requestCode;

  /// 具体数据载荷（长度与协议里 PayloadLen 对应）。
  final Uint8List payload;

  bool get isSuccess => responseCode == BTDeviceResponseCode.success;
}

/// Utility class to parse Device-Control response payloads defined in SDD §4.1.3.4.
class DeviceControlResponseParser {
  DeviceControlResponseParser._();

  /// 通用解析：若 [frame] 满足完整应答格式，则返回 [BTDeviceControlResponse]；否则返回 null。
  static BTDeviceControlResponse? tryParseResponse(BTDeviceControlFrame frame) {
    if (frame.type != BTDeviceFrameType.response) return null;

    final p = frame.payload;
    if (p.length < 4) return null; // 至少有 respCode+reqType+reqCode+len

    final respCode = p[0];
    final reqType = p[1];
    final reqCode = p[2];
    final payloadLen = p[3];

    if (p.length < 4 + payloadLen) return null; // 长度不足

    final payload = Uint8List.fromList(p.sublist(4, 4 + payloadLen));
    return BTDeviceControlResponse(
      responseCode: respCode,
      requestType: reqType,
      requestCode: reqCode,
      payload: payload,
    );
  }

  /// 解析电量百分比 (0-100)。成功返回整数，否则 null。
  static int? tryParseBatteryLevel(BTDeviceControlFrame frame) {
    final resp = tryParseResponse(frame);
    if (resp == null) return null;
    if (!resp.isSuccess) return null;

    if (resp.requestType == BTDeviceFrameType.query.value &&
        resp.requestCode == BTDeviceQueryCode.batteryLevel &&
        resp.payload.length >= 1) {
      return resp.payload[0];
    }
    return null;
  }

  /// 解析固件版本 (payload 为可变长 ASCII 字符串)
  static String? tryParseFirmwareVersion(BTDeviceControlFrame frame) {
    final resp = tryParseResponse(frame);
    if (resp == null || !resp.isSuccess) return null;
    if (resp.requestType == BTDeviceFrameType.query.value &&
        resp.requestCode == BTDeviceQueryCode.firmwareVersion &&
        resp.payload.isNotEmpty) {
      return String.fromCharCodes(resp.payload).trim();
    }
    return null;
  }

  /// 解析硬件版本 (payload 为 ASCII 字符串，如 "HW_v2.1")
  static String? tryParseHardwareVersion(BTDeviceControlFrame frame) {
    final resp = tryParseResponse(frame);
    if (resp == null || !resp.isSuccess) return null;
    if (resp.requestType == BTDeviceFrameType.query.value &&
        resp.requestCode == BTDeviceQueryCode.hardwareVersion &&
        resp.payload.isNotEmpty) {
      return String.fromCharCodes(resp.payload).trim();
    }
    return null;
  }

  /// 解析设备名称 (payload 为 ASCII 字符串)
  static String? tryParseDeviceName(BTDeviceControlFrame frame) {
    final resp = tryParseResponse(frame);
    if (resp == null || !resp.isSuccess) return null;
    if (resp.requestType == BTDeviceFrameType.query.value &&
        resp.requestCode == BTDeviceQueryCode.deviceName &&
        resp.payload.isNotEmpty) {
      return String.fromCharCodes(resp.payload).trim();
    }
    return null;
  }
}
