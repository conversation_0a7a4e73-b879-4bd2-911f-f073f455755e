// Device control protocol frame encoding/decoding utilities
// Spec reference: aiTalk SDD §4.1.3.4 蓝牙设备控制协议
// Author: Auto-generated by AI assistant
// Date: 2025-07-15

import 'dart:typed_data';

/// Fixed 2-byte frame header 0x55AA (big-endian).
const int _kFrameHeader = 0x55AA;

/// Single-byte frame type as defined in spec.
/// 00: Config, 01: Command, 10: Query, 11: Response
enum BTDeviceFrameType {
  config(0x00),
  command(0x01),
  query(0x02),
  response(0x03);

  const BTDeviceFrameType(this.value);
  final int value;

  static BTDeviceFrameType? fromInt(int v) {
    for (final t in BTDeviceFrameType.values) {
      if (t.value == v) return t;
    }
    return null;
  }
}

/// Common representation of a decoded device control frame.
class BTDeviceControlFrame {
  BTDeviceControlFrame({
    required this.type,
    required this.payload,
  });

  final BTDeviceFrameType type;
  final Uint8List payload;

  /// First byte inside [payload] is often the **code** identifying config /
  /// command / query. Convenience getter provided for callers.
  int get code => payload.isEmpty ? 0 : payload[0];

  /// Encodes current frame into raw bytes ready to be sent over BLE.
  Uint8List toBytes() {
    final bytes = BytesBuilder();

    // Frame header big-endian 0x55 0xAA
    bytes.add([_kFrameHeader >> 8, _kFrameHeader & 0xFF]);
    bytes.add([type.value]);
    bytes.add([payload.length]);
    bytes.add(payload);
    return bytes.toBytes();
  }

  @override
  String toString() =>
      'BTDeviceControlFrame(type: $type, len: ${payload.length}, code: $code)';
}

/// Parsing utilities for device control frames.
abstract class BTDeviceControlParser {
  /// Attempts to parse [data] into a [BTDeviceControlFrame].
  /// Returns `null` if frame is invalid or incomplete.
  static BTDeviceControlFrame? tryParse(Uint8List data) {
    // Minimum length: header(2) + type(1) + len(1)
    if (data.length < 4) return null;
    final header = (data[0] << 8) | data[1];
    if (header != _kFrameHeader) return null;

    final typeByte = data[2];
    final lenByte = data[3];
    if (data.length < 4 + lenByte) return null; // incomplete frame

    final type = BTDeviceFrameType.fromInt(typeByte);
    if (type == null) return null;

    final payload = Uint8List.fromList(data.sublist(4, 4 + lenByte));
    return BTDeviceControlFrame(type: type, payload: payload);
  }

  /// Utility to build a frame with given [type], [code] and optional [data].
  /// [code] becomes the first byte of payload per spec.
  static Uint8List build({
    required BTDeviceFrameType type,
    required int code,
    Uint8List? data,
  }) {
    data ??= Uint8List(0);
    final payload = Uint8List(data.length + 1)
      ..[0] = code
      ..setRange(1, data.length + 1, data);
    return BTDeviceControlFrame(type: type, payload: payload).toBytes();
  }
}

/// Common response status codes per spec §4.1.3.4
class BTDeviceResponseCode {
  static const int success = 0x00;
  static const int invalidParameter = 0x01;
  static const int notSupported = 0x02;
  static const int timeout = 0x03;
  static const int authenticationError = 0x04;
  static const int unknownError = 0xFF;
}

/// Example config/query/command codes (partial, extend as needed).
class BTDeviceConfigCode {
  static const int baudRate = 0x00;
  static const int deviceName = 0x01;
  static const int txPower = 0x02;
}

class BTDeviceQueryCode {
  static const int baudRate = 0x00;
  static const int deviceName = 0x01;
  static const int txPower = 0x02;
  static const int firmwareVersion = 0x03;
  static const int hardwareVersion = 0x04;
  static const int batteryLevel = 0x05;
}

class BTDeviceCommandCode {
  static const int resetBLE = 0x00;
  static const int disconnectBLE = 0x01;
} 