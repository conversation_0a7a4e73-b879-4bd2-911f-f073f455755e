// TK8620 射频通信协议帧编码器
// 参考规范: aiTalk SDD §4.1.3.1 & §4.1.3.2
// 该文件实现了TK8620协议帧的编码和载荷构建功能

import 'dart:typed_data';
import 'dart:convert';
import 'tk8620_protocol.dart';

/// TK8620协议帧编码器
/// 负责将TK8620Frame对象编码为字节序列，以及构建各种类型的载荷
class TK8620FrameEncoder {
  TK8620FrameEncoder._();

  /// 将帧编码为字节序列
  static Uint8List encode(TK8620Frame frame) {
    final buffer = BytesBuilder();

    // 1. 添加帧控制字段 (FrameCtrl)
    buffer.addByte(frame.frameCtrl);

    // 2. 根据帧类型添加不同的头部
    if (frame.isVoiceFrame) {
      // 语音帧使用简化头部: FrameCtrl(1) + SrcID(1)
      buffer.addByte(frame.srcId);
    } else {
      // 非语音帧使用完整头部
      // FrameCnt(2) - 大端序
      buffer.addByte((frame.frameCnt >> 8) & 0xFF);
      buffer.addByte(frame.frameCnt & 0xFF);

      // SrcID - 根据useLongSrcId决定长度
      if (frame.useLongSrcId) {
        // 4字节SrcID (大端序) - 用于加入会话请求/响应
        buffer.addByte((frame.srcId >> 24) & 0xFF);
        buffer.addByte((frame.srcId >> 16) & 0xFF);
        buffer.addByte((frame.srcId >> 8) & 0xFF);
        buffer.addByte(frame.srcId & 0xFF);
      } else {
        // 1字节SrcID - 正常情况
        buffer.addByte(frame.srcId);
      }

      // DstID(4) - 大端序
      buffer.addByte((frame.dstId >> 24) & 0xFF);
      buffer.addByte((frame.dstId >> 16) & 0xFF);
      buffer.addByte((frame.dstId >> 8) & 0xFF);
      buffer.addByte(frame.dstId & 0xFF);

      // SubPkgNum(1)
      buffer.addByte(frame.subPkgNum);

      // SubPkgNo(1)
      buffer.addByte(frame.subPkgNo);
    }

    // 3. 添加有效载荷
    buffer.add(frame.payload);

    return buffer.toBytes();
  }

  /// 构建完整的TK8620帧并编码为字节序列
  static Uint8List buildFrame({
    required TK8620FrameType frameType,
    required TK8620CommunicationMode communicationMode,
    int version = TK8620Version.aiTalk_1_0,
    int frameCnt = 0,
    required int srcId,
    int dstId = 0,
    int subPkgNum = 1,
    int subPkgNo = 0,
    required Uint8List payload,
    bool useLongSrcId = false,
  }) {
    final frame = TK8620Frame(
      frameType: frameType,
      communicationMode: communicationMode,
      version: version,
      frameCnt: frameCnt,
      srcId: srcId,
      dstId: dstId,
      subPkgNum: subPkgNum,
      subPkgNo: subPkgNo,
      payload: payload,
      useLongSrcId: useLongSrcId,
    );

    return encode(frame);
  }
}

/// TK8620协议载荷构建器
/// 参考规范: aiTalk SDD §4.1.3.3 帧载荷定义 (FPayload)
class TK8620PayloadBuilder {
  TK8620PayloadBuilder._();

  /// 构建会话请求载荷
  static Uint8List buildJoinRequest({
    required int sessionPassword,
    required String userName,
  }) {
    final buffer = BytesBuilder();

    // 控制码
    buffer.addByte(TK8620SessionCode.joinRequest);

    // SessionPassWd (4字节)
    buffer.addByte((sessionPassword >> 24) & 0xFF);
    buffer.addByte((sessionPassword >> 16) & 0xFF);
    buffer.addByte((sessionPassword >> 8) & 0xFF);
    buffer.addByte(sessionPassword & 0xFF);

    // UserName (最大32字节，UTF-8编码)
    final nameBytes = Uint8List.fromList(utf8.encode(userName));
    if (nameBytes.length > 32) {
      buffer.add(nameBytes.sublist(0, 32));
    } else {
      buffer.add(nameBytes);
      // 补齐32字节
      buffer.add(Uint8List(32 - nameBytes.length));
    }

    return buffer.toBytes();
  }

  /// 构建会话响应载荷
  static Uint8List buildJoinResponse({
    required int responseCode,
    required int memberId,
    required int sessionId,
    required String groupName,
    List<int> memberDeviceIds = const [], // 仅deviceId列表，id按索引自动生成
  }) {
    final buffer = BytesBuilder();

    // 控制码
    buffer.addByte(TK8620SessionCode.joinResponse);

    // 响应码
    buffer.addByte(responseCode);

    // 群内 ID
    buffer.addByte(memberId & 0xFF);

    // SessionID (4字节)
    buffer.addByte((sessionId >> 24) & 0xFF);
    buffer.addByte((sessionId >> 16) & 0xFF);
    buffer.addByte((sessionId >> 8) & 0xFF);
    buffer.addByte(sessionId & 0xFF);

    // 群名称 (24字节 UTF-8)
    final nameBytes = Uint8List.fromList(utf8.encode(groupName));
    if (nameBytes.length > 24) {
      buffer.add(nameBytes.sublist(0, 24));
    } else {
      buffer.add(nameBytes);
      buffer.add(Uint8List(24 - nameBytes.length));
    }

    // 成员列表
    for (int i = 0; i < memberDeviceIds.length; i++) {
      final did = memberDeviceIds[i] & 0xFFFFFFFF;
      buffer.addByte(i & 0xFF); // MemberId
      buffer.addByte((did >> 24) & 0xFF);
      buffer.addByte((did >> 16) & 0xFF);
      buffer.addByte((did >> 8) & 0xFF);
      buffer.addByte(did & 0xFF);
    }

    return buffer.toBytes();
  }

  /// 构建建立通话请求载荷 (Create Talk Request)
  static Uint8List buildCreateTalkRequest({required int sessionId}) {
    final buffer = BytesBuilder();

    // 控制码
    buffer.addByte(TK8620SessionCode.createTalkRequest);

    // SessionID (4字节)
    buffer.addByte((sessionId >> 24) & 0xFF);
    buffer.addByte((sessionId >> 16) & 0xFF);
    buffer.addByte((sessionId >> 8) & 0xFF);
    buffer.addByte(sessionId & 0xFF);

    return buffer.toBytes();
  }

  /// 构建建立通话响应载荷 (Create Talk Response)
  static Uint8List buildCreateTalkResponse({
    required int responseCode,
    required int sessionId,
  }) {
    final buffer = BytesBuilder();

    // 控制码
    buffer.addByte(TK8620SessionCode.createTalkResponse);

    // 响应码 (1字节)
    buffer.addByte(responseCode);

    // SessionID (4字节)
    buffer.addByte((sessionId >> 24) & 0xFF);
    buffer.addByte((sessionId >> 16) & 0xFF);
    buffer.addByte((sessionId >> 8) & 0xFF);
    buffer.addByte(sessionId & 0xFF);

    return buffer.toBytes();
  }

  /// 构建加入会话通知载荷 (Join Notification)
  static Uint8List buildJoinNotification({required int memberId}) {
    final buffer = BytesBuilder();

    // 控制码
    buffer.addByte(TK8620SessionCode.joinNotify);

    // MemberId (1字节)
    buffer.addByte(memberId & 0xFF);

    return buffer.toBytes();
  }

  /// 构建文本数据载荷
  static Uint8List buildTextData(String text) {
    final buffer = BytesBuilder();

    // 数据类型码
    buffer.addByte(TK8620DataType.text);

    // 文本数据 (UTF-8编码)
    buffer.add(utf8.encode(text));

    return buffer.toBytes();
  }

  /// 构建图像数据载荷
  static Uint8List buildImageData(Uint8List imageData) {
    final buffer = BytesBuilder();

    // 数据类型码
    buffer.addByte(TK8620DataType.image);

    // 图像数据
    buffer.add(imageData);

    return buffer.toBytes();
  }

  /// 构建GPS数据载荷
  static Uint8List buildGPSData({
    required double latitude,
    required double longitude,
    required double altitude,
  }) {
    final buffer = BytesBuilder();

    // 数据类型码
    buffer.addByte(TK8620DataType.gps);

    // 将浮点数转换为整数表示 (乘以10^7)
    final latInt = (latitude * 10000000).toInt();
    final longInt = (longitude * 10000000).toInt();
    final altInt = (altitude * 100).toInt();

    // 纬度 (4字节)
    buffer.addByte((latInt >> 24) & 0xFF);
    buffer.addByte((latInt >> 16) & 0xFF);
    buffer.addByte((latInt >> 8) & 0xFF);
    buffer.addByte(latInt & 0xFF);

    // 经度 (4字节)
    buffer.addByte((longInt >> 24) & 0xFF);
    buffer.addByte((longInt >> 16) & 0xFF);
    buffer.addByte((longInt >> 8) & 0xFF);
    buffer.addByte(longInt & 0xFF);

    // 海拔 (4字节)
    buffer.addByte((altInt >> 24) & 0xFF);
    buffer.addByte((altInt >> 16) & 0xFF);
    buffer.addByte((altInt >> 8) & 0xFF);
    buffer.addByte(altInt & 0xFF);

    return buffer.toBytes();
  }

  /// 构建语音数据载荷 (语音帧专用)
  static Uint8List buildVoiceData({
    required Uint8List audioData,
    bool isLastPacket = false,
  }) {
    final buffer = BytesBuilder();

    // 填充字节信息 (最低位用于标识是否为最后一个包)
    int paddingByte = 0;
    if (isLastPacket) {
      paddingByte |= 0x80; // 设置最高位为1表示最后一个包
    }

    buffer.addByte(paddingByte);
    buffer.add(audioData);

    return buffer.toBytes();
  }

  /// 构建实时通话语音数据载荷 (data帧专用)
  static Uint8List buildRealTimeVoiceData({
    required Uint8List audioData,
    bool isLastPacket = false,
  }) {
    final buffer = BytesBuilder();

    // 数据类型：实时通话语音
    buffer.addByte(TK8620DataType.realTimeVoice);

    // 填充字节信息 (最低位用于标识是否为最后一个包)
    int paddingByte = 0;
    if (isLastPacket) {
      paddingByte |= 0x80; // 设置最高位为1表示最后一个包
    }

    buffer.addByte(paddingByte);
    buffer.add(audioData);

    return buffer.toBytes();
  }

  /// 构建会话建立通知载荷
  static Uint8List buildSessionEstablish({
    required int sessionId,
    required String sessionName,
  }) {
    final buffer = BytesBuilder();

    // 控制码
    buffer.addByte(TK8620SessionCode.establish);

    // SessionID (4字节)
    buffer.addByte((sessionId >> 24) & 0xFF);
    buffer.addByte((sessionId >> 16) & 0xFF);
    buffer.addByte((sessionId >> 8) & 0xFF);
    buffer.addByte(sessionId & 0xFF);

    // 会话名称 (UTF-8编码)
    final nameBytes = Uint8List.fromList(utf8.encode(sessionName));
    buffer.add(nameBytes);

    return buffer.toBytes();
  }

  /// 构建会话终止通知载荷
  static Uint8List buildSessionTerminate({
    required int sessionId,
    required int reasonCode,
  }) {
    final buffer = BytesBuilder();

    // 控制码
    buffer.addByte(TK8620SessionCode.terminate);

    // SessionID (4字节)
    buffer.addByte((sessionId >> 24) & 0xFF);
    buffer.addByte((sessionId >> 16) & 0xFF);
    buffer.addByte((sessionId >> 8) & 0xFF);
    buffer.addByte(sessionId & 0xFF);

    // 终止原因码
    buffer.addByte(reasonCode);

    return buffer.toBytes();
  }
}
