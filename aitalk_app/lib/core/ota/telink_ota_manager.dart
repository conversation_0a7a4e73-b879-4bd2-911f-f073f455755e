// Telink OTA管理器
// 移植自Telink Generic OTA iOS库
// Author: Auto-generated by AI

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';

import 'models/ota_settings_model.dart';
import 'models/ota_progress.dart';
import 'models/ota_result.dart';
import 'protocol/ota_protocol.dart';
import 'protocol/ota_commands.dart';
import 'protocol/ota_response_parser.dart';
import 'services/file_data_source.dart';
import 'services/ota_gatt_helper.dart';
import 'exceptions/ota_exceptions.dart';
import 'ota_constants.dart';

/// Telink OTA管理器
/// 负责整个OTA升级流程的控制和管理
class TelinkOtaManager {
  static TelinkOtaManager? _instance;

  /// 获取单例实例
  static TelinkOtaManager get instance {
    _instance ??= TelinkOtaManager._();
    return _instance!;
  }

  TelinkOtaManager._();

  /// 当前设备
  BluetoothDevice? _currentDevice;

  /// GATT助手
  OtaGattHelper? _gattHelper;

  /// 文件数据源
  final FileDataSource _fileDataSource = FileDataSource.instance;

  /// 当前OTA设置
  OtaSettingsModel? _currentSettings;

  /// 固件信息
  FirmwareInfo? _firmwareInfo;

  /// 安全启动信息
  SecurityBootInfo? _securityBootInfo;

  /// 进度通知器
  final ValueNotifier<OtaProgress> _progressNotifier = ValueNotifier(
    OtaProgress.initial(),
  );

  /// 取消标志
  bool _isCancelled = false;

  /// 发送完成标志（参考iOS源码sendFinish）
  bool _sendFinish = false;

  /// 升级开始时间
  DateTime? _upgradeStartTime;

  /// 当前包索引
  int _currentPacketIndex = 0;

  /// 总包数
  int _totalPackets = 0;

  /// 已传输字节数
  int _bytesTransferred = 0;

  /// 传输速度计算
  final List<double> _speedSamples = [];
  DateTime? _lastSpeedSampleTime;

  /// 进度流
  ValueListenable<OtaProgress> get progressNotifier => _progressNotifier;

  /// 当前进度
  OtaProgress get currentProgress => _progressNotifier.value;

  /// 是否正在升级
  bool get isUpgrading => currentProgress.status.isInProgress;

  /// 当前设备
  BluetoothDevice? get currentDevice => _currentDevice;

  /// 开始OTA升级
  /// [device] 目标设备
  /// [settings] OTA设置
  Future<OtaResult> startOta(
    BluetoothDevice device,
    OtaSettingsModel settings,
  ) async {
    debugPrint('🚀 [OTA] 开始OTA升级');
    debugPrint('📱 [OTA] 设备: ${device.platformName}');
    debugPrint('⚙️ [OTA] 协议: ${settings.protocol.name}');
    debugPrint('📁 [OTA] 文件: ${settings.filePath}');

    try {
      // 验证参数
      if (!settings.isValid()) {
        debugPrint('❌ [OTA] 设置验证失败');
        throw const OtaDataValidationException('OTA设置无效');
      }

      if (settings.filePath == null) {
        debugPrint('❌ [OTA] 未指定固件文件路径');
        throw const OtaFileException('未指定固件文件路径');
      }

      // 检查是否已在升级中
      if (isUpgrading) {
        debugPrint('❌ [OTA] OTA升级正在进行中');
        throw const OtaDeviceStateException('OTA升级正在进行中');
      }

      // 重置状态
      debugPrint('🔄 [OTA] 重置状态');
      _resetState();
      _currentDevice = device;
      _currentSettings = settings;
      _upgradeStartTime = DateTime.now();

      // 更新进度
      debugPrint('📡 [OTA] 连接设备中...');
      _updateProgress(OtaProgress.connecting());

      // 初始化GATT助手
      debugPrint('🔧 [OTA] 初始化GATT助手');
      await _initializeGattHelper();

      // 加载固件文件
      debugPrint('📂 [OTA] 加载固件文件');
      await _loadFirmwareFile();

      // 加载安全启动文件（如果需要）
      if (settings.securityBootEnable &&
          settings.securityBootFilePath != null) {
        debugPrint('🔐 [OTA] 加载安全启动文件');
        await _loadSecurityBootFile();
      }

      // 执行OTA升级
      debugPrint('⚡ [OTA] 开始执行OTA升级');
      final result = await _performOtaUpgrade();

      // 更新最终进度
      if (result.isSuccess) {
        debugPrint('✅ [OTA] 升级成功完成');
        _updateProgress(OtaProgress.completed());
      } else {
        debugPrint('❌ [OTA] 升级失败: ${result.shortDescription}');
        _updateProgress(OtaProgress.failed(result.shortDescription));
      }

      return result;
    } catch (e) {
      final errorMessage = e is OtaException ? e.message : e.toString();
      debugPrint('💥 [OTA] 升级异常: $errorMessage');
      _updateProgress(OtaProgress.failed(errorMessage));

      return OtaResult.failure(
        errorMessage: errorMessage,
        exception: e is Exception ? e : Exception(e.toString()),
        startTime: _upgradeStartTime,
        endTime: DateTime.now(),
        totalBytesTransferred: _bytesTransferred,
        totalPacketsTransferred: _currentPacketIndex,
      );
    } finally {
      await _cleanup();
    }
  }

  /// 取消OTA升级
  Future<void> cancelOta() async {
    if (!isUpgrading) return;

    _isCancelled = true;
    _updateProgress(OtaProgress.cancelled());

    try {
      // 发送结束命令
      if (_gattHelper != null && _gattHelper!.isInitialized) {
        final endCommand = OtaCommands.buildOtaEnd(0, 0);
        await _gattHelper!.writeData(endCommand);
      }
    } catch (e) {
      // 忽略取消时的错误
    }

    await _cleanup();
  }

  /// 重置状态
  void _resetState() {
    _isCancelled = false;
    _sendFinish = false; // 重置发送完成标志
    _currentPacketIndex = 0;
    _totalPackets = 0;
    _bytesTransferred = 0;
    _speedSamples.clear();
    _lastSpeedSampleTime = null;
    _firmwareInfo = null;
    _securityBootInfo = null;
  }

  /// 初始化GATT助手
  Future<void> _initializeGattHelper() async {
    _gattHelper = OtaGattHelper(_currentDevice!);
    await _gattHelper!.initialize();
  }

  /// 加载固件文件
  Future<void> _loadFirmwareFile() async {
    debugPrint('📂 [OTA] 正在加载固件文件: ${_currentSettings!.filePath}');
    _firmwareInfo = await _fileDataSource.loadFirmwareFile(
      _currentSettings!.filePath!,
    );

    // 计算总包数
    final pduLength = _currentSettings!.pduLength;
    _totalPackets = (_firmwareInfo!.fileSize / pduLength).ceil();

    debugPrint('📊 [OTA] 固件文件信息:');
    debugPrint('   - 文件大小: ${_firmwareInfo!.fileSize} 字节');
    debugPrint('   - PDU长度: $pduLength 字节');
    debugPrint('   - 总包数: $_totalPackets 包');
    final pid = _firmwareInfo!.pid;
    final vid = _firmwareInfo!.vid;
    if (pid != null) {
      debugPrint('   - PID: 0x${pid.toRadixString(16).toUpperCase()}');
    }
    if (vid != null) {
      debugPrint('   - VID: 0x${vid.toRadixString(16).toUpperCase()}');
    }
  }

  /// 加载安全启动文件
  Future<void> _loadSecurityBootFile() async {
    _securityBootInfo = await _fileDataSource.loadSecurityBootFile(
      _currentSettings!.securityBootFilePath!,
    );
  }

  /// 执行OTA升级
  Future<OtaResult> _performOtaUpgrade() async {
    try {
      _updateProgress(OtaProgress.preparing());

      // 根据协议类型执行不同的升级流程
      if (_currentSettings!.protocol == OtaProtocol.legacy) {
        await _performLegacyOta();
      } else {
        await _performExtendOta();
      }

      return OtaResult.success(
        startTime: _upgradeStartTime,
        endTime: DateTime.now(),
        totalBytesTransferred: _bytesTransferred,
        totalPacketsTransferred: _currentPacketIndex,
        averageTransferSpeed: _calculateAverageSpeed(),
      );
    } catch (e) {
      if (e is OtaException) rethrow;
      throw OtaProtocolException('OTA升级失败: $e');
    }
  }

  /// 执行Legacy协议OTA
  Future<void> _performLegacyOta() async {
    debugPrint('🔄 [OTA] 执行Legacy协议OTA');

    try {
      // 1. 发送OTA版本查询命令（对所有协议都发送，参考iOS源码第649行）
      debugPrint('📤 [OTA] 发送版本查询命令');
      final versionCommand = OtaCommands.buildOtaVersion();
      await _gattHelper!.writeData(versionCommand);

      // 2. 发送OTA开始命令（参考iOS源码第658行）
      debugPrint('📤 [OTA] 发送Legacy开始命令');
      final startCommand = OtaCommands.buildOtaStart();
      await _gattHelper!.writeData(startCommand);

      // 3. 立即主动读取响应（参考iOS源码第660行）
      debugPrint('📖 [OTA] 立即主动读取响应');
      final response = await _gattHelper!.waitForResponse(useRead: true);

      // 4. 验证响应（模拟iOS源码中isReadBeforeOtaIndex0的逻辑）
      debugPrint(
        '🔍 [OTA] 验证Legacy响应: ${response.map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ')}',
      );
      if (response.isEmpty) {
        throw const OtaProtocolException('Legacy协议响应为空');
      }
      debugPrint('✅ [OTA] Legacy响应验证通过，开始数据传输');

      // 5. 传输固件数据
      debugPrint('📤 [OTA] 开始传输固件数据');
      await _transferFirmwareData();

      // 6. 发送结束命令
      debugPrint('📤 [OTA] 发送结束命令');
      final crc32 = OtaCommands.calculateCrc32(_firmwareInfo!.data);
      debugPrint(
        '🔐 [OTA] 计算CRC32: 0x${crc32.toRadixString(16).toUpperCase()}',
      );
      final endCommand = OtaCommands.buildOtaEnd(
        _firmwareInfo!.fileSize,
        crc32,
      );
      await _gattHelper!.writeData(endCommand);

      // 设置发送完成标志（参考iOS源码第927行：self.sendFinish = YES）
      _sendFinish = true;
      debugPrint('✅ [OTA] Legacy结束命令已发送，设置sendFinish=true');

      // Legacy协议：设备会主动断开连接表示成功（参考iOS源码第566-568行）
      debugPrint('⏳ [OTA] 等待设备断开连接（Legacy协议成功标志）');

      // 等待设备断开连接，这是Legacy协议的正常成功流程
      await Future.delayed(const Duration(seconds: 3));
      debugPrint('✅ [OTA] Legacy协议OTA完成');
    } catch (e) {
      // 检查是否是设备断开连接
      if (e.toString().contains('device is not connected') ||
          e.toString().contains('GATT_ERROR')) {
        if (_sendFinish) {
          debugPrint('🎉 [OTA] Legacy协议：数据发送完成后设备断开，升级成功！');
          return; // 这是正常的成功流程
        } else {
          debugPrint('❌ [OTA] Legacy协议：数据未发送完成就断开，升级失败！');
          debugPrint('   当前进度: $_currentPacketIndex/$_totalPackets 包');
          throw OtaProtocolException(
            'Legacy协议：设备过早断开连接（${_currentPacketIndex}/$_totalPackets包）',
          );
        }
      }
      rethrow; // 其他异常重新抛出
    }
  }

  /// 执行Extend协议OTA
  Future<void> _performExtendOta() async {
    debugPrint('🔄 [OTA] 执行Extend协议OTA');

    // 1. 版本比较（如果启用）
    if (_currentSettings!.versionCompare) {
      debugPrint('🔍 [OTA] 执行版本比较');
      await _performVersionComparison();
    }

    // 2. 发送OTA扩展开始命令
    debugPrint('📤 [OTA] 发送Extend开始命令');
    final startCommand = OtaCommands.buildOtaStartExtend();
    await _gattHelper!.writeData(startCommand);

    debugPrint('⏳ [OTA] 等待开始命令响应');
    final startResponse = await _gattHelper!.waitForResponse();
    if (!OtaResponseParser.isSuccessResponse(startResponse)) {
      final resultCode = OtaResponseParser.getResultCode(startResponse);
      debugPrint('❌ [OTA] 开始命令失败: ${resultCode?.name ?? "未知错误"}');
      throw OtaExceptionFactory.fromResultCode(
        resultCode ?? OtaResultCode.packetInvalid,
      );
    }
    debugPrint('✅ [OTA] 开始命令成功');

    // 3. 安全启动处理（如果启用）
    if (_currentSettings!.securityBootEnable && _securityBootInfo != null) {
      debugPrint('🔐 [OTA] 传输安全启动数据');
      await _transferSecurityBootData();
    }

    // 4. 传输固件数据
    debugPrint('📤 [OTA] 开始传输固件数据');
    await _transferFirmwareData();

    // 5. 发送结束命令
    debugPrint('📤 [OTA] 发送结束命令');
    final crc32 = OtaCommands.calculateCrc32(_firmwareInfo!.data);
    debugPrint('🔐 [OTA] 计算CRC32: 0x${crc32.toRadixString(16).toUpperCase()}');
    final endCommand = OtaCommands.buildOtaEnd(_firmwareInfo!.fileSize, crc32);
    await _gattHelper!.writeData(endCommand);

    debugPrint('⏳ [OTA] 等待结束命令响应');
    final endResponse = await _gattHelper!.waitForResponse(
      timeout: const Duration(seconds: 10),
    );
    if (!OtaResponseParser.isSuccessResponse(endResponse)) {
      final resultCode = OtaResponseParser.getResultCode(endResponse);
      debugPrint('❌ [OTA] 结束命令失败: ${resultCode?.name ?? "未知错误"}');
      throw OtaExceptionFactory.fromResultCode(
        resultCode ?? OtaResultCode.packetInvalid,
      );
    }
    debugPrint('✅ [OTA] Extend协议OTA完成');
  }

  /// 执行版本比较
  Future<void> _performVersionComparison() async {
    final versionRequest = OtaCommands.buildFirmwareVersionRequest(
      _currentSettings!.binVersion,
      _currentSettings!.versionCompare,
    );

    await _gattHelper!.writeData(versionRequest);

    final versionResponse = await _gattHelper!.waitForResponse(
      timeout: const Duration(seconds: kFirmwareVersionRequestTimeout),
    );

    final parsedResponse = OtaResponseParser.parseFirmwareVersionResponse(
      versionResponse,
    );
    if (parsedResponse == null) {
      throw const OtaProtocolException('版本响应解析失败');
    }

    if (!parsedResponse.upgradeAllowed) {
      throw OtaExceptionFactory.versionMismatch(
        parsedResponse.currentVersion,
        parsedResponse.targetVersion,
      );
    }
  }

  /// 传输安全启动数据
  Future<void> _transferSecurityBootData() async {
    if (_securityBootInfo == null) return;

    // 传输公钥数据 (8个包，每包8字节)
    for (int i = 0; i < 8; i++) {
      if (_isCancelled) throw const OtaCancelledException();

      final packet = OtaCommands.buildPublicKeyPacket(
        _securityBootInfo!.publicKeyData,
        i,
      );
      await _gattHelper!.writeData(packet);
      await Future.delayed(
        Duration(milliseconds: _currentSettings!.writeInterval),
      );
    }

    // 传输签名数据 (8个包，每包8字节)
    for (int i = 0; i < 8; i++) {
      if (_isCancelled) throw const OtaCancelledException();

      final packet = OtaCommands.buildSignaturePacket(
        _securityBootInfo!.signatureData,
        i,
      );
      await _gattHelper!.writeData(packet);
      await Future.delayed(
        Duration(milliseconds: _currentSettings!.writeInterval),
      );
    }
  }

  /// 传输固件数据
  Future<void> _transferFirmwareData() async {
    debugPrint('📤 [OTA] 开始传输固件数据');
    final pduLength = _currentSettings!.pduLength;
    final firmwareData = _firmwareInfo!.data;

    _currentPacketIndex = 0;
    _bytesTransferred = 0;

    debugPrint('📊 [OTA] 传输参数:');
    debugPrint('   - 数据总长度: ${firmwareData.length} 字节');
    debugPrint('   - PDU长度: $pduLength 字节');
    debugPrint('   - 预计包数: $_totalPackets 包');

    // 使用递归方式发送数据包，模拟iOS的回调机制
    await _sendOtaPartData();

    // 传输完成日志
    final totalTime = DateTime.now().difference(_upgradeStartTime!);
    final avgSpeed = _bytesTransferred / totalTime.inSeconds;
    final avgSpeedStr = '${(avgSpeed / 1024).toStringAsFixed(1)} KB/s';
    debugPrint('✅ [OTA] 固件数据传输完成');
    debugPrint('📊 [OTA] 传输统计:');
    debugPrint('   - 总字节数: $_bytesTransferred');
    debugPrint('   - 总包数: $_totalPackets');
    debugPrint('   - 传输时间: ${totalTime.inSeconds}秒');
    debugPrint('   - 平均速度: $avgSpeedStr');
  }

  /// 发送OTA数据包（递归方式，模拟iOS源码的sendOTAPartData方法）
  Future<void> _sendOtaPartData() async {
    if (_isCancelled) throw const OtaCancelledException();

    final firmwareData = _firmwareInfo!.data;
    final pduLength = _currentSettings!.pduLength;

    // 检查是否传输完成（参考iOS源码第923行）
    final remainingBytes = firmwareData.length - _bytesTransferred;
    if (remainingBytes == 0) {
      // 传输完成，返回到主流程
      return;
    }

    // 计算当前包的数据长度
    final currentPacketSize = remainingBytes < pduLength
        ? remainingBytes
        : pduLength;

    // 提取当前包数据
    final packetData = firmwareData.sublist(
      _bytesTransferred,
      _bytesTransferred + currentPacketSize,
    );

    // 构建数据包
    final packet = OtaCommands.buildOtaDataPacket(
      packetData,
      _currentPacketIndex,
    );

    // 发送数据包
    await _gattHelper!.writeData(packet);

    // 更新统计信息
    _currentPacketIndex++;
    _bytesTransferred += currentPacketSize;

    // 更新传输速度
    _updateTransferSpeed(currentPacketSize);

    // 更新进度
    final progress = _bytesTransferred / firmwareData.length;
    final estimatedTimeRemaining = _calculateEstimatedTimeRemaining();

    _updateProgress(
      OtaProgress.upgrading(
        progress: progress,
        bytesTransferred: _bytesTransferred,
        totalBytes: firmwareData.length,
        currentPacketIndex: _currentPacketIndex,
        totalPackets: _totalPackets,
        transferSpeed: _calculateCurrentSpeed(),
        estimatedTimeRemaining: estimatedTimeRemaining,
      ),
    );

    // 每100包打印一次进度
    if (_currentPacketIndex % 100 == 0 ||
        _currentPacketIndex == _totalPackets) {
      final progressPercent = (progress * 100).toStringAsFixed(1);
      final speed = _calculateCurrentSpeed();
      final speedStr = speed > 0
          ? '${(speed / 1024).toStringAsFixed(1)} KB/s'
          : '计算中...';
      debugPrint(
        '📊 [OTA] 传输进度: $progressPercent% ($_currentPacketIndex/$_totalPackets包) - $speedStr',
      );
    }

    // 继续发送下一个包（模拟iOS 11+的连续发送方式）
    // 现代蓝牙实现不需要手动读取间隔，依赖蓝牙栈的流控机制
    if (remainingBytes > currentPacketSize) {
      // 添加少量延迟避免过快发送导致设备缓冲区溢出
      await Future.delayed(
        Duration(milliseconds: _currentSettings!.writeInterval),
      );
      await _sendOtaPartData();
    }
  }

  /// 更新传输速度
  void _updateTransferSpeed(int bytesTransferred) {
    final now = DateTime.now();

    if (_lastSpeedSampleTime != null) {
      final timeDiff = now.difference(_lastSpeedSampleTime!).inMilliseconds;
      if (timeDiff > 0) {
        final speed = (bytesTransferred * 1000) / timeDiff; // bytes/second
        _speedSamples.add(speed);

        // 保持最近10个样本
        if (_speedSamples.length > 10) {
          _speedSamples.removeAt(0);
        }
      }
    }

    _lastSpeedSampleTime = now;
  }

  /// 计算当前传输速度
  double _calculateCurrentSpeed() {
    if (_speedSamples.isEmpty) return 0.0;
    return _speedSamples.reduce((a, b) => a + b) / _speedSamples.length;
  }

  /// 计算平均传输速度
  double _calculateAverageSpeed() {
    if (_upgradeStartTime == null || _bytesTransferred == 0) return 0.0;

    final duration = DateTime.now().difference(_upgradeStartTime!);
    final seconds = duration.inMilliseconds / 1000.0;

    return seconds > 0 ? _bytesTransferred / seconds : 0.0;
  }

  /// 计算预估剩余时间
  int? _calculateEstimatedTimeRemaining() {
    final currentSpeed = _calculateCurrentSpeed();
    if (currentSpeed <= 0 || _firmwareInfo == null) return null;

    final remainingBytes = _firmwareInfo!.fileSize - _bytesTransferred;
    return (remainingBytes / currentSpeed).round();
  }

  /// 更新进度
  void _updateProgress(OtaProgress progress) {
    _progressNotifier.value = progress;
  }

  /// 清理资源
  Future<void> _cleanup() async {
    try {
      await _gattHelper?.dispose();
    } catch (e) {
      // 忽略清理时的错误
    }

    _gattHelper = null;
    _currentDevice = null;
    _currentSettings = null;
    _firmwareInfo = null;
    _securityBootInfo = null;
  }

  /// 获取OTA状态信息
  Map<String, dynamic> getStatusInfo() {
    return {
      'isUpgrading': isUpgrading,
      'currentStatus': currentProgress.status.name,
      'progress': currentProgress.progress,
      'bytesTransferred': _bytesTransferred,
      'totalBytes': _firmwareInfo?.fileSize ?? 0,
      'currentPacketIndex': _currentPacketIndex,
      'totalPackets': _totalPackets,
      'transferSpeed': _calculateCurrentSpeed(),
      'averageSpeed': _calculateAverageSpeed(),
      'estimatedTimeRemaining': _calculateEstimatedTimeRemaining(),
      'deviceInfo': _gattHelper?.getDeviceInfo(),
    };
  }

  /// 释放资源
  void dispose() {
    _cleanup();
    _progressNotifier.dispose();
    _instance = null;
  }
}
