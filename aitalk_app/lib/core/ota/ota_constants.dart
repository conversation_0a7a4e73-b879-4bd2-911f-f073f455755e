// OTA常量定义
// 移植自Telink Generic OTA iOS库
// Author: Auto-generated by AI

import 'package:flutter_blue_plus/flutter_blue_plus.dart';

/// Telink Generic OTA库版本
const String kTelinkGenericOTALibVersion = "v2.0.7";

/// OTA服务UUID
final Guid OTA_SERVICE_UUID = Guid("00010203-0405-0607-0809-0a0b0c0d1912");

/// OTA特征UUID
final Guid OTA_CHARACTERISTIC_UUID = Guid(
  "00010203-0405-0607-0809-0a0b0c0d2b12",
);

/// OTA写入间隔（毫秒）
/// 增加间隔以避免设备缓冲区溢出导致连接断开
const int kOTAWriteInterval = 15;

/// OTA读取间隔
const int kOTAReadInterval = 8;

/// OTA读取超时（秒）
const int kOTAReadTimeout = 5;

/// 默认PDU长度（字节）
const int kDefaultPduLength = 16;

/// 最大重试次数
const int kMaxRetryCount = 3;

/// 默认连接超时（秒）
const int kDefaultConnectionTimeout = 10;

/// 固件版本请求超时（秒）
const int kFirmwareVersionRequestTimeout = 10;
