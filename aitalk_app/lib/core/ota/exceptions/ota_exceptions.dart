// OTA异常定义
// Author: Auto-generated by AI

import '../protocol/ota_protocol.dart';

/// OTA基础异常
abstract class OtaException implements Exception {
  final String message;
  final dynamic cause;

  const OtaException(this.message, [this.cause]);

  @override
  String toString() => 'OtaException: $message${cause != null ? ' (cause: $cause)' : ''}';
}

/// 连接异常
class OtaConnectionException extends OtaException {
  const OtaConnectionException(super.message, [super.cause]);

  @override
  String toString() => 'OtaConnectionException: $message${cause != null ? ' (cause: $cause)' : ''}';
}

/// 服务发现异常
class OtaServiceException extends OtaException {
  const OtaServiceException(super.message, [super.cause]);

  @override
  String toString() => 'OtaServiceException: $message${cause != null ? ' (cause: $cause)' : ''}';
}

/// 协议异常
class OtaProtocolException extends OtaException {
  final OtaResultCode? resultCode;

  const OtaProtocolException(super.message, [this.resultCode, super.cause]);

  @override
  String toString() => 'OtaProtocolException: $message${resultCode != null ? ' (code: ${resultCode!.name})' : ''}${cause != null ? ' (cause: $cause)' : ''}';
}

/// 文件异常
class OtaFileException extends OtaException {
  const OtaFileException(super.message, [super.cause]);

  @override
  String toString() => 'OtaFileException: $message${cause != null ? ' (cause: $cause)' : ''}';
}

/// 超时异常
class OtaTimeoutException extends OtaException {
  final Duration timeout;

  const OtaTimeoutException(super.message, this.timeout, [super.cause]);

  @override
  String toString() => 'OtaTimeoutException: $message (timeout: ${timeout.inSeconds}s)${cause != null ? ' (cause: $cause)' : ''}';
}

/// 取消异常
class OtaCancelledException extends OtaException {
  const OtaCancelledException([super.message = '用户取消操作']);

  @override
  String toString() => 'OtaCancelledException: $message';
}

/// 版本异常
class OtaVersionException extends OtaException {
  final int? currentVersion;
  final int? targetVersion;

  const OtaVersionException(super.message, [this.currentVersion, this.targetVersion, super.cause]);

  @override
  String toString() {
    final versionInfo = currentVersion != null && targetVersion != null 
        ? ' (current: 0x${currentVersion!.toRadixString(16)}, target: 0x${targetVersion!.toRadixString(16)})'
        : '';
    return 'OtaVersionException: $message$versionInfo${cause != null ? ' (cause: $cause)' : ''}';
  }
}

/// 数据校验异常
class OtaDataValidationException extends OtaException {
  const OtaDataValidationException(super.message, [super.cause]);

  @override
  String toString() => 'OtaDataValidationException: $message${cause != null ? ' (cause: $cause)' : ''}';
}

/// 设备状态异常
class OtaDeviceStateException extends OtaException {
  const OtaDeviceStateException(super.message, [super.cause]);

  @override
  String toString() => 'OtaDeviceStateException: $message${cause != null ? ' (cause: $cause)' : ''}';
}

/// 安全启动异常
class OtaSecurityBootException extends OtaException {
  const OtaSecurityBootException(super.message, [super.cause]);

  @override
  String toString() => 'OtaSecurityBootException: $message${cause != null ? ' (cause: $cause)' : ''}';
}

/// OTA异常工厂类
class OtaExceptionFactory {
  const OtaExceptionFactory._();

  /// 从结果代码创建异常
  static OtaException fromResultCode(OtaResultCode resultCode, [String? additionalMessage]) {
    final message = additionalMessage != null 
        ? '${resultCode.description}: $additionalMessage'
        : resultCode.description;

    switch (resultCode) {
      case OtaResultCode.success:
        throw ArgumentError('Cannot create exception from success result code');
      
      case OtaResultCode.dataPacketSequenceError:
      case OtaResultCode.packetInvalid:
      case OtaResultCode.dataCRCError:
      case OtaResultCode.dataUncomplete:
        return OtaProtocolException(message, resultCode);
      
      case OtaResultCode.writeFlashError:
        return OtaDeviceStateException(message, resultCode);
      
      case OtaResultCode.firmwareSizeError:
      case OtaResultCode.firmwareMarkError:
      case OtaResultCode.firmwareCRCError:
      case OtaResultCode.firmwareEncryptError:
      case OtaResultCode.firmwareTypeError:
        return OtaFileException(message, resultCode);
      
      case OtaResultCode.versionError:
        return OtaVersionException(message, null, null, resultCode);
      
      case OtaResultCode.pduLengthError:
        return OtaDataValidationException(message, resultCode);
      
      case OtaResultCode.secbootSignVerifyFail:
        return OtaSecurityBootException(message, resultCode);
    }
  }

  /// 创建连接异常
  static OtaConnectionException connectionFailed(String reason, [dynamic cause]) {
    return OtaConnectionException('连接失败: $reason', cause);
  }

  /// 创建服务发现异常
  static OtaServiceException serviceNotFound(String serviceName) {
    return OtaServiceException('未找到$serviceName服务');
  }

  /// 创建特征发现异常
  static OtaServiceException characteristicNotFound(String characteristicName) {
    return OtaServiceException('未找到$characteristicName特征');
  }

  /// 创建文件读取异常
  static OtaFileException fileReadFailed(String filePath, [dynamic cause]) {
    return OtaFileException('读取文件失败: $filePath', cause);
  }

  /// 创建文件格式异常
  static OtaFileException invalidFileFormat(String reason) {
    return OtaFileException('文件格式无效: $reason');
  }

  /// 创建超时异常
  static OtaTimeoutException timeout(String operation, Duration timeout, [dynamic cause]) {
    return OtaTimeoutException('$operation超时', timeout, cause);
  }

  /// 创建版本不匹配异常
  static OtaVersionException versionMismatch(int currentVersion, int targetVersion) {
    return OtaVersionException(
      '版本不匹配，无法升级',
      currentVersion,
      targetVersion,
    );
  }

  /// 创建设备断开异常
  static OtaConnectionException deviceDisconnected() {
    return const OtaConnectionException('设备意外断开连接');
  }

  /// 创建数据校验异常
  static OtaDataValidationException dataValidationFailed(String reason) {
    return OtaDataValidationException('数据校验失败: $reason');
  }
}
