// 文件数据处理服务
// 移植自Telink Generic OTA iOS库
// Author: Auto-generated by AI

import 'dart:io';
import 'dart:typed_data';
import '../exceptions/ota_exceptions.dart';

/// 固件文件信息
class FirmwareInfo {
  /// 文件路径
  final String filePath;
  
  /// 文件大小
  final int fileSize;
  
  /// PID (Product ID)
  final int pid;
  
  /// VID (Vendor ID)
  final int vid;
  
  /// 文件数据
  final Uint8List data;

  const FirmwareInfo({
    required this.filePath,
    required this.fileSize,
    required this.pid,
    required this.vid,
    required this.data,
  });

  @override
  String toString() {
    return 'FirmwareInfo(path: $filePath, size: $fileSize, pid: 0x${pid.toRadixString(16)}, vid: 0x${vid.toRadixString(16)})';
  }
}

/// 安全启动文件信息
class SecurityBootInfo {
  /// 公钥数据 (64字节)
  final Uint8List publicKeyData;
  
  /// 签名数据 (64字节)
  final Uint8List signatureData;
  
  /// 原始文件数据
  final Uint8List rawData;

  const SecurityBootInfo({
    required this.publicKeyData,
    required this.signatureData,
    required this.rawData,
  });

  @override
  String toString() {
    return 'SecurityBootInfo(publicKey: ${publicKeyData.length}bytes, signature: ${signatureData.length}bytes)';
  }
}

/// 文件数据源服务
class FileDataSource {
  FileDataSource._();
  
  static final FileDataSource _instance = FileDataSource._();
  static FileDataSource get instance => _instance;

  /// 读取固件文件
  /// [filePath] 文件路径
  Future<FirmwareInfo> loadFirmwareFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw OtaExceptionFactory.fileReadFailed(filePath, 'File does not exist');
      }

      final data = await file.readAsBytes();
      if (data.isEmpty) {
        throw OtaExceptionFactory.fileReadFailed(filePath, 'File is empty');
      }

      // 验证文件格式
      if (!_isValidBinFile(data)) {
        throw OtaExceptionFactory.invalidFileFormat('Not a valid bin file');
      }

      final pid = _extractPid(data);
      final vid = _extractVid(data);

      return FirmwareInfo(
        filePath: filePath,
        fileSize: data.length,
        pid: pid,
        vid: vid,
        data: data,
      );
    } catch (e) {
      if (e is OtaException) rethrow;
      throw OtaExceptionFactory.fileReadFailed(filePath, e);
    }
  }

  /// 读取安全启动文件
  /// [filePath] 安全启动文件路径
  Future<SecurityBootInfo> loadSecurityBootFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw OtaExceptionFactory.fileReadFailed(filePath, 'Security boot file does not exist');
      }

      final data = await file.readAsBytes();
      if (data.length < 30) {
        throw OtaExceptionFactory.invalidFileFormat('Security boot file too small');
      }

      final publicKeyData = _extractPublicKeyData(data);
      final signatureData = _extractSignatureData(data);

      if (publicKeyData.length != 64) {
        throw OtaExceptionFactory.invalidFileFormat('Invalid public key data length');
      }

      if (signatureData.length != 64) {
        throw OtaExceptionFactory.invalidFileFormat('Invalid signature data length');
      }

      return SecurityBootInfo(
        publicKeyData: publicKeyData,
        signatureData: signatureData,
        rawData: data,
      );
    } catch (e) {
      if (e is OtaException) rethrow;
      throw OtaExceptionFactory.fileReadFailed(filePath, e);
    }
  }

  /// 验证bin文件格式
  bool _isValidBinFile(Uint8List data) {
    // 基本长度检查
    if (data.length < 6) return false;
    
    // 可以添加更多的格式验证逻辑
    // 例如检查文件头、魔数等
    
    return true;
  }

  /// 提取PID (Product ID)
  /// 从偏移0x2处读取2字节
  int _extractPid(Uint8List data) {
    if (data.length < 4) return 0;
    
    final byteData = ByteData.sublistView(data);
    return byteData.getUint16(0x2, Endian.little);
  }

  /// 提取VID (Vendor ID)
  /// 从偏移0x4处读取2字节
  int _extractVid(Uint8List data) {
    if (data.length < 6) return 0;
    
    final byteData = ByteData.sublistView(data);
    return byteData.getUint16(0x4, Endian.little);
  }

  /// 提取公钥数据
  /// 从安全启动文件中提取64字节公钥数据
  Uint8List _extractPublicKeyData(Uint8List data) {
    if (data.length < 94) { // 至少需要30 + 64字节
      throw OtaExceptionFactory.invalidFileFormat('File too small for public key extraction');
    }

    // 从文件末尾-30字节处读取偏移量
    final byteData = ByteData.sublistView(data);
    final offset = byteData.getUint16(data.length - 30, Endian.little);
    
    if (data.length < offset + 64) {
      throw OtaExceptionFactory.invalidFileFormat('Invalid public key offset');
    }

    return data.sublist(offset, offset + 64);
  }

  /// 提取签名数据
  /// 从安全启动文件中提取64字节签名数据
  Uint8List _extractSignatureData(Uint8List data) {
    if (data.length < 158) { // 至少需要30 + 64 + 64字节
      throw OtaExceptionFactory.invalidFileFormat('File too small for signature extraction');
    }

    // 从文件末尾-30字节处读取偏移量
    final byteData = ByteData.sublistView(data);
    final offset = byteData.getUint16(data.length - 30, Endian.little);
    
    if (data.length < offset + 128) {
      throw OtaExceptionFactory.invalidFileFormat('Invalid signature offset');
    }

    // 签名数据在公钥数据之后
    return data.sublist(offset + 64, offset + 128);
  }

  /// 获取文件扩展名
  String getFileExtension(String filePath) {
    final lastDotIndex = filePath.lastIndexOf('.');
    if (lastDotIndex == -1) return '';
    return filePath.substring(lastDotIndex + 1).toLowerCase();
  }

  /// 验证文件是否为bin格式
  bool isBinFile(String filePath) {
    return getFileExtension(filePath) == 'bin';
  }

  /// 获取文件大小
  Future<int> getFileSize(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) return 0;
      return await file.length();
    } catch (e) {
      return 0;
    }
  }

  /// 验证文件是否存在且可读
  Future<bool> isFileAccessible(String filePath) async {
    try {
      final file = File(filePath);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  /// 从文件名提取版本信息
  String? extractVersionFromFileName(String fileName) {
    // 匹配版本号模式：v1.2.3 或 1.2.3
    final regex = RegExp(r'v?(\d+\.\d+\.\d+)');
    final match = regex.firstMatch(fileName);
    return match?.group(1);
  }

  /// 计算文件MD5哈希
  Future<String> calculateFileMd5(String filePath) async {
    try {
      final file = File(filePath);
      final data = await file.readAsBytes();
      // 这里需要导入crypto包来计算MD5
      // 为了简化，暂时返回空字符串
      return '';
    } catch (e) {
      throw OtaExceptionFactory.fileReadFailed(filePath, e);
    }
  }

  /// 分块读取大文件
  Stream<Uint8List> readFileInChunks(String filePath, {int chunkSize = 1024}) async* {
    try {
      final file = File(filePath);
      final stream = file.openRead();
      
      await for (final chunk in stream) {
        yield Uint8List.fromList(chunk);
      }
    } catch (e) {
      throw OtaExceptionFactory.fileReadFailed(filePath, e);
    }
  }

  /// 验证固件文件完整性
  Future<bool> validateFirmwareIntegrity(FirmwareInfo firmware) async {
    try {
      // 检查文件大小
      if (firmware.fileSize != firmware.data.length) {
        return false;
      }

      // 检查PID和VID是否有效
      if (firmware.pid == 0 && firmware.vid == 0) {
        return false;
      }

      // 可以添加更多的完整性检查
      // 例如CRC校验、文件头验证等

      return true;
    } catch (e) {
      return false;
    }
  }
}
