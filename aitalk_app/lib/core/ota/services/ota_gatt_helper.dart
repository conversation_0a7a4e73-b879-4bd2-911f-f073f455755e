// OTA GATT助手类
// 移植自Telink Generic OTA iOS库
// Author: Auto-generated by AI

import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';

import '../ota_constants.dart';
import '../exceptions/ota_exceptions.dart';
import '../protocol/ota_response_parser.dart';

/// OTA GATT助手类
/// 负责与蓝牙设备的OTA服务进行通信
class OtaGattHelper {
  /// 蓝牙设备
  final BluetoothDevice device;

  /// OTA服务
  BluetoothService? _otaService;

  /// OTA特征
  BluetoothCharacteristic? _otaCharacteristic;

  /// 通知订阅
  StreamSubscription<List<int>>? _notificationSubscription;

  /// 响应数据流控制器
  final StreamController<Uint8List> _responseController =
      StreamController<Uint8List>.broadcast();

  /// 是否已连接
  bool _isConnected = false;

  /// 是否已初始化
  bool _isInitialized = false;

  OtaGattHelper(this.device);

  /// 响应数据流
  Stream<Uint8List> get responseStream => _responseController.stream;

  /// 是否已连接
  bool get isConnected => _isConnected;

  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  /// 初始化OTA服务
  Future<void> initialize() async {
    debugPrint('🔧 [GATT] 初始化OTA GATT服务');
    try {
      // 检查设备连接状态
      debugPrint('🔍 [GATT] 检查设备连接状态');
      final connectionState = await device.connectionState.first;
      if (connectionState != BluetoothConnectionState.connected) {
        debugPrint('❌ [GATT] 设备未连接');
        throw OtaExceptionFactory.connectionFailed('设备未连接');
      }

      _isConnected = true;
      debugPrint('✅ [GATT] 设备已连接');

      // 发现服务
      debugPrint('🔍 [GATT] 发现蓝牙服务');
      final services = await device.discoverServices();
      debugPrint('📋 [GATT] 发现 ${services.length} 个服务');

      // 查找OTA服务
      debugPrint('🔍 [GATT] 查找OTA服务: ${OTA_SERVICE_UUID.str}');
      _otaService = services.firstWhere(
        (service) => service.uuid == OTA_SERVICE_UUID,
        orElse: () => throw OtaExceptionFactory.serviceNotFound('OTA服务'),
      );
      debugPrint('✅ [GATT] 找到OTA服务');

      // 查找OTA特征
      debugPrint('🔍 [GATT] 查找OTA特征: ${OTA_CHARACTERISTIC_UUID.str}');
      _otaCharacteristic = _otaService!.characteristics.firstWhere(
        (characteristic) => characteristic.uuid == OTA_CHARACTERISTIC_UUID,
        orElse: () => throw OtaExceptionFactory.characteristicNotFound('OTA特征'),
      );
      debugPrint('✅ [GATT] 找到OTA特征');

      // 检查特征属性
      if (!_otaCharacteristic!.properties.write &&
          !_otaCharacteristic!.properties.writeWithoutResponse) {
        throw const OtaServiceException('OTA特征不支持写入');
      }

      if (!_otaCharacteristic!.properties.notify &&
          !_otaCharacteristic!.properties.indicate) {
        throw const OtaServiceException('OTA特征不支持通知');
      }

      // 启用通知
      await _enableNotifications();

      _isInitialized = true;
    } catch (e) {
      _isConnected = false;
      _isInitialized = false;
      if (e is OtaException) rethrow;
      throw OtaExceptionFactory.serviceNotFound('初始化失败: $e');
    }
  }

  /// 启用通知
  Future<void> _enableNotifications() async {
    if (_otaCharacteristic == null) return;

    try {
      // 设置通知
      await _otaCharacteristic!.setNotifyValue(true);

      // 订阅通知
      _notificationSubscription = _otaCharacteristic!.lastValueStream.listen(
        (data) {
          if (data.isNotEmpty) {
            _responseController.add(Uint8List.fromList(data));
          }
        },
        onError: (error) {
          _responseController.addError(
            OtaExceptionFactory.connectionFailed('通知接收错误: $error'),
          );
        },
      );
    } catch (e) {
      throw OtaExceptionFactory.serviceNotFound('启用通知失败: $e');
    }
  }

  /// 写入数据
  /// [data] 要写入的数据
  /// [withResponse] 是否需要响应
  Future<void> writeData(Uint8List data, {bool withResponse = false}) async {
    if (!_isInitialized || _otaCharacteristic == null) {
      throw const OtaDeviceStateException('OTA服务未初始化');
    }

    try {
      // 只在调试模式下打印详细的数据包信息
      if (kDebugMode && data.length <= 20) {
        final hexData = data
            .map((b) => b.toRadixString(16).padLeft(2, '0'))
            .join(' ');
        debugPrint('📤 [GATT] 写入数据 (${data.length}字节): $hexData');
      } else if (data.length > 20) {
        debugPrint('📤 [GATT] 写入数据包 (${data.length}字节)');
      }

      if (withResponse && _otaCharacteristic!.properties.write) {
        await _otaCharacteristic!.write(data, withoutResponse: false);
      } else if (_otaCharacteristic!.properties.writeWithoutResponse) {
        await _otaCharacteristic!.write(data, withoutResponse: true);
      } else {
        throw const OtaServiceException('特征不支持所需的写入类型');
      }
    } catch (e) {
      debugPrint('❌ [GATT] 写入数据失败: $e');
      if (e is OtaException) rethrow;
      throw OtaServiceException('写入数据失败: $e');
    }
  }

  /// 主动读取特征值
  /// 用于Legacy协议，需要主动读取而不是等待通知
  /// 返回读取到的数据
  Future<Uint8List> readCharacteristic() async {
    if (!_isInitialized || _otaCharacteristic == null) {
      throw const OtaDeviceStateException('OTA服务未初始化');
    }

    try {
      debugPrint('📖 [GATT] 主动读取特征值');
      final data = await _otaCharacteristic!.read();
      debugPrint(
        '📖 [GATT] 读取到数据 (${data.length}字节): ${data.map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ')}',
      );
      return Uint8List.fromList(data);
    } catch (e) {
      debugPrint('❌ [GATT] 读取特征值失败: $e');
      throw OtaServiceException('读取特征值失败: $e');
    }
  }

  /// 等待响应
  /// [timeout] 超时时间
  /// [expectedOpcode] 期望的操作码（可选）
  /// [useRead] 是否使用主动读取（Legacy协议需要）
  Future<Uint8List> waitForResponse({
    Duration timeout = const Duration(seconds: 5),
    int? expectedOpcode,
    bool useRead = false,
  }) async {
    try {
      debugPrint('⏳ [GATT] 等待响应 (超时: ${timeout.inSeconds}秒, 主动读取: $useRead)');

      Uint8List response;

      // 如果需要主动读取，直接读取特征值
      if (useRead) {
        response = await readCharacteristic();
      } else {
        // 否则等待通知
        response = await responseStream.timeout(timeout).first;
      }

      // 打印响应数据
      if (kDebugMode && response.length <= 20) {
        final hexData = response
            .map((b) => b.toRadixString(16).padLeft(2, '0'))
            .join(' ');
        debugPrint('📥 [GATT] 收到响应 (${response.length}字节): $hexData');
      } else if (response.length > 20) {
        debugPrint('📥 [GATT] 收到响应 (${response.length}字节)');
      }

      // 如果指定了期望的操作码，进行验证
      if (expectedOpcode != null && response.length >= 2) {
        final receivedOpcode = response[0] | (response[1] << 8);
        if (receivedOpcode != expectedOpcode) {
          debugPrint(
            '❌ [GATT] 操作码不匹配: 期望 0x${expectedOpcode.toRadixString(16)}, 收到 0x${receivedOpcode.toRadixString(16)}',
          );
          throw OtaProtocolException(
            '收到意外的操作码: 0x${receivedOpcode.toRadixString(16)}',
          );
        }
        debugPrint('✅ [GATT] 操作码匹配: 0x${receivedOpcode.toRadixString(16)}');
      }

      return response;
    } on TimeoutException {
      debugPrint('⏰ [GATT] 等待响应超时 (${timeout.inSeconds}秒)');
      throw OtaExceptionFactory.timeout('等待响应', timeout);
    } catch (e) {
      if (e is OtaException) rethrow;
      throw OtaProtocolException('等待响应失败: $e');
    }
  }

  /// 发送命令并等待响应
  /// [command] 要发送的命令
  /// [timeout] 超时时间
  /// [expectedOpcode] 期望的响应操作码
  Future<Uint8List> sendCommandAndWaitResponse(
    Uint8List command, {
    Duration timeout = const Duration(seconds: 5),
    int? expectedOpcode,
  }) async {
    await writeData(command);
    return await waitForResponse(
      timeout: timeout,
      expectedOpcode: expectedOpcode,
    );
  }

  /// 批量写入数据包
  /// [packets] 数据包列表
  /// [interval] 写入间隔（毫秒）
  /// [onProgress] 进度回调
  Future<void> writeDataPackets(
    List<Uint8List> packets, {
    int interval = kOTAWriteInterval,
    void Function(int current, int total)? onProgress,
  }) async {
    for (int i = 0; i < packets.length; i++) {
      await writeData(packets[i]);
      onProgress?.call(i + 1, packets.length);

      // 添加写入间隔
      if (i < packets.length - 1 && interval > 0) {
        await Future.delayed(Duration(milliseconds: interval));
      }
    }
  }

  /// 检查设备连接状态
  Future<bool> checkConnection() async {
    try {
      final state = await device.connectionState.first;
      _isConnected = state == BluetoothConnectionState.connected;
      return _isConnected;
    } catch (e) {
      _isConnected = false;
      return false;
    }
  }

  /// 重新连接设备
  Future<void> reconnect() async {
    try {
      await dispose();

      // 重新连接
      await device.connect(
        timeout: const Duration(seconds: kDefaultConnectionTimeout),
      );

      // 重新初始化
      await initialize();
    } catch (e) {
      throw OtaExceptionFactory.connectionFailed('重新连接失败: $e');
    }
  }

  /// 获取设备信息
  Map<String, dynamic> getDeviceInfo() {
    return {
      'deviceId': device.remoteId.str,
      'deviceName': device.platformName,
      'isConnected': _isConnected,
      'isInitialized': _isInitialized,
      'hasOtaService': _otaService != null,
      'hasOtaCharacteristic': _otaCharacteristic != null,
    };
  }

  /// 验证OTA服务可用性
  Future<bool> validateOtaService() async {
    try {
      if (!_isInitialized) return false;

      // 检查服务和特征是否仍然可用
      final services = await device.discoverServices();
      final otaService = services
          .where((s) => s.uuid == OTA_SERVICE_UUID)
          .firstOrNull;
      if (otaService == null) return false;

      final otaChar = otaService.characteristics
          .where((c) => c.uuid == OTA_CHARACTERISTIC_UUID)
          .firstOrNull;
      return otaChar != null;
    } catch (e) {
      return false;
    }
  }

  /// 清理资源
  Future<void> dispose() async {
    try {
      // 取消通知订阅
      await _notificationSubscription?.cancel();
      _notificationSubscription = null;

      // 禁用通知
      if (_otaCharacteristic != null && _isConnected) {
        try {
          await _otaCharacteristic!.setNotifyValue(false);
        } catch (e) {
          // 忽略禁用通知时的错误
        }
      }

      // 关闭响应流
      await _responseController.close();

      _otaService = null;
      _otaCharacteristic = null;
      _isConnected = false;
      _isInitialized = false;
    } catch (e) {
      // 忽略清理时的错误
    }
  }
}
