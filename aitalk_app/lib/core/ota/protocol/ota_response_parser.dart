// OTA响应解析
// 移植自Telink Generic OTA iOS库
// Author: Auto-generated by AI

import 'dart:typed_data';
import 'ota_protocol.dart';

/// OTA响应数据
class OtaResponse {
  /// 操作码
  final OtaOpcode opcode;
  
  /// 原始数据
  final Uint8List rawData;
  
  /// 解析后的数据
  final Map<String, dynamic> parsedData;

  const OtaResponse({
    required this.opcode,
    required this.rawData,
    required this.parsedData,
  });

  @override
  String toString() {
    return 'OtaResponse(opcode: ${opcode.name}, data: $parsedData)';
  }
}

/// 固件版本响应数据
class FirmwareVersionResponse {
  /// 是否允许升级
  final bool upgradeAllowed;
  
  /// 当前固件版本
  final int currentVersion;
  
  /// 目标固件版本
  final int targetVersion;
  
  /// 结果代码
  final OtaResultCode resultCode;

  const FirmwareVersionResponse({
    required this.upgradeAllowed,
    required this.currentVersion,
    required this.targetVersion,
    required this.resultCode,
  });

  @override
  String toString() {
    return 'FirmwareVersionResponse(allowed: $upgradeAllowed, current: 0x${currentVersion.toRadixString(16)}, target: 0x${targetVersion.toRadixString(16)}, result: ${resultCode.name})';
  }
}

/// OTA响应解析器
class OtaResponseParser {
  const OtaResponseParser._();

  /// 解析OTA响应
  /// [data] 响应数据
  static OtaResponse? parseResponse(Uint8List data) {
    if (data.length < 2) return null;
    
    final opcode = OtaOpcode.fromBytes(data.take(2).toList());
    if (opcode == null) return null;
    
    Map<String, dynamic> parsedData = {};
    
    switch (opcode) {
      case OtaOpcode.otaFirmWareVersionResponse:
        parsedData = _parseFirmwareVersionResponse(data);
        break;
      
      case OtaOpcode.otaVersion:
        parsedData = _parseVersionResponse(data);
        break;
      
      default:
        // 对于其他响应，只解析基本信息
        parsedData = _parseBasicResponse(data);
        break;
    }
    
    return OtaResponse(
      opcode: opcode,
      rawData: data,
      parsedData: parsedData,
    );
  }

  /// 解析固件版本响应
  static Map<String, dynamic> _parseFirmwareVersionResponse(Uint8List data) {
    if (data.length < 8) {
      return {'error': '数据长度不足'};
    }
    
    final byteData = ByteData.sublistView(data);
    
    // 跳过操作码 (2字节)
    final resultCode = OtaResultCode.fromByte(byteData.getUint8(2)) ?? OtaResultCode.packetInvalid;
    final currentVersion = byteData.getUint16(3, Endian.little);
    final targetVersion = byteData.getUint16(5, Endian.little);
    final upgradeAllowed = byteData.getUint8(7) == 1;
    
    return {
      'resultCode': resultCode,
      'currentVersion': currentVersion,
      'targetVersion': targetVersion,
      'upgradeAllowed': upgradeAllowed,
    };
  }

  /// 解析版本响应
  static Map<String, dynamic> _parseVersionResponse(Uint8List data) {
    if (data.length < 4) {
      return {'error': '数据长度不足'};
    }
    
    final byteData = ByteData.sublistView(data);
    
    // 跳过操作码 (2字节)
    final version = byteData.getUint16(2, Endian.little);
    
    return {
      'version': version,
    };
  }

  /// 解析基本响应
  static Map<String, dynamic> _parseBasicResponse(Uint8List data) {
    final result = <String, dynamic>{};
    
    // 如果有结果代码字段
    if (data.length > 2) {
      final resultCodeByte = data[2];
      final resultCode = OtaResultCode.fromByte(resultCodeByte);
      if (resultCode != null) {
        result['resultCode'] = resultCode;
      }
    }
    
    // 添加原始数据长度
    result['dataLength'] = data.length;
    
    return result;
  }

  /// 解析固件版本响应为强类型对象
  /// [data] 响应数据
  static FirmwareVersionResponse? parseFirmwareVersionResponse(Uint8List data) {
    final response = parseResponse(data);
    if (response?.opcode != OtaOpcode.otaFirmWareVersionResponse) {
      return null;
    }
    
    final parsedData = response!.parsedData;
    if (parsedData.containsKey('error')) {
      return null;
    }
    
    return FirmwareVersionResponse(
      upgradeAllowed: parsedData['upgradeAllowed'] ?? false,
      currentVersion: parsedData['currentVersion'] ?? 0,
      targetVersion: parsedData['targetVersion'] ?? 0,
      resultCode: parsedData['resultCode'] ?? OtaResultCode.packetInvalid,
    );
  }

  /// 检查响应是否为成功
  /// [data] 响应数据
  static bool isSuccessResponse(Uint8List data) {
    final response = parseResponse(data);
    if (response == null) return false;
    
    final resultCode = response.parsedData['resultCode'] as OtaResultCode?;
    return resultCode == OtaResultCode.success;
  }

  /// 获取响应中的错误代码
  /// [data] 响应数据
  static OtaResultCode? getResultCode(Uint8List data) {
    final response = parseResponse(data);
    return response?.parsedData['resultCode'] as OtaResultCode?;
  }

  /// 验证响应数据完整性
  /// [data] 响应数据
  static bool validateResponse(Uint8List data) {
    if (data.isEmpty) return false;
    
    final response = parseResponse(data);
    if (response == null) return false;
    
    // 检查是否有解析错误
    return !response.parsedData.containsKey('error');
  }

  /// 获取响应描述
  /// [data] 响应数据
  static String getResponseDescription(Uint8List data) {
    final response = parseResponse(data);
    if (response == null) {
      return '无效响应';
    }
    
    final buffer = StringBuffer();
    buffer.write('${_getOpcodeDescription(response.opcode)}响应');
    
    final resultCode = response.parsedData['resultCode'] as OtaResultCode?;
    if (resultCode != null) {
      buffer.write(' - ${resultCode.description}');
    }
    
    return buffer.toString();
  }

  /// 获取操作码描述
  static String _getOpcodeDescription(OtaOpcode opcode) {
    switch (opcode) {
      case OtaOpcode.otaVersion:
        return '版本查询';
      case OtaOpcode.otaStart:
        return 'OTA开始';
      case OtaOpcode.otaEnd:
        return 'OTA结束';
      case OtaOpcode.otaStartExtend:
        return 'OTA扩展开始';
      case OtaOpcode.otaFirmWareVersionRequest:
        return '固件版本请求';
      case OtaOpcode.otaFirmWareVersionResponse:
        return '固件版本';
    }
  }

  /// 解析数据包索引
  /// [data] 数据包
  static int? parsePacketIndex(Uint8List data) {
    if (data.length < 2) return null;
    
    final byteData = ByteData.sublistView(data);
    return byteData.getUint16(0, Endian.little);
  }

  /// 提取数据包内容
  /// [data] 数据包
  static Uint8List? extractPacketData(Uint8List data) {
    if (data.length <= 2) return null;
    
    return data.sublist(2);
  }

  /// 检查是否为OTA相关响应
  /// [data] 响应数据
  static bool isOtaResponse(Uint8List data) {
    if (data.length < 2) return false;
    
    final opcode = OtaOpcode.fromBytes(data.take(2).toList());
    return opcode != null;
  }
}
