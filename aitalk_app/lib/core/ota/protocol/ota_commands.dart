// OTA命令构建
// 移植自Telink Generic OTA iOS库
// Author: Auto-generated by AI

import 'dart:typed_data';
import 'ota_protocol.dart';

/// OTA命令构建器
class OtaCommands {
  const OtaCommands._();

  /// 构建OTA开始命令 (Legacy协议)
  static Uint8List buildOtaStart() {
    final opcode = OtaOpcode.otaStart.toBytes();
    return Uint8List.fromList(opcode);
  }

  /// 构建OTA版本查询命令 (Legacy协议)
  static Uint8List buildOtaVersion() {
    final opcode = OtaOpcode.otaVersion.toBytes();
    return Uint8List.fromList(opcode);
  }

  /// 构建OTA结束命令 (通用)
  /// [totalLength] 固件总长度
  /// [crc32] 固件CRC32校验值
  static Uint8List buildOtaEnd(int totalLength, int crc32) {
    final opcode = OtaOpcode.otaEnd.toBytes();
    final data = ByteData(10);

    // 操作码 (2字节)
    data.setUint8(0, opcode[0]);
    data.setUint8(1, opcode[1]);

    // 总长度 (4字节，小端序)
    data.setUint32(2, totalLength, Endian.little);

    // CRC32 (4字节，小端序)
    data.setUint32(6, crc32, Endian.little);

    return data.buffer.asUint8List();
  }

  /// 构建OTA扩展开始命令 (Extend协议)
  static Uint8List buildOtaStartExtend() {
    final opcode = OtaOpcode.otaStartExtend.toBytes();
    return Uint8List.fromList(opcode);
  }

  /// 构建固件版本请求命令 (Extend协议)
  /// [firmwareVersion] 固件版本号
  /// [versionCompare] 是否进行版本比较
  static Uint8List buildFirmwareVersionRequest(
    int firmwareVersion,
    bool versionCompare,
  ) {
    final opcode = OtaOpcode.otaFirmWareVersionRequest.toBytes();
    final data = ByteData(5);

    // 操作码 (2字节)
    data.setUint8(0, opcode[0]);
    data.setUint8(1, opcode[1]);

    // 固件版本 (2字节，小端序)
    data.setUint16(2, firmwareVersion, Endian.little);

    // 版本比较标志 (1字节)
    data.setUint8(4, versionCompare ? 1 : 0);

    return data.buffer.asUint8List();
  }

  /// 构建OTA数据包
  /// [data] 数据内容
  /// [index] 包索引
  /// 严格按照iOS源码：[2字节索引] + [数据] + [0xFF填充] + [2字节CRC16]
  static Uint8List buildOtaDataPacket(Uint8List data, int index) {
    // 计算需要填充到16字节倍数的长度（参考iOS源码第1076行）
    final packet16Count = (data.length / 16.0).ceil();
    final paddedDataLength = packet16Count * 16;
    final dataWithIndexLength = paddedDataLength + 2; // +2字节索引
    final totalPacketLength = dataWithIndexLength + 2; // +2字节CRC16

    final packet = ByteData(totalPacketLength);

    // 包索引 (2字节，小端序) - 参考iOS源码第1070-1073行
    packet.setUint16(0, index, Endian.little);

    // 原始数据内容 - 参考iOS源码第1074-1075行
    for (int i = 0; i < data.length; i++) {
      packet.setUint8(i + 2, data[i]);
    }

    // 用0xFF填充到16字节倍数（参考iOS源码第1078-1082行）
    for (int i = data.length; i < paddedDataLength; i++) {
      packet.setUint8(i + 2, 0xFF);
    }

    // 计算CRC16校验（参考iOS源码第1084-1089行）
    final dataForCrc = packet.buffer.asUint8List(0, dataWithIndexLength);
    final crc16 = _calculateCrc16(dataForCrc);
    packet.setUint16(dataWithIndexLength, crc16, Endian.little);

    return packet.buffer.asUint8List();
  }

  /// 计算CRC16校验码（参考iOS源码crc16函数）
  static int _calculateCrc16(Uint8List data) {
    int crc = 0x0000;
    const int polynomial = 0x1021;

    for (int byte in data) {
      crc ^= (byte << 8);
      for (int i = 0; i < 8; i++) {
        if ((crc & 0x8000) != 0) {
          crc = ((crc << 1) ^ polynomial) & 0xFFFF;
        } else {
          crc = (crc << 1) & 0xFFFF;
        }
      }
    }

    return crc;
  }

  /// 构建设置固件索引命令
  /// [firmwareIndex] 固件索引 (0-255)
  static Uint8List buildSetFirmwareIndex(int firmwareIndex) {
    if (firmwareIndex < 0 || firmwareIndex > 255) {
      throw ArgumentError('固件索引必须在0-255范围内');
    }

    final data = ByteData(3);
    // 这里使用自定义命令码，具体值需要根据实际协议确定
    data.setUint16(0, 0xFF10, Endian.little); // 假设的命令码
    data.setUint8(2, firmwareIndex);

    return data.buffer.asUint8List();
  }

  /// 构建公钥数据包 (安全启动)
  /// [publicKeyData] 公钥数据 (64字节)
  /// [packetIndex] 包索引
  static Uint8List buildPublicKeyPacket(
    Uint8List publicKeyData,
    int packetIndex,
  ) {
    if (publicKeyData.length != 64) {
      throw ArgumentError('公钥数据必须为64字节');
    }

    // 每个包8字节数据 + 2字节索引
    const packetSize = 8;
    final startOffset = packetIndex * packetSize;
    final endOffset = (startOffset + packetSize).clamp(0, publicKeyData.length);

    final packet = ByteData(packetSize + 2);

    // 包索引 (2字节，小端序)
    packet.setUint16(0, packetIndex, Endian.little);

    // 数据内容
    for (int i = 0; i < packetSize && startOffset + i < endOffset; i++) {
      packet.setUint8(i + 2, publicKeyData[startOffset + i]);
    }

    return packet.buffer.asUint8List();
  }

  /// 构建签名数据包 (安全启动)
  /// [signatureData] 签名数据 (64字节)
  /// [packetIndex] 包索引
  static Uint8List buildSignaturePacket(
    Uint8List signatureData,
    int packetIndex,
  ) {
    if (signatureData.length != 64) {
      throw ArgumentError('签名数据必须为64字节');
    }

    // 每个包8字节数据 + 2字节索引
    const packetSize = 8;
    final startOffset = packetIndex * packetSize;
    final endOffset = (startOffset + packetSize).clamp(0, signatureData.length);

    final packet = ByteData(packetSize + 2);

    // 包索引 (2字节，小端序)
    packet.setUint16(0, packetIndex + 8, Endian.little); // 签名包从索引8开始

    // 数据内容
    for (int i = 0; i < packetSize && startOffset + i < endOffset; i++) {
      packet.setUint8(i + 2, signatureData[startOffset + i]);
    }

    return packet.buffer.asUint8List();
  }

  /// 计算CRC32校验值
  /// [data] 要计算校验值的数据
  static int calculateCrc32(Uint8List data) {
    const int polynomial = 0xEDB88320;
    int crc = 0xFFFFFFFF;

    for (int byte in data) {
      crc ^= byte;
      for (int i = 0; i < 8; i++) {
        if ((crc & 1) != 0) {
          crc = (crc >> 1) ^ polynomial;
        } else {
          crc >>= 1;
        }
      }
    }

    return ~crc & 0xFFFFFFFF;
  }

  /// 验证命令格式
  /// [command] 要验证的命令
  static bool validateCommand(Uint8List command) {
    if (command.length < 2) return false;

    final opcode = OtaOpcode.fromBytes(command.take(2).toList());
    if (opcode == null) return false;

    // 根据不同命令验证长度
    switch (opcode) {
      case OtaOpcode.otaStart:
      case OtaOpcode.otaVersion:
      case OtaOpcode.otaStartExtend:
        return command.length == 2;

      case OtaOpcode.otaEnd:
        return command.length == 10;

      case OtaOpcode.otaFirmWareVersionRequest:
        return command.length == 5;

      case OtaOpcode.otaFirmWareVersionResponse:
        return command.length >= 2; // 响应长度可变
    }
  }

  /// 解析命令操作码
  /// [command] 命令数据
  static OtaOpcode? parseOpcode(Uint8List command) {
    if (command.length < 2) return null;
    return OtaOpcode.fromBytes(command.take(2).toList());
  }

  /// 获取命令描述
  /// [opcode] 操作码
  static String getCommandDescription(OtaOpcode opcode) {
    switch (opcode) {
      case OtaOpcode.otaVersion:
        return 'OTA版本查询';
      case OtaOpcode.otaStart:
        return 'OTA开始 (Legacy)';
      case OtaOpcode.otaEnd:
        return 'OTA结束';
      case OtaOpcode.otaStartExtend:
        return 'OTA开始 (Extend)';
      case OtaOpcode.otaFirmWareVersionRequest:
        return '固件版本请求';
      case OtaOpcode.otaFirmWareVersionResponse:
        return '固件版本响应';
    }
  }
}
