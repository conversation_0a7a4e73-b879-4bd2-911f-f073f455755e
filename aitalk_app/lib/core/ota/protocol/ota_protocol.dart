// OTA协议定义和枚举
// 移植自Telink Generic OTA iOS库
// Author: Auto-generated by AI

/// OTA协议类型
enum OtaProtocol {
  /// 传统协议
  legacy(0),
  /// 扩展协议
  extend(1);

  const OtaProtocol(this.value);
  final int value;
}

/// OTA操作码
/// 参考: AN_20111001-C_Telink B91 BLE Single Connection SDK Developer Handbook.pdf (page.303)
enum OtaOpcode {
  /// (Legacy)获得slave当前firmware版本号的命令
  otaVersion(0xFF00),
  /// (Legacy)OTA升级开始命令
  otaStart(0xFF01),
  /// (All)结束命令，OTA中的legacy和extend protocol均采用该命令
  otaEnd(0xFF02),
  /// (Extend)extend protocol中的OTA升级开始命令
  otaStartExtend(0xFF03),
  /// (Extend)OTA升级过程中的版本比较请求命令
  otaFirmWareVersionRequest(0xFF04),
  /// (Extend)版本响应命令
  otaFirmWareVersionResponse(0xFF05);

  const OtaOpcode(this.value);
  final int value;

  /// 将操作码转换为字节数组
  List<int> toBytes() {
    return [value & 0xFF, (value >> 8) & 0xFF];
  }

  /// 从字节数组解析操作码
  static OtaOpcode? fromBytes(List<int> bytes) {
    if (bytes.length < 2) return null;
    final value = bytes[0] | (bytes[1] << 8);
    for (final opcode in OtaOpcode.values) {
      if (opcode.value == value) return opcode;
    }
    return null;
  }
}

/// OTA结果代码
/// 参考: AN_20111001-C_Telink B91 BLE Single Connection SDK Developer Handbook.pdf (page.307)
enum OtaResultCode {
  /// 成功
  success(0x00),
  /// OTA数据包序列号错误：重复的OTA PDU或丢失某些OTA PDU
  dataPacketSequenceError(0x01),
  /// 无效的OTA包：1.无效的OTA命令；2.addr_index超出范围；3.非标准OTA PDU长度
  packetInvalid(0x02),
  /// 包PDU CRC错误
  dataCRCError(0x03),
  /// 将OTA数据写入flash错误
  writeFlashError(0x04),
  /// 丢失最后一个或多个OTA PDU
  dataUncomplete(0x05),
  /// 固件大小错误
  firmwareSizeError(0x06),
  /// 固件标记错误
  firmwareMarkError(0x07),
  /// 固件CRC错误
  firmwareCRCError(0x08),
  /// 固件加密错误
  firmwareEncryptError(0x09),
  /// 版本错误
  versionError(0x0A),
  /// PDU长度错误
  pduLengthError(0x0B),
  /// 固件类型错误
  firmwareTypeError(0x0C),
  /// 安全启动签名验证失败
  secbootSignVerifyFail(0x86);

  const OtaResultCode(this.value);
  final int value;

  /// 从字节值获取结果代码
  static OtaResultCode? fromByte(int byte) {
    for (final code in OtaResultCode.values) {
      if (code.value == byte) return code;
    }
    return null;
  }

  /// 获取错误描述
  String get description {
    switch (this) {
      case OtaResultCode.success:
        return '升级成功';
      case OtaResultCode.dataPacketSequenceError:
        return 'OTA数据包序列号错误';
      case OtaResultCode.packetInvalid:
        return '无效的OTA包';
      case OtaResultCode.dataCRCError:
        return '包PDU CRC错误';
      case OtaResultCode.writeFlashError:
        return '写入flash错误';
      case OtaResultCode.dataUncomplete:
        return '数据不完整';
      case OtaResultCode.firmwareSizeError:
        return '固件大小错误';
      case OtaResultCode.firmwareMarkError:
        return '固件标记错误';
      case OtaResultCode.firmwareCRCError:
        return '固件CRC错误';
      case OtaResultCode.firmwareEncryptError:
        return '固件加密错误';
      case OtaResultCode.versionError:
        return '版本错误';
      case OtaResultCode.pduLengthError:
        return 'PDU长度错误';
      case OtaResultCode.firmwareTypeError:
        return '固件类型错误';
      case OtaResultCode.secbootSignVerifyFail:
        return '安全启动签名验证失败';
    }
  }
}

/// OTA状态
enum OtaStatus {
  /// 空闲状态
  idle,
  /// 连接中
  connecting,
  /// 准备中
  preparing,
  /// 升级中
  upgrading,
  /// 完成
  completed,
  /// 失败
  failed,
  /// 已取消
  cancelled;

  /// 是否为进行中状态
  bool get isInProgress {
    return this == OtaStatus.connecting ||
           this == OtaStatus.preparing ||
           this == OtaStatus.upgrading;
  }

  /// 是否为最终状态
  bool get isFinal {
    return this == OtaStatus.completed ||
           this == OtaStatus.failed ||
           this == OtaStatus.cancelled;
  }
}
