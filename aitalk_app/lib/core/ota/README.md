# Telink OTA 模块

这个模块提供了完整的Telink设备OTA（Over-The-Air）固件升级功能，从iOS原生代码迁移到Flutter/Dart实现。

## 功能特性

- 支持Legacy和Extend两种OTA协议
- 实时进度监控和速度计算
- 完整的错误处理和重试机制
- 版本比较和安全启动支持
- 文件完整性验证（CRC32）
- 可配置的传输参数

## 核心组件

### TelinkOtaManager
主要的OTA管理器，使用单例模式：

```dart
final otaManager = TelinkOtaManager.instance;

// 监听进度更新
otaManager.progressNotifier.addListener(() {
  final progress = otaManager.progressNotifier.value;
  print('进度: ${progress.progressPercentage}');
});

// 开始OTA升级
final settings = OtaSettingsModel(
  filePath: '/path/to/firmware.bin',
  protocol: OtaProtocol.extend,
);

final result = await otaManager.startOta(device, settings);
if (result.isSuccess) {
  print('升级成功！');
} else {
  print('升级失败: ${result.shortDescription}');
}
```

### OtaSettingsModel
OTA配置模型：

```dart
const settings = OtaSettingsModel(
  filePath: '/path/to/firmware.bin',
  protocol: OtaProtocol.extend,          // 协议类型
  versionCompare: true,                  // 是否进行版本比较
  securityBootEnable: false,             // 是否启用安全启动
  securityBootFilePath: '',              // 安全启动文件路径
  maxRetryCount: 3,                      // 最大重试次数
  packetSize: 16,                        // 数据包大小
  connectionTimeout: Duration(seconds: 10), // 连接超时
  responseTimeout: Duration(seconds: 3),     // 响应超时
);
```

### OtaProgress
进度信息模型：

```dart
// 监听进度变化
ValueListenableBuilder<OtaProgress>(
  valueListenable: otaManager.progressNotifier,
  builder: (context, progress, child) {
    return Column(
      children: [
        Text(progress.stepDescription),
        LinearProgressIndicator(value: progress.progress),
        Text(progress.progressPercentage),
        if (progress.transferSpeed > 0)
          Text(progress.transferSpeedString),
      ],
    );
  },
)
```

## 协议支持

### Legacy协议
- 适用于较老的Telink设备
- 简单的数据传输流程
- 基本的错误处理

### Extend协议
- 适用于新版Telink设备
- 支持版本比较
- 支持安全启动
- 更完善的错误处理

## 错误处理

模块提供了完整的异常层次结构：

```dart
try {
  await otaManager.startOta(device, settings);
} on OtaFileException catch (e) {
  // 文件相关错误
  print('文件错误: ${e.message}');
} on OtaConnectionException catch (e) {
  // 连接相关错误
  print('连接错误: ${e.message}');
} on OtaProtocolException catch (e) {
  // 协议相关错误
  print('协议错误: ${e.message}');
} on OtaException catch (e) {
  // 通用OTA错误
  print('OTA错误: ${e.message}');
}
```

## 文件要求

### 固件文件(.bin)
- 必须是有效的Telink固件文件
- 文件大小不能为0
- 支持PID/VID提取和验证

### 安全启动文件
- 仅在启用安全启动时需要
- 必须与固件文件匹配

## 使用示例

### 基本OTA升级

```dart
class OtaUpgradeExample extends StatefulWidget {
  @override
  _OtaUpgradeExampleState createState() => _OtaUpgradeExampleState();
}

class _OtaUpgradeExampleState extends State<OtaUpgradeExample> {
  final _otaManager = TelinkOtaManager.instance;
  
  Future<void> _startUpgrade() async {
    final settings = OtaSettingsModel(
      filePath: '/path/to/firmware.bin',
      protocol: OtaProtocol.extend,
    );
    
    try {
      final result = await _otaManager.startOta(device, settings);
      if (result.isSuccess) {
        _showSuccess('升级成功！');
      } else {
        _showError('升级失败: ${result.shortDescription}');
      }
    } catch (e) {
      _showError('升级异常: $e');
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<OtaProgress>(
      valueListenable: _otaManager.progressNotifier,
      builder: (context, progress, child) {
        return Column(
          children: [
            Text(progress.stepDescription),
            LinearProgressIndicator(value: progress.progress),
            ElevatedButton(
              onPressed: progress.status.isInProgress ? null : _startUpgrade,
              child: Text(progress.status.isInProgress ? '升级中...' : '开始升级'),
            ),
          ],
        );
      },
    );
  }
}
```

## 注意事项

1. **设备连接**: 确保设备已正确连接且支持OTA服务
2. **文件路径**: 确保固件文件路径正确且文件可读
3. **权限**: 确保应用有读取文件的权限
4. **内存管理**: 大文件传输时注意内存使用
5. **错误处理**: 始终处理可能的异常情况
6. **UI更新**: 使用ValueListenableBuilder监听进度更新

## 测试

运行单元测试：

```bash
flutter test test/core/ota/
```

## 依赖

- flutter_blue_plus: 蓝牙通信
- flutter/foundation: ValueNotifier支持
- dart:async: 异步操作支持
