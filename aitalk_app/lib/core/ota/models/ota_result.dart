// OTA结果模型
// Author: Auto-generated by AI

import '../protocol/ota_protocol.dart';

/// OTA升级结果
class OtaResult {
  /// 是否成功
  final bool isSuccess;
  
  /// 结果代码
  final OtaResultCode? resultCode;
  
  /// 错误消息
  final String? errorMessage;
  
  /// 异常对象
  final Exception? exception;
  
  /// 升级开始时间
  final DateTime? startTime;
  
  /// 升级结束时间
  final DateTime? endTime;
  
  /// 传输的总字节数
  final int totalBytesTransferred;
  
  /// 传输的总包数
  final int totalPacketsTransferred;
  
  /// 平均传输速度 (bytes/second)
  final double averageTransferSpeed;

  const OtaResult({
    required this.isSuccess,
    this.resultCode,
    this.errorMessage,
    this.exception,
    this.startTime,
    this.endTime,
    this.totalBytesTransferred = 0,
    this.totalPacketsTransferred = 0,
    this.averageTransferSpeed = 0.0,
  });

  /// 创建成功结果
  factory OtaResult.success({
    DateTime? startTime,
    DateTime? endTime,
    int totalBytesTransferred = 0,
    int totalPacketsTransferred = 0,
    double averageTransferSpeed = 0.0,
  }) {
    return OtaResult(
      isSuccess: true,
      resultCode: OtaResultCode.success,
      startTime: startTime,
      endTime: endTime,
      totalBytesTransferred: totalBytesTransferred,
      totalPacketsTransferred: totalPacketsTransferred,
      averageTransferSpeed: averageTransferSpeed,
    );
  }

  /// 创建失败结果
  factory OtaResult.failure({
    OtaResultCode? resultCode,
    String? errorMessage,
    Exception? exception,
    DateTime? startTime,
    DateTime? endTime,
    int totalBytesTransferred = 0,
    int totalPacketsTransferred = 0,
    double averageTransferSpeed = 0.0,
  }) {
    return OtaResult(
      isSuccess: false,
      resultCode: resultCode,
      errorMessage: errorMessage,
      exception: exception,
      startTime: startTime,
      endTime: endTime,
      totalBytesTransferred: totalBytesTransferred,
      totalPacketsTransferred: totalPacketsTransferred,
      averageTransferSpeed: averageTransferSpeed,
    );
  }

  /// 创建取消结果
  factory OtaResult.cancelled({
    DateTime? startTime,
    DateTime? endTime,
    int totalBytesTransferred = 0,
    int totalPacketsTransferred = 0,
    double averageTransferSpeed = 0.0,
  }) {
    return OtaResult(
      isSuccess: false,
      errorMessage: '用户取消升级',
      startTime: startTime,
      endTime: endTime,
      totalBytesTransferred: totalBytesTransferred,
      totalPacketsTransferred: totalPacketsTransferred,
      averageTransferSpeed: averageTransferSpeed,
    );
  }

  /// 获取升级耗时
  Duration? get duration {
    if (startTime == null || endTime == null) return null;
    return endTime!.difference(startTime!);
  }

  /// 获取耗时字符串
  String get durationString {
    final dur = duration;
    if (dur == null) return '未知';
    
    final totalSeconds = dur.inSeconds;
    if (totalSeconds < 60) {
      return '${totalSeconds}秒';
    } else if (totalSeconds < 3600) {
      final minutes = totalSeconds ~/ 60;
      final seconds = totalSeconds % 60;
      return '${minutes}分${seconds}秒';
    } else {
      final hours = totalSeconds ~/ 3600;
      final minutes = (totalSeconds % 3600) ~/ 60;
      return '${hours}小时${minutes}分';
    }
  }

  /// 获取传输速度字符串
  String get averageTransferSpeedString {
    if (averageTransferSpeed < 1024) {
      return '${averageTransferSpeed.toStringAsFixed(1)} B/s';
    } else if (averageTransferSpeed < 1024 * 1024) {
      return '${(averageTransferSpeed / 1024).toStringAsFixed(1)} KB/s';
    } else {
      return '${(averageTransferSpeed / (1024 * 1024)).toStringAsFixed(1)} MB/s';
    }
  }

  /// 获取传输信息字符串
  String get transferInfoString {
    final sizeKB = (totalBytesTransferred / 1024).toStringAsFixed(1);
    return '$sizeKB KB, $totalPacketsTransferred 包';
  }

  /// 获取详细描述
  String get detailDescription {
    final buffer = StringBuffer();
    
    if (isSuccess) {
      buffer.writeln('✅ OTA升级成功');
    } else {
      buffer.writeln('❌ OTA升级失败');
    }
    
    if (resultCode != null) {
      buffer.writeln('结果代码: ${resultCode!.name} (0x${resultCode!.value.toRadixString(16).toUpperCase()})');
      buffer.writeln('描述: ${resultCode!.description}');
    }
    
    if (errorMessage != null) {
      buffer.writeln('错误信息: $errorMessage');
    }
    
    if (duration != null) {
      buffer.writeln('耗时: $durationString');
    }
    
    if (totalBytesTransferred > 0) {
      buffer.writeln('传输数据: $transferInfoString');
      buffer.writeln('平均速度: $averageTransferSpeedString');
    }
    
    return buffer.toString().trim();
  }

  /// 获取简短描述
  String get shortDescription {
    if (isSuccess) {
      return '升级成功';
    } else if (resultCode != null) {
      return resultCode!.description;
    } else if (errorMessage != null) {
      return errorMessage!;
    } else {
      return '升级失败';
    }
  }

  @override
  String toString() {
    return 'OtaResult(isSuccess: $isSuccess, resultCode: ${resultCode?.name}, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OtaResult &&
        other.isSuccess == isSuccess &&
        other.resultCode == resultCode &&
        other.errorMessage == errorMessage &&
        other.startTime == startTime &&
        other.endTime == endTime &&
        other.totalBytesTransferred == totalBytesTransferred &&
        other.totalPacketsTransferred == totalPacketsTransferred &&
        other.averageTransferSpeed == averageTransferSpeed;
  }

  @override
  int get hashCode {
    return Object.hash(
      isSuccess,
      resultCode,
      errorMessage,
      startTime,
      endTime,
      totalBytesTransferred,
      totalPacketsTransferred,
      averageTransferSpeed,
    );
  }
}
