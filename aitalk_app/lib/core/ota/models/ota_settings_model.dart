// OTA设置模型
// 移植自Telink Generic OTA iOS库
// Author: Auto-generated by AI

import '../protocol/ota_protocol.dart';
import '../ota_constants.dart';

/// OTA设置模型
class OtaSettingsModel {
  /// 服务UUID字符串
  final String serviceUuidString;
  
  /// 特征UUID字符串
  final String characteristicUuidString;
  
  /// 读取间隔
  final int readInterval;
  
  /// 写入间隔（毫秒）
  final int writeInterval;
  
  /// 固件文件路径
  final String? filePath;
  
  /// 是否需要设置固件索引
  final bool needSetFirmwareIndex;
  
  /// 固件索引值（0-255）
  final int firmwareIndex;
  
  /// OTA协议类型
  final OtaProtocol protocol;
  
  /// 版本比较标志
  final bool versionCompare;
  
  /// Bin文件版本号
  final int binVersion;
  
  /// Extend模式的OTA，发送单个OTA包的最大OTA数据长度
  /// 为16的倍数，范围是16*1~16*15，默认为16*1
  final int pduLength;
  
  /// Extend模式的OTA，是否启用安全启动
  final bool securityBootEnable;
  
  /// 安全启动的描述符bin文件路径
  final String? securityBootFilePath;

  const OtaSettingsModel({
    this.serviceUuidString = "00010203-0405-0607-0809-0a0b0c0d1912",
    this.characteristicUuidString = "00010203-0405-0607-0809-0a0b0c0d2b12",
    this.readInterval = kOTAReadInterval,
    this.writeInterval = kOTAWriteInterval,
    this.filePath,
    this.needSetFirmwareIndex = false,
    this.firmwareIndex = 0,
    this.protocol = OtaProtocol.legacy,
    this.versionCompare = false,
    this.binVersion = 0,
    this.pduLength = kDefaultPduLength,
    this.securityBootEnable = false,
    this.securityBootFilePath,
  });

  /// 从另一个OtaSettingsModel创建副本
  OtaSettingsModel.fromOther(OtaSettingsModel other)
      : serviceUuidString = other.serviceUuidString,
        characteristicUuidString = other.characteristicUuidString,
        readInterval = other.readInterval,
        writeInterval = other.writeInterval,
        filePath = other.filePath,
        needSetFirmwareIndex = other.needSetFirmwareIndex,
        firmwareIndex = other.firmwareIndex,
        protocol = other.protocol,
        versionCompare = other.versionCompare,
        binVersion = other.binVersion,
        pduLength = other.pduLength,
        securityBootEnable = other.securityBootEnable,
        securityBootFilePath = other.securityBootFilePath;

  /// 创建副本并修改部分属性
  OtaSettingsModel copyWith({
    String? serviceUuidString,
    String? characteristicUuidString,
    int? readInterval,
    int? writeInterval,
    String? filePath,
    bool? needSetFirmwareIndex,
    int? firmwareIndex,
    OtaProtocol? protocol,
    bool? versionCompare,
    int? binVersion,
    int? pduLength,
    bool? securityBootEnable,
    String? securityBootFilePath,
  }) {
    return OtaSettingsModel(
      serviceUuidString: serviceUuidString ?? this.serviceUuidString,
      characteristicUuidString: characteristicUuidString ?? this.characteristicUuidString,
      readInterval: readInterval ?? this.readInterval,
      writeInterval: writeInterval ?? this.writeInterval,
      filePath: filePath ?? this.filePath,
      needSetFirmwareIndex: needSetFirmwareIndex ?? this.needSetFirmwareIndex,
      firmwareIndex: firmwareIndex ?? this.firmwareIndex,
      protocol: protocol ?? this.protocol,
      versionCompare: versionCompare ?? this.versionCompare,
      binVersion: binVersion ?? this.binVersion,
      pduLength: pduLength ?? this.pduLength,
      securityBootEnable: securityBootEnable ?? this.securityBootEnable,
      securityBootFilePath: securityBootFilePath ?? this.securityBootFilePath,
    );
  }

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'serviceUuidString': serviceUuidString,
      'characteristicUuidString': characteristicUuidString,
      'readInterval': readInterval,
      'writeInterval': writeInterval,
      'filePath': filePath,
      'needSetFirmwareIndex': needSetFirmwareIndex,
      'firmwareIndex': firmwareIndex,
      'protocol': protocol.value,
      'versionCompare': versionCompare,
      'binVersion': binVersion,
      'pduLength': pduLength,
      'securityBootEnable': securityBootEnable,
      'securityBootFilePath': securityBootFilePath,
    };
  }

  /// 从Map创建
  factory OtaSettingsModel.fromMap(Map<String, dynamic> map) {
    return OtaSettingsModel(
      serviceUuidString: map['serviceUuidString'] ?? "00010203-0405-0607-0809-0a0b0c0d1912",
      characteristicUuidString: map['characteristicUuidString'] ?? "00010203-0405-0607-0809-0a0b0c0d2b12",
      readInterval: map['readInterval'] ?? kOTAReadInterval,
      writeInterval: map['writeInterval'] ?? kOTAWriteInterval,
      filePath: map['filePath'],
      needSetFirmwareIndex: map['needSetFirmwareIndex'] ?? false,
      firmwareIndex: map['firmwareIndex'] ?? 0,
      protocol: OtaProtocol.values.firstWhere(
        (p) => p.value == (map['protocol'] ?? 0),
        orElse: () => OtaProtocol.legacy,
      ),
      versionCompare: map['versionCompare'] ?? false,
      binVersion: map['binVersion'] ?? 0,
      pduLength: map['pduLength'] ?? kDefaultPduLength,
      securityBootEnable: map['securityBootEnable'] ?? false,
      securityBootFilePath: map['securityBootFilePath'],
    );
  }

  /// 获取详细描述字符串
  String getDetailString() {
    final buffer = StringBuffer();
    buffer.writeln('OTA Settings:');
    buffer.writeln('Service UUID: $serviceUuidString');
    buffer.writeln('Characteristic UUID: $characteristicUuidString');
    buffer.writeln('Read Interval: $readInterval');
    buffer.writeln('Write Interval: $writeInterval ms');
    buffer.writeln('File Path: ${filePath ?? "未设置"}');
    buffer.writeln('Need Set Firmware Index: $needSetFirmwareIndex');
    if (needSetFirmwareIndex) {
      buffer.writeln('Firmware Index: $firmwareIndex');
    }
    buffer.writeln('Protocol: ${protocol == OtaProtocol.legacy ? "Legacy" : "Extend"}');
    if (protocol == OtaProtocol.extend) {
      buffer.writeln('Version Compare: $versionCompare');
      buffer.writeln('Bin Version: 0x${binVersion.toRadixString(16).toUpperCase().padLeft(4, '0')}');
      buffer.writeln('PDU Length: $pduLength');
    }
    buffer.writeln('Security Boot Enable: $securityBootEnable');
    if (securityBootEnable && securityBootFilePath != null) {
      buffer.writeln('Security Boot File Path: $securityBootFilePath');
    }
    return buffer.toString();
  }

  /// 验证设置是否有效
  bool isValid() {
    // 检查基本参数
    if (readInterval <= 0 || writeInterval <= 0) return false;
    if (firmwareIndex < 0 || firmwareIndex > 255) return false;
    
    // 检查PDU长度（必须是16的倍数，范围16-240）
    if (pduLength < 16 || pduLength > 240 || pduLength % 16 != 0) return false;
    
    // 检查版本号
    if (binVersion < 0 || binVersion > 0xFFFF) return false;
    
    return true;
  }

  @override
  String toString() {
    return 'OtaSettingsModel(protocol: ${protocol.name}, pduLength: $pduLength, binVersion: 0x${binVersion.toRadixString(16)})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OtaSettingsModel &&
        other.serviceUuidString == serviceUuidString &&
        other.characteristicUuidString == characteristicUuidString &&
        other.readInterval == readInterval &&
        other.writeInterval == writeInterval &&
        other.filePath == filePath &&
        other.needSetFirmwareIndex == needSetFirmwareIndex &&
        other.firmwareIndex == firmwareIndex &&
        other.protocol == protocol &&
        other.versionCompare == versionCompare &&
        other.binVersion == binVersion &&
        other.pduLength == pduLength &&
        other.securityBootEnable == securityBootEnable &&
        other.securityBootFilePath == securityBootFilePath;
  }

  @override
  int get hashCode {
    return Object.hash(
      serviceUuidString,
      characteristicUuidString,
      readInterval,
      writeInterval,
      filePath,
      needSetFirmwareIndex,
      firmwareIndex,
      protocol,
      versionCompare,
      binVersion,
      pduLength,
      securityBootEnable,
      securityBootFilePath,
    );
  }
}
