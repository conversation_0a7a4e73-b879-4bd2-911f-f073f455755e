// OTA进度模型
// Author: Auto-generated by AI

import '../protocol/ota_protocol.dart';

/// OTA进度信息
class OtaProgress {
  /// 当前状态
  final OtaStatus status;
  
  /// 进度百分比 (0.0 - 1.0)
  final double progress;
  
  /// 当前步骤描述
  final String stepDescription;
  
  /// 已传输字节数
  final int bytesTransferred;
  
  /// 总字节数
  final int totalBytes;
  
  /// 当前包索引
  final int currentPacketIndex;
  
  /// 总包数
  final int totalPackets;
  
  /// 传输速度 (bytes/second)
  final double transferSpeed;
  
  /// 剩余时间估计 (seconds)
  final int? estimatedTimeRemaining;

  const OtaProgress({
    required this.status,
    required this.progress,
    required this.stepDescription,
    this.bytesTransferred = 0,
    this.totalBytes = 0,
    this.currentPacketIndex = 0,
    this.totalPackets = 0,
    this.transferSpeed = 0.0,
    this.estimatedTimeRemaining,
  });

  /// 创建初始进度
  factory OtaProgress.initial() {
    return const OtaProgress(
      status: OtaStatus.idle,
      progress: 0.0,
      stepDescription: '准备开始',
    );
  }

  /// 创建连接中进度
  factory OtaProgress.connecting() {
    return const OtaProgress(
      status: OtaStatus.connecting,
      progress: 0.0,
      stepDescription: '连接设备中...',
    );
  }

  /// 创建准备中进度
  factory OtaProgress.preparing() {
    return const OtaProgress(
      status: OtaStatus.preparing,
      progress: 0.1,
      stepDescription: '准备升级...',
    );
  }

  /// 创建升级中进度
  factory OtaProgress.upgrading({
    required double progress,
    required int bytesTransferred,
    required int totalBytes,
    required int currentPacketIndex,
    required int totalPackets,
    double transferSpeed = 0.0,
    int? estimatedTimeRemaining,
  }) {
    return OtaProgress(
      status: OtaStatus.upgrading,
      progress: progress,
      stepDescription: '正在升级固件... (${(progress * 100).toInt()}%)',
      bytesTransferred: bytesTransferred,
      totalBytes: totalBytes,
      currentPacketIndex: currentPacketIndex,
      totalPackets: totalPackets,
      transferSpeed: transferSpeed,
      estimatedTimeRemaining: estimatedTimeRemaining,
    );
  }

  /// 创建完成进度
  factory OtaProgress.completed() {
    return const OtaProgress(
      status: OtaStatus.completed,
      progress: 1.0,
      stepDescription: '升级完成',
    );
  }

  /// 创建失败进度
  factory OtaProgress.failed(String errorMessage) {
    return OtaProgress(
      status: OtaStatus.failed,
      progress: 0.0,
      stepDescription: '升级失败: $errorMessage',
    );
  }

  /// 创建取消进度
  factory OtaProgress.cancelled() {
    return const OtaProgress(
      status: OtaStatus.cancelled,
      progress: 0.0,
      stepDescription: '升级已取消',
    );
  }

  /// 创建副本并修改部分属性
  OtaProgress copyWith({
    OtaStatus? status,
    double? progress,
    String? stepDescription,
    int? bytesTransferred,
    int? totalBytes,
    int? currentPacketIndex,
    int? totalPackets,
    double? transferSpeed,
    int? estimatedTimeRemaining,
  }) {
    return OtaProgress(
      status: status ?? this.status,
      progress: progress ?? this.progress,
      stepDescription: stepDescription ?? this.stepDescription,
      bytesTransferred: bytesTransferred ?? this.bytesTransferred,
      totalBytes: totalBytes ?? this.totalBytes,
      currentPacketIndex: currentPacketIndex ?? this.currentPacketIndex,
      totalPackets: totalPackets ?? this.totalPackets,
      transferSpeed: transferSpeed ?? this.transferSpeed,
      estimatedTimeRemaining: estimatedTimeRemaining ?? this.estimatedTimeRemaining,
    );
  }

  /// 获取进度百分比字符串
  String get progressPercentage {
    return '${(progress * 100).toInt()}%';
  }

  /// 获取传输速度字符串
  String get transferSpeedString {
    if (transferSpeed < 1024) {
      return '${transferSpeed.toStringAsFixed(1)} B/s';
    } else if (transferSpeed < 1024 * 1024) {
      return '${(transferSpeed / 1024).toStringAsFixed(1)} KB/s';
    } else {
      return '${(transferSpeed / (1024 * 1024)).toStringAsFixed(1)} MB/s';
    }
  }

  /// 获取剩余时间字符串
  String get estimatedTimeRemainingString {
    if (estimatedTimeRemaining == null) return '未知';
    
    final seconds = estimatedTimeRemaining!;
    if (seconds < 60) {
      return '${seconds}秒';
    } else if (seconds < 3600) {
      final minutes = seconds ~/ 60;
      final remainingSeconds = seconds % 60;
      return '${minutes}分${remainingSeconds}秒';
    } else {
      final hours = seconds ~/ 3600;
      final minutes = (seconds % 3600) ~/ 60;
      return '${hours}小时${minutes}分';
    }
  }

  /// 获取传输信息字符串
  String get transferInfoString {
    if (totalBytes == 0) return '';
    
    final transferredKB = (bytesTransferred / 1024).toStringAsFixed(1);
    final totalKB = (totalBytes / 1024).toStringAsFixed(1);
    
    return '$transferredKB KB / $totalKB KB';
  }

  /// 获取包信息字符串
  String get packetInfoString {
    if (totalPackets == 0) return '';
    return '$currentPacketIndex / $totalPackets 包';
  }

  @override
  String toString() {
    return 'OtaProgress(status: ${status.name}, progress: ${progressPercentage}, step: $stepDescription)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OtaProgress &&
        other.status == status &&
        other.progress == progress &&
        other.stepDescription == stepDescription &&
        other.bytesTransferred == bytesTransferred &&
        other.totalBytes == totalBytes &&
        other.currentPacketIndex == currentPacketIndex &&
        other.totalPackets == totalPackets &&
        other.transferSpeed == transferSpeed &&
        other.estimatedTimeRemaining == estimatedTimeRemaining;
  }

  @override
  int get hashCode {
    return Object.hash(
      status,
      progress,
      stepDescription,
      bytesTransferred,
      totalBytes,
      currentPacketIndex,
      totalPackets,
      transferSpeed,
      estimatedTimeRemaining,
    );
  }
}
