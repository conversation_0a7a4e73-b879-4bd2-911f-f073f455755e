import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';

import '../bluetooth/bluetooth_manager.dart';
import '../services/database_service.dart';
import '../bluetooth/protocol_gatt_helper.dart';
import '../protocol/device_control_request_sender.dart';
import '../protocol/device_control_response_parser.dart';
import '../protocol/at_response_handler.dart';

/// 设备运行状态数据模型，包含电量和各版本号信息。
@immutable
class DeviceStatus {
  const DeviceStatus({
    required this.batteryPercent,
    this.firmwareVersion,
    this.hardwareVersion,
    this.turMassVersion,
  });

  /// 电量百分比 0~100，-1 表示未知
  final int batteryPercent;

  /// BLE 模块固件版本
  final String? firmwareVersion;

  /// 硬件版本
  final String? hardwareVersion;

  /// TurMass MCU 固件版本（通过 AT 指令返回）
  final String? turMassVersion;

  DeviceStatus copyWith({
    int? batteryPercent,
    String? firmwareVersion,
    String? hardwareVersion,
    String? turMassVersion,
  }) {
    return DeviceStatus(
      batteryPercent: batteryPercent ?? this.batteryPercent,
      firmwareVersion: firmwareVersion ?? this.firmwareVersion,
      hardwareVersion: hardwareVersion ?? this.hardwareVersion,
      turMassVersion: turMassVersion ?? this.turMassVersion,
    );
  }

  @override
  String toString() {
    return 'DeviceStatus(battery: $batteryPercent, fw: $firmwareVersion, hw: $hardwareVersion, tur: $turMassVersion)';
  }
}

/// 设备管理：统一负责查询连接设备的电量与版本信息。
///
/// ChatListScreen 等 UI 组件只需要订阅 [statusNotifier] 即可获取最新状态。
class DeviceManager {
  DeviceManager._();

  static final DeviceManager instance = DeviceManager._();

  /// 设备状态通知器
  final ValueNotifier<DeviceStatus> statusNotifier = ValueNotifier(
    const DeviceStatus(batteryPercent: -1),
  );

  /// 当前已解析到的 Device ID（十六进制字符串，如 0x1234ABCD）。
  ///
  /// null 表示尚未获取或连接已断开；"FAILED" 表示解析失败。
  final ValueNotifier<String?> deviceIdNotifier = ValueNotifier(null);

  /// 当前设备名称（从 BLE 广播或协议查询得到）。
  final ValueNotifier<String?> deviceNameNotifier = ValueNotifier(null);

  StreamSubscription? _atSub;
  BluetoothDevice? _device;

  /// 同一设备仅触发一次 BLE 信息拉取
  bool _bleInfoFetched = false;

  /// 开始监听，全局调用一次即可。
  void start() {
    // 监听设备切换
    BluetoothManager.currentDevice.addListener(_onDeviceChanged);
    // 初始化一次
    _onDeviceChanged();

    // 监听 AT 指令响应（获取自定义固件版本等）
    _atSub ??= AtResponseHandler.instance.responses.listen((r) {
      // 1. 处理 DeviceID 响应
      if (r.type == AtResponseType.deviceId) {
        final idVal = r.payload?['deviceId'];
        if (idVal == null) {
          deviceIdNotifier.value = null;
        } else if (idVal == 0) {
          deviceIdNotifier.value = 'FAILED';
        } else {
          deviceIdNotifier.value =
              '0x${idVal.toRadixString(16).padLeft(8, '0').toUpperCase()}';
        }
      }

      if (r.type == AtResponseType.version) {
        final v = r.payload?['version'];
        if (v != null) {
          statusNotifier.value = statusNotifier.value.copyWith(
            turMassVersion: v,
          );
        }

        // AT 指令版本已返回，开始依次获取 BLE 电量/硬件/固件版本
        _tryFetchBleInfoSequence();
      }
    });
  }

  /// 停止监听，应用退出或不再需要时调用。
  void dispose() {
    BluetoothManager.currentDevice.removeListener(_onDeviceChanged);
    _atSub?.cancel();
  }

  void _onDeviceChanged() {
    final device = BluetoothManager.currentDevice.value;
    if (device != null) {
      _device = device;
      _bleInfoFetched = false; // 新设备，重置标记
      deviceNameNotifier.value = device.platformName.isNotEmpty
          ? device.platformName
          : null;
      _fetchOnce(); // 先查询一次电量，提升 UI 及时性
      // 重置 DeviceID 状态为加载中
      deviceIdNotifier.value = null;
    } else {
      _device = null;
      _bleInfoFetched = false;
      statusNotifier.value = const DeviceStatus(batteryPercent: -1);
      // 断开连接时清空 DeviceID
      deviceIdNotifier.value = null;
      deviceNameNotifier.value = null;
    }
  }

  /// 单独查询电量
  Future<int?> fetchBatteryLevel() async {
    if (_device == null) return null;
    try {
      final level = await _queryBatteryLevel(_device!);
      if (level != null) {
        statusNotifier.value = statusNotifier.value.copyWith(
          batteryPercent: level,
        );
      }
      return level;
    } catch (e) {
      debugPrint('DeviceManager fetchBatteryLevel error: $e');
      return null;
    }
  }

  /// 单独查询 BLE 固件版本
  Future<String?> fetchFirmwareVersion() async {
    if (_device == null) return null;
    try {
      final fw = await _queryFirmwareVersion(_device!);
      if (fw != null) {
        statusNotifier.value = statusNotifier.value.copyWith(
          firmwareVersion: fw,
        );
      }
      return fw;
    } catch (e) {
      debugPrint('DeviceManager fetchFirmwareVersion error: $e');
      return null;
    }
  }

  /// 单独查询硬件版本
  Future<String?> fetchHardwareVersion() async {
    if (_device == null) return null;
    try {
      final hw = await _queryHardwareVersion(_device!);
      if (hw != null) {
        statusNotifier.value = statusNotifier.value.copyWith(
          hardwareVersion: hw,
        );
      }
      return hw;
    } catch (e) {
      debugPrint('DeviceManager fetchHardwareVersion error: $e');
      return null;
    }
  }

  /// 内部调用：仅查询电量，减少 BLE 负载
  Future<void> _fetchOnce() async {
    await fetchBatteryLevel();
  }

  /// AT 指令结束后依次拉取 BLE 信息（电量→硬件版本→固件版本）
  Future<void> _tryFetchBleInfoSequence() async {
    if (_bleInfoFetched || _device == null) return;
    _bleInfoFetched = true; // 防抖，确保只执行一次
    try {
      await fetchBatteryLevel();
      await fetchHardwareVersion();
      await fetchFirmwareVersion();

      // 强制通过协议查询设备名称
      final String? resolvedName = await fetchDeviceName();
      if (resolvedName != null && resolvedName.isNotEmpty) {
        deviceNameNotifier.value = resolvedName;
      }

      // 全部信息拉取完毕后，记录到本地数据库
      try {
        final id = deviceIdNotifier.value ?? 'UNRESOLVED';
        DatabaseService.instance.upsertBluetoothDevice(
          deviceId: id,
          name: resolvedName ?? _device!.platformName,
        );
      } catch (e) {
        debugPrint('DeviceManager save bluetooth device error: $e');
      }
    } catch (e) {
      debugPrint('DeviceManager _fetchBleInfoSequence error: $e');
    }
  }

  // ===== 内部 BLE 查询实现 =====
  Future<int?> _queryBatteryLevel(
    BluetoothDevice device, {
    Duration timeout = const Duration(seconds: 2),
  }) async {
    final frameStream = await ProtocolGattHelper.frameStream(device);
    final completer = Completer<int?>();
    late StreamSubscription sub;
    sub = frameStream.listen((frame) {
      final level = DeviceControlResponseParser.tryParseBatteryLevel(frame);
      if (level != null) completer.complete(level);
    });
    await DeviceControlRequestSender.sendBatteryQuery(device);
    int? level;
    try {
      level = await completer.future.timeout(timeout);
    } catch (_) {
      level = null;
    }
    await sub.cancel();
    return level;
  }

  Future<String?> _queryFirmwareVersion(
    BluetoothDevice device, {
    Duration timeout = const Duration(seconds: 2),
  }) async {
    final frameStream = await ProtocolGattHelper.frameStream(device);
    final completer = Completer<String?>();
    late StreamSubscription sub;
    sub = frameStream.listen((frame) {
      final v = DeviceControlResponseParser.tryParseFirmwareVersion(frame);
      if (v != null) completer.complete(v);
    });
    await DeviceControlRequestSender.sendFirmwareVersionQuery(device);
    String? ver;
    try {
      ver = await completer.future.timeout(timeout);
    } catch (_) {
      ver = null;
    }
    await sub.cancel();
    return ver;
  }

  Future<String?> _queryHardwareVersion(
    BluetoothDevice device, {
    Duration timeout = const Duration(seconds: 2),
  }) async {
    final frameStream = await ProtocolGattHelper.frameStream(device);
    final completer = Completer<String?>();
    late StreamSubscription sub;
    sub = frameStream.listen((frame) {
      final v = DeviceControlResponseParser.tryParseHardwareVersion(frame);
      if (v != null) completer.complete(v);
    });
    await DeviceControlRequestSender.sendHardwareVersionQuery(device);
    String? ver;
    try {
      ver = await completer.future.timeout(timeout);
    } catch (_) {
      ver = null;
    }
    await sub.cancel();
    return ver;
  }

  /// 单独查询设备名称
  Future<String?> fetchDeviceName() async {
    if (_device == null) return null;
    try {
      final name = await _queryDeviceName(_device!);
      return name;
    } catch (e) {
      debugPrint('DeviceManager fetchDeviceName error: $e');
      return null;
    }
  }

  Future<String?> _queryDeviceName(
    BluetoothDevice device, {
    Duration timeout = const Duration(seconds: 2),
  }) async {
    final frameStream = await ProtocolGattHelper.frameStream(device);
    final completer = Completer<String?>();
    late StreamSubscription sub;
    sub = frameStream.listen((frame) {
      final n = DeviceControlResponseParser.tryParseDeviceName(frame);
      if (n != null) completer.complete(n);
    });
    await DeviceControlRequestSender.sendDeviceNameQuery(device);
    String? name;
    try {
      name = await completer.future.timeout(timeout);
    } catch (_) {
      name = null;
    }
    await sub.cancel();
    return name;
  }
}
