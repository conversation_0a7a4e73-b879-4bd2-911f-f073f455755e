import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';
import '../core/constants/colors.dart';
import '../core/bluetooth/connection_keeper.dart';
import 'chats/chat_list_screen.dart';
import 'calls/call_list_screen.dart';
import 'contacts/contact_list_screen.dart';
import 'profile/profile_screen.dart';

/// 主导航页面，统一管理底部导航栏和页面切换
class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  int _selectedIndex = 0;

  // 页面列表
  late final List<Widget> _pages;

  @override
  void initState() {
    super.initState();
    _pages = [
      const ChatListScreen(),      // 聊天页面
      const CallListScreen(),      // 通话记录页面
      const ContactListScreen(),   // 联系人页面
      const ProfileScreen(),       // 我的页面
    ];

    // 监听自动重连状态, 显示 SnackBar
    ConnectionKeeper.autoReconnecting.addListener(_onAutoReconnectChanged);
  }

  void _onAutoReconnectChanged() {
    if (!mounted) return;
    final messenger = ScaffoldMessenger.of(context);
    if (ConnectionKeeper.autoReconnecting.value) {
      messenger.showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.autoReconnect_message),
          duration: const Duration(days: 1), // 持续显示, 直到手动取消或成功
          action: SnackBarAction(
            label: AppLocalizations.of(context)!.autoReconnect_cancel,
            onPressed: () {
              ConnectionKeeper.cancelAutoReconnect();
            },
          ),
        ),
      );
    } else {
      messenger.hideCurrentSnackBar();
    }
  }

  @override
  void dispose() {
    ConnectionKeeper.autoReconnecting.removeListener(_onAutoReconnectChanged);
    super.dispose();
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _selectedIndex,
        children: _pages,
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        selectedItemColor: context.brandPrimary,
        unselectedItemColor: context.textSecondaryCol,
        type: BottomNavigationBarType.fixed,
        onTap: _onItemTapped,
        items: [
          BottomNavigationBarItem(
            icon: const Icon(Icons.message),
            label: AppLocalizations.of(context)!.bottomNav_chat,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.call),
            label: AppLocalizations.of(context)!.bottomNav_call,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.people),
            label: AppLocalizations.of(context)!.bottomNav_contacts,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.person),
            label: AppLocalizations.of(context)!.bottomNav_profile,
          ),
        ],
      ),
    );
  }
}