import 'package:flutter/material.dart';
import '../../core/constants/colors.dart';
import '../../l10n/app_localizations.dart';
import '../../main.dart';
import '../../core/services/settings_service.dart';

class ThemeSettingsScreen extends StatefulWidget {
  const ThemeSettingsScreen({super.key});

  @override
  State<ThemeSettingsScreen> createState() => _ThemeSettingsScreenState();
}

class _ThemeSettingsScreenState extends State<ThemeSettingsScreen> {
  late ThemeMode _selected;

  @override
  void initState() {
    super.initState();
    _selected = themeModeNotifier.value;
  }

  void _onSelect(ThemeMode mode) {
    setState(() => _selected = mode);
  }

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context)!;
    return Scaffold(
      backgroundColor: context.bgSecondary,
      appBar: AppBar(
        backgroundColor: context.tileBackground,
        elevation: 0,
        leadingWidth: 88,
        leading: TextButton(
          onPressed: () => Navigator.of(context).pop(),
          style: TextButton.styleFrom(
            minimumSize: Size.zero,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: FittedBox(
            fit: BoxFit.scaleDown,
            alignment: Alignment.centerLeft,
            child: Text(
              loc.createGroup_cancel,
              style: TextStyle(color: context.brandPrimary, fontSize: 16),
            ),
          ),
        ),
        title: Text(
          loc.settings_themeMode,
          style: TextStyle(
            color: context.textPrimaryCol,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: () {
              themeModeNotifier.value = _selected;
              SettingsService.saveThemeMode(_selected);
              Navigator.of(context).pop();
            },
            child: Text(
              loc.createGroup_done,
              style: TextStyle(color: context.brandPrimary, fontSize: 16),
            ),
          ),
        ],
      ),
      body: ListView(
        children: [
          const SizedBox(height: 12),
          _buildOption(title: loc.settings_themeSystem, mode: ThemeMode.system),
          _buildOption(title: loc.settings_themeLight, mode: ThemeMode.light),
          _buildOption(title: loc.settings_themeDark, mode: ThemeMode.dark),
        ],
      ),
    );
  }

  Widget _buildOption({required String title, required ThemeMode mode}) {
    final bool selected = _selected == mode;
    return Container(
      color: context.tileBackground,
      child: ListTile(
        title: Text(
          title,
          style: TextStyle(fontSize: 16, color: context.textPrimaryCol),
        ),
        trailing: selected
            ? Icon(Icons.check, color: context.brandPrimary)
            : null,
        onTap: () => _onSelect(mode),
      ),
    );
  }
}
