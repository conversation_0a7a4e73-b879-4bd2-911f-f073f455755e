import 'dart:io';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';

import '../../core/constants/colors.dart';
import '../../l10n/app_localizations.dart';

/// A screen that displays app storage breakdown and allows
/// clearing cache or managing chat history.
class StorageManagementScreen extends StatefulWidget {
  const StorageManagementScreen({super.key});

  @override
  State<StorageManagementScreen> createState() =>
      _StorageManagementScreenState();
}

class _StorageManagementScreenState extends State<StorageManagementScreen> {
  int _cacheBytes = 0;
  int _chatBytes = 0;
  int _appDataBytes = 0;

  bool _clearingCache = false;

  @override
  void initState() {
    super.initState();
    _loadStorageInfo();
  }

  Future<void> _loadStorageInfo() async {
    final Directory tempDir = await getTemporaryDirectory();
    final Directory docsDir = await getApplicationDocumentsDirectory();

    // 1. Cache size (temporary directory)
    final int cache = await _computeDirectorySize(tempDir);

    // 2. Chat history (SQLite database size)
    const String dbName = 'aitalk_manager.db';
    final File dbFile = File(p.join(docsDir.path, dbName));
    int chat = 0;
    if (await dbFile.exists()) {
      chat = await dbFile.length();
    }

    // 3. App data (documents directory minus database size)
    final int docsTotal = await _computeDirectorySize(docsDir);
    final int appData = math.max(0, docsTotal - chat);

    if (mounted) {
      setState(() {
        _cacheBytes = cache;
        _chatBytes = chat;
        _appDataBytes = appData;
      });
    }
  }

  Future<int> _computeDirectorySize(Directory dir) async {
    int total = 0;
    try {
      await for (var entity in dir.list(recursive: true, followLinks: false)) {
        if (entity is File) {
          total += await entity.length();
        }
      }
    } catch (_) {}
    return total;
  }

  String _formatBytes(int bytes) {
    if (bytes <= 0) return '0 B';
    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
    int i = (math.log(bytes) / math.log(1024)).floor();
    return '${(bytes / math.pow(1024, i)).toStringAsFixed(1)} ${suffixes[i]}';
  }

  Future<void> _clearCache() async {
    setState(() => _clearingCache = true);
    try {
      final tempDir = await getTemporaryDirectory();
      await _deleteDirectoryContents(tempDir);
    } catch (_) {}
    await _loadStorageInfo();
    setState(() => _clearingCache = false);
    if (mounted) {
      final loc = AppLocalizations.of(context)!;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(loc.storage_cacheCleared)));
    }
  }

  Future<void> _deleteDirectoryContents(Directory dir) async {
    await for (var entity in dir.list(recursive: false, followLinks: false)) {
      try {
        if (entity is File) {
          await entity.delete();
        } else if (entity is Directory) {
          await entity.delete(recursive: true);
        }
      } catch (_) {}
    }
  }

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context)!;

    final int totalBytes = _cacheBytes + _chatBytes + _appDataBytes;

    return Scaffold(
      backgroundColor: context.bgSecondary,
      appBar: AppBar(
        backgroundColor: context.tileBackground,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: context.textPrimaryCol),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          // Re-use existing localization, fallback to hard-coded.
          loc.settings_storageManagement,
          style: TextStyle(
            color: context.textPrimaryCol,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildTotalSection(totalBytes, loc),
          const SizedBox(height: 16),
          _buildCard(
            title: loc.storage_cache,
            sizeText: _formatBytes(_cacheBytes),
            description: loc.storage_cacheDesc,
            actionLabel: loc.storage_cacheClear,
            actionColor: Colors.green,
            onAction: _clearingCache ? null : _clearCache,
          ),
          const SizedBox(height: 16),
          _buildCard(
            title: loc.storage_chathistory,
            sizeText: _formatBytes(_chatBytes),
            description: loc.storage_chathistoryDesc,
            actionLabel: loc.storage_chathistoryManage,
            actionColor: Colors.grey,
            onAction: () {
              // TODO: Implement chat history management page
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Chat history management not implemented yet.'),
                ),
              );
            },
          ),
          const SizedBox(height: 16),
          _buildCard(
            title: loc.storage_appdata,
            sizeText: _formatBytes(_appDataBytes),
            description: loc.storage_appdataDesc,
            // No action button
          ),
        ],
      ),
    );
  }

  Widget _buildTotalSection(int totalBytes, AppLocalizations loc) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          loc.storage_totalTitle,
          style: TextStyle(fontSize: 16, color: context.textSecondaryCol),
        ),
        const SizedBox(height: 8),
        Text(
          _formatBytes(totalBytes),
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: context.textPrimaryCol,
          ),
        ),
        // Placeholder for percentage or additional info
      ],
    );
  }

  Widget _buildCard({
    required String title,
    required String sizeText,
    required String description,
    String? actionLabel,
    Color? actionColor,
    VoidCallback? onAction,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: context.tileBackground,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: context.textPrimaryCol,
                  ),
                ),
              ),
              if (actionLabel != null)
                TextButton(
                  style: TextButton.styleFrom(
                    backgroundColor: actionColor ?? context.brandPrimary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 4,
                    ),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  onPressed: onAction,
                  child: Text(actionLabel),
                ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            sizeText,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: context.textPrimaryCol,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: TextStyle(fontSize: 14, color: context.textSecondaryCol),
          ),
        ],
      ),
    );
  }
}

/// Fallback localization getters for storage management strings.
/// These will be overridden automatically once `flutter gen-l10n` is run
/// after updating ARB files.
extension _StorageLocFallback on AppLocalizations {
  String get storage_totalTitle => 'aiTalk Data';
  String get storage_cache => 'Cache';
  String get storage_cacheDesc => 'Cache is used to temporarily store data.';
  String get storage_cacheClear => 'Clear';
  String get storage_cacheCleared => 'Cache cleared';
  String get storage_chathistory => 'Chat History';
  String get storage_chathistoryDesc =>
      'Chat History contains all conversations.';
  String get storage_chathistoryManage => 'Manage';
  String get storage_appdata => 'App Data';
  String get storage_appdataDesc =>
      'App Data refers to files required by the app.';
}
