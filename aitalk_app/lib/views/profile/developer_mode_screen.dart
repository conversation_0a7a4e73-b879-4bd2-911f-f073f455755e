import 'package:flutter/material.dart';
import '../../core/constants/colors.dart';
import '../../l10n/app_localizations.dart';
import '../device/bluetooth_ota_screen.dart';

class DeveloperModeScreen extends StatelessWidget {
  const DeveloperModeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: context.bgSecondary,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: context.bgPrimary,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new_rounded),
          onPressed: () => Navigator.pop(context),
          color: context.textPrimaryCol,
        ),
        title: Text(
          loc.developer_title,
          style: TextStyle(
            color: context.textPrimaryCol,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: ListView(
        children: [
          const SizedBox(height: 16),
          // 蓝牙OTA
          _buildListItem(
            context: context,
            icon: Icons.system_update,
            iconColor: context.brandPrimary,
            title: loc.developer_bluetoothOta,
            subtitle: loc.developer_bluetoothOta_desc,
            onTap: () => Navigator.of(context).push(
              MaterialPageRoute(builder: (_) => const BluetoothOtaScreen()),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListItem({
    required BuildContext context,
    required IconData icon,
    required Color iconColor,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
  }) {
    return Container(
      color: context.bgPrimary,
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: iconColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: iconColor, size: 24),
        ),
        title: Text(
          title,
          style: TextStyle(
            color: context.textPrimaryCol,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: subtitle != null
            ? Text(
                subtitle,
                style: TextStyle(color: context.textSecondaryCol, fontSize: 14),
              )
            : null,
        trailing: Icon(
          Icons.chevron_right,
          color: context.textTertiaryCol,
          size: 20,
        ),
        onTap: onTap,
      ),
    );
  }
}
