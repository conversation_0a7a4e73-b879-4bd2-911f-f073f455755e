import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../core/constants/colors.dart';
import '../../l10n/app_localizations.dart';
import '../../main.dart';
import '../../core/user/user_profile.dart';
import '../../core/services/settings_service.dart';

class TextSizeSettingsScreen extends StatefulWidget {
  const TextSizeSettingsScreen({super.key});

  @override
  State<TextSizeSettingsScreen> createState() => _TextSizeSettingsScreenState();
}

class _TextSizeSettingsScreenState extends State<TextSizeSettingsScreen> {
  late double _originScale;
  late double _currentScale;

  static const double _minScale = 0.8;
  static const double _maxScale = 1.4;

  @override
  void initState() {
    super.initState();
    _originScale = textScaleNotifier.value;
    _currentScale = _originScale;
  }

  void _updateScale(double value) {
    setState(() => _currentScale = value);
    textScaleNotifier.value = value; // 实时预览
    SettingsService.saveTextScale(value);
  }

  void _cancel() {
    textScaleNotifier.value = _originScale; // 恢复原值
    Navigator.of(context).pop();
  }

  void _done() {
    SettingsService.saveTextScale(_currentScale);
    Navigator.of(context).pop(); // 保持当前值
  }

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context)!;
    return Scaffold(
      backgroundColor: context.bgSecondary,
      appBar: AppBar(
        backgroundColor: context.tileBackground,
        elevation: 0,
        leadingWidth: 88,
        leading: TextButton(
          onPressed: _cancel,
          style: TextButton.styleFrom(
            minimumSize: Size.zero,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: FittedBox(
            fit: BoxFit.scaleDown,
            alignment: Alignment.centerLeft,
            child: Text(
              loc?.createGroup_cancel ?? 'Cancel',
              style: TextStyle(color: context.brandPrimary, fontSize: 16),
            ),
          ),
        ),
        title: Text(
          loc.settings_textSize,
          style: TextStyle(
            color: context.textPrimaryCol,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: _done,
            child: Text(
              loc?.createGroup_done ?? 'Done',
              style: TextStyle(color: context.brandPrimary, fontSize: 16),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(child: Center(child: _DemoChat())),
          Padding(
            padding: const EdgeInsets.only(left: 24, right: 24, bottom: 40),
            child: Column(
              children: [
                Slider(
                  min: _minScale,
                  max: _maxScale,
                  divisions: 6,
                  value: _currentScale.clamp(_minScale, _maxScale),
                  onChanged: _updateScale,
                  activeColor: context.brandPrimary,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: const [
                    Text('A', style: TextStyle(fontSize: 12)),
                    Text('A', style: TextStyle(fontSize: 24)),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Simple preview of chat bubbles to show text size effect.
class _DemoChat extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context)!;
    return ValueListenableBuilder<String?>(
      valueListenable: UserProfile.instance.nicknameNotifier,
      builder: (context, nickname, _) {
        final String selfName = nickname ?? 'Me';
        const String otherName = '匿名用户';

        String initial(String n) => n.isNotEmpty ? n.characters.first : '?';

        final selfAvatar = CircleAvatar(
          radius: 16,
          backgroundColor: context.brandPrimary,
          child: Text(
            initial(selfName),
            style: const TextStyle(color: Colors.white, fontSize: 14),
          ),
        );

        final otherAvatar = CircleAvatar(
          radius: 16,
          backgroundColor: Colors.grey,
          child: Text(
            initial(otherName),
            style: const TextStyle(color: Colors.white, fontSize: 14),
          ),
        );

        final now = DateTime.now();
        final selfTime = DateFormat('HH:mm').format(now);
        final otherTime = DateFormat(
          'HH:mm',
        ).format(now.add(const Duration(minutes: 1)));

        return SizedBox(
          width: 260,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _ChatRow(
                isMine: true,
                avatarWidget: selfAvatar,
                message: loc.textSize_demoMessage(selfName),
                time: selfTime,
                bubbleColor: context.brandPrimary,
                textColor: Colors.white,
              ),
              const SizedBox(height: 32),
              _ChatRow(
                isMine: false,
                avatarWidget: otherAvatar,
                message: loc.textSize_demoMessage(otherName),
                time: otherTime,
                bubbleColor: Colors.green.shade600,
                textColor: Colors.white,
              ),
            ],
          ),
        );
      },
    );
  }
}

class _ChatRow extends StatelessWidget {
  final bool isMine;
  final Widget avatarWidget;
  final String message;
  final String time;
  final Color bubbleColor;
  final Color textColor;
  const _ChatRow({
    required this.isMine,
    required this.avatarWidget,
    required this.message,
    required this.time,
    required this.bubbleColor,
    required this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    final bubble = Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: bubbleColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(message, style: TextStyle(color: textColor, fontSize: 14)),
    );
    final timeWidget = Text(
      time,
      style: TextStyle(color: context.textSecondaryCol, fontSize: 10),
    );

    if (isMine) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [bubble, const SizedBox(width: 8), avatarWidget],
          ),
          timeWidget,
        ],
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(children: [avatarWidget, const SizedBox(width: 8), bubble]),
          timeWidget,
        ],
      );
    }
  }
}

/// Fallback localization extension in case l10n not regenerated yet.
extension _TextSizeLocFallback on AppLocalizations {
  String get settings_textSize => 'Text Size';
  String textSize_demoMessage(String name) => "I'm $name";
}
