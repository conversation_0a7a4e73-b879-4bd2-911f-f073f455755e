import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../../core/constants/colors.dart';
import '../../core/services/notification_service.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  bool _systemNotify = true;
  bool _showContent = true;
  bool _voiceCallNotify = true;

  @override
  void initState() {
    super.initState();
    // 读取当前通知开关状态
    _systemNotify = NotificationService.instance.enabled.value;
    // 监听全局变化，保持 UI 同步
    NotificationService.instance.enabled.addListener(_onEnabledChanged);
  }

  void _onEnabledChanged() {
    if (mounted) {
      setState(() => _systemNotify = NotificationService.instance.enabled.value);
    }
  }

  @override
  void dispose() {
    NotificationService.instance.enabled.removeListener(_onEnabledChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: context.bgSecondary,
      appBar: AppBar(
        backgroundColor: context.tileBackground,
        elevation: 0,
        iconTheme: IconThemeData(color: context.textPrimaryCol),
        title: Text(
          loc.settings_notifications,
          style: TextStyle(color: context.textPrimaryCol, fontSize: 16, fontWeight: FontWeight.w500),
        ),
        centerTitle: true,
      ),
      body: ListView(
        children: [
          const SizedBox(height: 12),
          _buildSwitchTile(
            title: loc.notification_system,
            description: loc.notification_system_desc,
            value: _systemNotify,
            onChanged: (v) async {
              setState(() => _systemNotify = v);
              await NotificationService.instance.setEnabled(v);
            },
          ),
          _buildSwitchTile(
            title: loc.notification_showContent,
            description: loc.notification_showContent_desc,
            value: _showContent,
            onChanged: (v) => setState(() => _showContent = v),
          ),
          _buildSwitchTile(
            title: loc.notification_voiceCall,
            description: loc.notification_voiceCall_desc,
            value: _voiceCallNotify,
            onChanged: (v) => setState(() => _voiceCallNotify = v),
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String description,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      color: context.tileBackground,
      child: SwitchListTile(
        title: Text(title, style: TextStyle(color: context.textPrimaryCol, fontSize: 16)),
        subtitle: Text(description, style: TextStyle(color: context.textSecondaryCol, fontSize: 12)),
        value: value,
        activeColor: context.brandPrimary,
        onChanged: onChanged,
      ),
    );
  }
} 