import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../../core/constants/colors.dart';
import '../../main.dart';
import '../../core/services/settings_service.dart';

class LanguageSettingsScreen extends StatefulWidget {
  const LanguageSettingsScreen({super.key});

  @override
  State<LanguageSettingsScreen> createState() => _LanguageSettingsScreenState();
}

class _LanguageSettingsScreenState extends State<LanguageSettingsScreen> {
  // 当前选中的语言
  Locale? _selectedLocale;

  @override
  void initState() {
    super.initState();
    _selectedLocale = localeNotifier.value; // 初始值与全局保持一致
  }

  void _onSelect(Locale? locale) {
    setState(() {
      _selectedLocale = locale;
    });
  }

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context)!;

    // 使用国际化字符串

    return Scaffold(
      backgroundColor: context.bgSecondary,
      appBar: AppBar(
        backgroundColor: context.tileBackground,
        elevation: 0,
        leadingWidth: 88, // 增加区域宽度，避免文字换行
        leading: TextButton(
          onPressed: () => Navigator.of(context).pop(),
          style: TextButton.styleFrom(
            minimumSize: Size.zero, // 取消默认最小尺寸
            padding: const EdgeInsets.symmetric(horizontal: 16),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: FittedBox(
            fit: BoxFit.scaleDown,
            alignment: Alignment.centerLeft,
            child: Text(
              loc.createGroup_cancel, // 重用已有的“取消”/Cancel 文案
              style: TextStyle(color: context.brandPrimary, fontSize: 16),
            ),
          ),
        ),
        title: Text(
          loc.profile_language,
          style: TextStyle(
            color: context.textPrimaryCol,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: () {
              // 保存选择并返回
              localeNotifier.value = _selectedLocale;
              SettingsService.saveLocale(_selectedLocale);
              Navigator.of(context).pop();
            },
            child: Text(
              loc.createGroup_done, // 重用已有的“完成”文案
              style: TextStyle(color: context.brandPrimary, fontSize: 16),
            ),
          ),
        ],
      ),
      body: ListView(
        children: [
          const SizedBox(height: 12),
          _buildOption(title: loc.profile_languageSystem, locale: null),
          _buildOption(
            title: loc.profile_languageChinese,
            locale: const Locale('zh'),
          ),
          _buildOption(
            title: loc.profile_languageEnglish,
            locale: const Locale('en'),
          ),
        ],
      ),
    );
  }

  // 单个选项
  Widget _buildOption({required String title, required Locale? locale}) {
    final bool selected;
    if (locale == null) {
      selected = _selectedLocale == null;
    } else {
      selected = _selectedLocale?.languageCode == locale.languageCode;
    }

    return Container(
      color: context.tileBackground,
      child: ListTile(
        title: Text(
          title,
          style: TextStyle(fontSize: 16, color: context.textPrimaryCol),
        ),
        trailing: selected
            ? Icon(Icons.check, color: context.brandPrimary)
            : null,
        onTap: () => _onSelect(locale),
      ),
    );
  }
}
