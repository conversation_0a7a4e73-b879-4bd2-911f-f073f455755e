import 'package:flutter/material.dart';
import 'package:characters/characters.dart';
import '../../l10n/app_localizations.dart';
import '../../core/constants/colors.dart';
import '../../core/user/user_profile.dart';
import 'qr_code_screen.dart';

const double _trailingWidth = 120; // 统一右侧区域宽度

class ProfileDetailScreen extends StatelessWidget {
  const ProfileDetailScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: AppBar(title: Text(loc.profile_myInfo), centerTitle: true),
      body: ListView(
        children: [
          const SizedBox(height: 24),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            decoration: BoxDecoration(
              color: context.bgPrimary,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                _buildAvatarRow(context, loc),
                _buildDivider(context),
                _buildNicknameRow(context, loc),
                _buildDivider(context),
                _buildQrRow(context, loc),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Divider(
      height: 1,
      thickness: 1,
      indent: 16,
      endIndent: 16,
      color: context.bgSecondary.withValues(alpha: 0.5),
    );
  }

  Widget _buildAvatarRow(BuildContext context, AppLocalizations loc) {
    return ValueListenableBuilder<String?>(
      valueListenable: UserProfile.instance.nicknameNotifier,
      builder: (context, nickname, _) {
        // 取昵称首字符，若为空则使用问号占位
        final String firstChar = (nickname?.trim().isNotEmpty ?? false)
            ? nickname!.trim().characters.first
            : '?';

        return ListTile(
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
          title: Text(loc.profile_avatar),
          trailing: SizedBox(
            width: _trailingWidth,
            child: Align(
              alignment: Alignment.centerRight,
              child: CircleAvatar(
                radius: 40, // 更大尺寸
                backgroundColor: context.brandPrimary,
                child: Text(
                  firstChar,
                  style: const TextStyle(color: Colors.white, fontSize: 24),
                ),
              ),
            ),
          ),
          onTap: () {},
        );
      },
    );
  }

  Widget _buildNicknameRow(BuildContext context, AppLocalizations loc) {
    return ValueListenableBuilder<String?>(
      valueListenable: UserProfile.instance.nicknameNotifier,
      builder: (context, nickname, _) {
        final displayName = (nickname?.trim() ?? '');
        return ListTile(
          title: Text(loc.profile_nickname),
          trailing: SizedBox(
            width: _trailingWidth,
            child: Align(
              alignment: Alignment.centerRight,
              child: Text(
                displayName,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.textSecondaryCol,
                ),
              ),
            ),
          ),
          onTap: () => _showEditNicknameDialog(context, loc, nickname),
        );
      },
    );
  }

  void _showEditNicknameDialog(
    BuildContext context,
    AppLocalizations loc,
    String? current,
  ) {
    final TextEditingController controller = TextEditingController(
      text: current ?? '',
    );
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(loc.profile_nickname),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(hintText: ''),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(ctx),
            child: Text(loc.channelPicker_cancel),
          ),
          TextButton(
            onPressed: () {
              final newName = controller.text.trim();
              // 允许将昵称设置为空字符串，以便清除昵称
              UserProfile.instance.setNickname(newName);
              Navigator.pop(ctx);
            },
            child: Text(loc.createGroup_done),
          ),
        ],
      ),
    );
  }

  Widget _buildQrRow(BuildContext context, AppLocalizations loc) {
    return ListTile(
      title: Text(loc.profile_qrcode),
      trailing: SizedBox(
        width: _trailingWidth,
        child: const Align(
          alignment: Alignment.centerRight,
          child: Icon(Icons.qr_code, size: 28),
        ),
      ),
      onTap: () {
        Navigator.of(
          context,
        ).push(MaterialPageRoute(builder: (_) => const QrCodeScreen()));
      },
    );
  }
}
