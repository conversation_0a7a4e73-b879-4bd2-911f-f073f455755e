import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../../core/constants/colors.dart';
import '../../core/services/settings_service.dart';
import '../../core/protocol/di_data_dispatcher.dart';

class VoicePlaybackSettingsScreen extends StatefulWidget {
  const VoicePlaybackSettingsScreen({super.key});

  @override
  State<VoicePlaybackSettingsScreen> createState() =>
      _VoicePlaybackSettingsScreenState();
}

class _VoicePlaybackSettingsScreenState
    extends State<VoicePlaybackSettingsScreen> {
  bool _autoPlay = false;
  bool _backgroundMode = false;

  @override
  void initState() {
    super.initState();
    // 加载持久化设置
    SettingsService.loadAutoPlayVoice().then((value) {
      if (value != null) {
        setState(() => _autoPlay = value);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: context.bgSecondary,
      appBar: AppBar(
        backgroundColor: context.tileBackground,
        elevation: 0,
        iconTheme: IconThemeData(color: context.textPrimaryCol),
        title: Text(
          loc.settings_voicePlayback,
          style: TextStyle(
            color: context.textPrimaryCol,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
      ),
      body: ListView(
        children: [
          const SizedBox(height: 12),
          _buildSwitchTile(
            title: loc.voicePlayback_autoPlay,
            description: loc.voicePlayback_autoPlay_desc,
            value: _autoPlay,
            onChanged: (v) {
              setState(() => _autoPlay = v);
              // 保存设置并通知核心
              SettingsService.saveAutoPlayVoice(v);
              DiDataDispatcher.instance.setAutoPlayVoice(v);
            },
          ),
          _buildSwitchTile(
            title: loc.voicePlayback_backgroundMode,
            description: loc.voicePlayback_backgroundMode_desc,
            value: _backgroundMode,
            onChanged: (v) => setState(() => _backgroundMode = v),
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String description,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      color: context.tileBackground,
      child: SwitchListTile(
        title: Text(
          title,
          style: TextStyle(color: context.textPrimaryCol, fontSize: 16),
        ),
        subtitle: Text(
          description,
          style: TextStyle(color: context.textSecondaryCol, fontSize: 12),
        ),
        value: value,
        activeColor: context.brandPrimary,
        onChanged: onChanged,
      ),
    );
  }
}
