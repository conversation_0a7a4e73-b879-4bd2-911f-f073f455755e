import 'package:flutter/material.dart';
import '../../core/constants/colors.dart';
import '../../l10n/app_localizations.dart';
import 'device_info_screen.dart';
import '../../core/bluetooth/bluetooth_manager.dart';
import '../../core/device/device_manager.dart';
import '../../core/services/database_service.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';

class DeviceManagementScreen extends StatefulWidget {
  const DeviceManagementScreen({super.key});

  @override
  State<DeviceManagementScreen> createState() => _DeviceManagementScreenState();
}

class _DeviceManagementScreenState extends State<DeviceManagementScreen> {
  @override
  void initState() {
    super.initState();
    // 确保 DeviceManager 已启动，以便获取 DeviceID 等信息
    DeviceManager.instance.start();
  }

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: context.bgSecondary,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: context.bgPrimary,
        leading: Icon<PERSON>utton(
          icon: const Icon(Icons.arrow_back_ios_new_rounded),
          onPressed: () => Navigator.pop(context),
          color: context.textPrimaryCol,
        ),
        title: Text(
          loc.profile_deviceManagement,
          style: TextStyle(
            color: context.textPrimaryCol,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: ValueListenableBuilder<int>(
        valueListenable: DatabaseService.deviceListChanged,
        builder: (context, _, __) {
          return FutureBuilder<List<Map<String, dynamic>>>(
            future: DatabaseService.instance.fetchBluetoothDevices(),
            builder: (context, snapshot) {
              if (!snapshot.hasData) {
                return Center(child: CircularProgressIndicator());
              }

              final records = snapshot.data!;
              if (records.isEmpty) {
                return Center(
                  child: Text(
                    loc.deviceInfo_deviceId_loading,
                    style: TextStyle(color: context.textSecondaryCol),
                  ),
                );
              }

              // 使用ValueListenableBuilder监听deviceIdNotifier的变化
              return ValueListenableBuilder<String?>(
                valueListenable: DeviceManager.instance.deviceIdNotifier,
                builder: (context, deviceIdStr, _) {
                  final current = BluetoothManager.currentDevice.value;

                  // 添加调试打印
                  debugPrint('当前连接的deviceIdStr: $deviceIdStr');
                  for (final record in records) {
                    debugPrint(
                      '数据库记录: ${record['device_id']} | ${record['name']}',
                    );
                  }

                  final items = records.map((row) {
                    final String devId = row['device_id'] as String;
                    // 修改判断逻辑：考虑deviceIdStr为null的情况
                    bool isConn = false;
                    if (deviceIdStr != null) {
                      isConn = deviceIdStr == devId;
                    } else if (current != null) {
                      // 如果deviceId还没解析出来，但确实有设备连接了，让UI显示出来
                      // 这里是个临时方案，因为设备管理界面进入时可能ID还没解析好
                      isConn = current.platformName == row['name'];
                    }

                    return _DeviceItem(
                      name: row['name'] as String,
                      id: devId,
                      isConnected: isConn,
                      device: isConn ? current : null,
                    );
                  }).toList();

                  return ListView.separated(
                    padding: const EdgeInsets.symmetric(
                      vertical: 16,
                      horizontal: 16,
                    ),
                    itemCount: items.length,
                    separatorBuilder: (_, __) => const SizedBox(height: 12),
                    itemBuilder: (context, index) =>
                        _DeviceCard(device: items[index]),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}

class _DeviceItem {
  final String name;
  final String id;
  final bool isConnected;
  final BluetoothDevice? device;

  const _DeviceItem({
    required this.name,
    required this.id,
    this.isConnected = false,
    this.device,
  });
}

class _DeviceCard extends StatelessWidget {
  const _DeviceCard({required this.device});

  final _DeviceItem device;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: context.bgPrimary,
      elevation: 2,
      borderRadius: BorderRadius.circular(8),
      shadowColor: AppColors.shadow,
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: () {
          if (device.device != null) {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (_) => DeviceInfoScreen(
                  deviceModel: 'ATK-100CN',
                  deviceName: device.device!.platformName,
                  deviceId: device.id,
                  device: device.device!,
                  hardwareVersion: AppLocalizations.of(
                    context,
                  )!.deviceInfo_deviceId_loading,
                  bleVersion: AppLocalizations.of(
                    context,
                  )!.deviceInfo_deviceId_loading,
                  turMassFirmwareVersion: AppLocalizations.of(
                    context,
                  )!.deviceInfo_deviceId_loading,
                ),
              ),
            );
          }
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      device.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    width: 10,
                    height: 10,
                    decoration: BoxDecoration(
                      color: device.isConnected
                          ? AppColors.success
                          : context.textSecondaryCol,
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                '设备ID：${device.id}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: context.textSecondaryCol,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
