import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import '../../core/constants/colors.dart';
import '../../l10n/app_localizations.dart';
import '../../core/bluetooth/bluetooth_manager.dart';
import '../../core/device/device_manager.dart';
import '../../core/ota/ota.dart';

class BluetoothOtaScreen extends StatefulWidget {
  const BluetoothOtaScreen({super.key});

  @override
  State<BluetoothOtaScreen> createState() => _BluetoothOtaScreenState();
}

class _BluetoothOtaScreenState extends State<BluetoothOtaScreen> {
  File? _selectedFile;
  String? _fileName;
  String? _currentVersion;
  String? _targetVersion;
  final OtaSettingsModel _otaSettings = const OtaSettingsModel();

  // OTA管理器
  final TelinkOtaManager _otaManager = TelinkOtaManager.instance;

  @override
  void initState() {
    super.initState();
    _loadCurrentVersion();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _loadCurrentVersion() {
    final status = DeviceManager.instance.statusNotifier.value;
    setState(() {
      _currentVersion = status.firmwareVersion ?? '未知';
    });
  }

  Future<void> _selectFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['bin'],
      );

      if (result != null && result.files.single.path != null) {
        setState(() {
          _selectedFile = File(result.files.single.path!);
          _fileName = result.files.single.name;
          _targetVersion = _extractVersionFromFileName(_fileName!);
        });
      }
    } catch (e) {
      if (mounted) {
        _showErrorMessage(AppLocalizations.of(context)!.bluetoothOta_fileError);
      }
    }
  }

  String _extractVersionFromFileName(String fileName) {
    // 尝试从文件名中提取版本号，这里是一个简单的实现
    // 可以根据实际的文件命名规则进行调整
    final regex = RegExp(r'v?(\d+\.\d+\.\d+)');
    final match = regex.firstMatch(fileName);
    return match?.group(1) ?? '未知';
  }

  Future<void> _startUpgrade() async {
    final loc = AppLocalizations.of(context)!;

    if (_selectedFile == null) {
      _showErrorMessage(loc.bluetoothOta_noFile);
      return;
    }

    final device = BluetoothManager.currentDevice.value;
    if (device == null) {
      _showErrorMessage(loc.bluetoothOta_noDevice);
      return;
    }

    try {
      // 创建OTA设置
      final settings = _otaSettings.copyWith(filePath: _selectedFile!.path);

      // 开始OTA升级
      final result = await _otaManager.startOta(device, settings);

      if (result.isSuccess) {
        _showSuccessMessage(loc.bluetoothOta_success);
        // 更新设备版本信息
        _loadCurrentVersion();
      } else {
        _showErrorMessage(
          '${loc.bluetoothOta_failed}: ${result.shortDescription}',
        );
      }
    } catch (e) {
      _showErrorMessage('${loc.bluetoothOta_failed}: $e');
    }
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: context.bgSecondary,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: context.bgPrimary,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new_rounded),
          onPressed: () => Navigator.pop(context),
          color: context.textPrimaryCol,
        ),
        title: Text(
          loc.bluetoothOta_title,
          style: TextStyle(
            color: context.textPrimaryCol,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: ValueListenableBuilder<OtaProgress>(
        valueListenable: _otaManager.progressNotifier,
        builder: (context, progress, child) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 设备信息卡片
                _buildDeviceInfoCard(loc),
                const SizedBox(height: 16),

                // 文件选择卡片
                _buildFileSelectionCard(loc, progress),
                const SizedBox(height: 16),

                // 升级进度
                if (progress.status.isInProgress)
                  _buildProgressCard(loc, progress),

                const Spacer(),

                // 开始升级按钮
                _buildUpgradeButton(loc, progress),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildDeviceInfoCard(AppLocalizations loc) {
    return Card(
      color: context.bgPrimary,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              loc.bluetoothOta_deviceInfo,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: context.textPrimaryCol,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              loc.bluetoothOta_currentVersion,
              _currentVersion ?? '未知',
            ),
            if (_targetVersion != null) ...[
              const SizedBox(height: 8),
              _buildInfoRow(loc.bluetoothOta_targetVersion, _targetVersion!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFileSelectionCard(AppLocalizations loc, OtaProgress progress) {
    return Card(
      color: context.bgPrimary,
      child: InkWell(
        onTap: progress.status.isInProgress ? null : _selectFile,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Icon(Icons.folder_open, color: context.brandPrimary, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      loc.bluetoothOta_selectFile,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: context.textPrimaryCol,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _fileName ?? loc.bluetoothOta_selectFile_desc,
                      style: TextStyle(
                        fontSize: 14,
                        color: _fileName != null
                            ? context.brandPrimary
                            : context.textSecondaryCol,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.chevron_right,
                color: context.textTertiaryCol,
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressCard(AppLocalizations loc, OtaProgress progress) {
    return Card(
      color: context.bgPrimary,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              progress.stepDescription,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: context.textPrimaryCol,
              ),
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: progress.progress,
              backgroundColor: context.textTertiaryCol.withValues(alpha: 0.3),
              valueColor: AlwaysStoppedAnimation<Color>(context.brandPrimary),
            ),
            const SizedBox(height: 8),
            Text(
              progress.progressPercentage,
              style: TextStyle(fontSize: 14, color: context.textSecondaryCol),
            ),
            if (progress.transferInfoString.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                progress.transferInfoString,
                style: TextStyle(fontSize: 12, color: context.textTertiaryCol),
              ),
            ],
            if (progress.transferSpeed > 0) ...[
              const SizedBox(height: 4),
              Text(
                '${progress.transferSpeedString} - ${progress.estimatedTimeRemainingString}',
                style: TextStyle(fontSize: 12, color: context.textTertiaryCol),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildUpgradeButton(AppLocalizations loc, OtaProgress progress) {
    final canUpgrade =
        _selectedFile != null &&
        BluetoothManager.currentDevice.value != null &&
        !progress.status.isInProgress;

    return SizedBox(
      width: double.infinity,
      height: 48,
      child: ElevatedButton(
        onPressed: canUpgrade ? _startUpgrade : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: context.brandPrimary,
          disabledBackgroundColor: context.textTertiaryCol.withValues(
            alpha: 0.3,
          ),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child: Text(
          progress.status.isInProgress
              ? loc.bluetoothOta_upgrading
              : loc.bluetoothOta_startUpgrade,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 14, color: context.textSecondaryCol),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: context.textPrimaryCol,
          ),
        ),
      ],
    );
  }
}
