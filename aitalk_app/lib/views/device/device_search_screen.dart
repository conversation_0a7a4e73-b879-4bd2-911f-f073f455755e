import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import '../../core/bluetooth/bluetooth_manager.dart';
import '../../core/constants/colors.dart';
import '../../core/bluetooth/bluetooth_uuids.dart';

class DeviceSearchScreen extends StatefulWidget {
  const DeviceSearchScreen({super.key});

  @override
  State<DeviceSearchScreen> createState() => _DeviceSearchScreenState();
}

class _DeviceSearchScreenState extends State<DeviceSearchScreen> {
  bool _isScanning = false;
  List<ScanResult> _scanResults = [];
  StreamSubscription<List<ScanResult>>? _scanResultsSubscription;
  StreamSubscription<bool>? _isScanningSubscription;

  @override
  void initState() {
    super.initState();
    // 监听扫描状态
    _isScanningSubscription = FlutterBluePlus.isScanning.listen((isScanning) {
      setState(() {
        _isScanning = isScanning;
      });
    });
    // 开始扫描
    _startScan();
  }

  @override
  void dispose() {
    _scanResultsSubscription?.cancel();
    _isScanningSubscription?.cancel();
    // 停止扫描
    FlutterBluePlus.stopScan();
    super.dispose();
  }

  // 开始扫描
  Future<void> _startScan() async {
    setState(() {
      _scanResults = [];
    });

    try {
      // 检查蓝牙是否可用和开启
      if (!(await FlutterBluePlus.isAvailable)) {
        _showErrorMessage('蓝牙不可用');
        return;
      }

      // 等待蓝牙真正处于打开状态 (iOS 初始通常为 unknown)
      if (await FlutterBluePlus.adapterStateNow != BluetoothAdapterState.on) {
        try {
          await FlutterBluePlus.adapterState
              .firstWhere((s) => s == BluetoothAdapterState.on)
              .timeout(const Duration(seconds: 4));
        } catch (_) {
          _showErrorMessage('请开启蓝牙');
          return;
        }
      }

      // 设置扫描结果监听
      _scanResultsSubscription?.cancel();
      _scanResultsSubscription = BluetoothManager.scanResults.listen((results) {
        setState(() {
          // 按 RSSI 从高到低排序，信号强的排前面
          _scanResults = List<ScanResult>.from(results)
            ..sort((a, b) => b.rssi.compareTo(a.rssi));
        });
      }, onError: (e) {
        _showErrorMessage('扫描出错: $e');
      });

      // 开始扫描，5秒后自动停止
      await BluetoothManager.startScan(
        timeout: const Duration(seconds: 5),
        withServices: [SERVICE_2022_UUID],
      );
    } catch (e) {
      _showErrorMessage('扫描异常: $e');
    }
  }

  // 显示错误消息
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  // 根据信号强度获取颜色
  Color _getRssiColor(int rssi) {
    if (rssi >= -50) {
      return Colors.green; // 强信号，绿色
    } else if (rssi >= -65) {
      return Colors.green; // 较好信号，绿色
    } else if (rssi >= -80) {
      return Colors.orange; // 一般信号，橙色
    } else if (rssi >= -90) {
      return Colors.orange; // 较弱信号，橙色
    } else {
      return Colors.red; // 弱信号，红色
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.bgSecondary,
      appBar: AppBar(
        backgroundColor: context.bgSecondary,
        elevation: 0,
        leading: TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            '取消',
            style: TextStyle(
              color: context.brandPrimary,
              fontSize: 16,
            ),
          ),
        ),
        title: Text(
          '可配对设备列表',
          style: TextStyle(
            color: context.textPrimaryCol,
            fontSize: 18,
            fontWeight: FontWeight.normal,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: _isScanning ? context.textTertiaryCol : context.brandPrimary,
            ),
            onPressed: _isScanning ? null : _startScan,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            if (_isScanning)
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 8.0),
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              ),
            Expanded(
              child: Card(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
                child: _scanResults.isEmpty
                    ? const Center(
                        child: Text(
                          '未找到设备',
                          style: TextStyle(color: Colors.grey),
                        ),
                      )
                    : ListView.separated(
                        padding: EdgeInsets.zero,
                        itemCount: _scanResults.length,
                        separatorBuilder: (context, index) => Divider(
                          height: 1,
                          indent: 16,
                          endIndent: 16,
                          color: context.bgSecondary.withOpacity(0.5),
                        ),
                        itemBuilder: (context, index) {
                          final result = _scanResults[index];
                          final device = result.device;
                          final rssi = result.rssi;
                          final rssiColor = _getRssiColor(rssi);
                          
                          return ListTile(
                            title: Text(
                              device.platformName.isNotEmpty
                                  ? device.platformName
                                  : device.remoteId.str,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: context.textPrimaryCol,
                              ),
                            ),
                            subtitle: Text(
                              'RSSI: $rssi dBm',
                              style: TextStyle(
                                fontSize: 14,
                                color: rssiColor,
                              ),
                            ),
                            onTap: () {
                              // 返回选中的设备
                              Navigator.pop(context, {
                                'device': device,
                                'name': device.platformName,
                                'rssi': rssi,
                              });
                            },
                          );
                        },
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 