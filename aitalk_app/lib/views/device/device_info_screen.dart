import 'package:flutter/material.dart';
import '../../core/constants/colors.dart';
import '../../l10n/app_localizations.dart';
import '../../core/protocol/at_response_handler.dart';
import '../../core/device/device_manager.dart';
import 'dart:async';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';

/// 设备信息界面。
class DeviceInfoScreen extends StatefulWidget {
  const DeviceInfoScreen({
    super.key,
    required this.deviceModel,
    required this.deviceName,
    required this.deviceId,
    required this.hardwareVersion,
    required this.bleVersion,
    required this.turMassFirmwareVersion,
    this.turMassActivated = false,
    required this.device,
  });

  final String deviceModel;
  final String deviceName;
  final String deviceId;
  final BluetoothDevice device;
  final String hardwareVersion;
  final String bleVersion;
  final String turMassFirmwareVersion;
  final bool turMassActivated;

  @override
  State<DeviceInfoScreen> createState() => _DeviceInfoScreenState();
}

class _DeviceInfoScreenState extends State<DeviceInfoScreen> {
  late bool _isTurMassActivated;
  late String _deviceId;
  int? _battery;
  StreamSubscription? _respSub;

  @override
  void initState() {
    super.initState();
    _isTurMassActivated = widget.turMassActivated;

    _deviceId = widget.deviceId;

    // Query battery when entering screen
    DeviceManager.instance.fetchBatteryLevel().then((value) {
      if (mounted && value != null) {
        setState(() => _battery = value);
      }
    });

    // 监听 DeviceID 响应
    _respSub = AtResponseHandler.instance.responses.listen((r) {
      if (r.type == AtResponseType.deviceId) {
        final idVal = r.payload?['deviceId'];
        if (idVal != null && mounted) {
          setState(() {
            final loc = AppLocalizations.of(context)!;
            _deviceId = idVal == 0
                ? loc.deviceInfo_deviceId_failed
                : idVal.toString();
          });
        }
      }
    });
  }

  @override
  void dispose() {
    _respSub?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: context.bgSecondary,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: context.bgPrimary,
        leadingWidth: 88, // 增加宽度，避免中英文换行
        leading: TextButton(
          onPressed: () => Navigator.pop(context),
          style: TextButton.styleFrom(
            minimumSize: Size.zero,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: FittedBox(
            fit: BoxFit.scaleDown,
            alignment: Alignment.centerLeft,
            child: Text(
              loc.channelPicker_cancel,
              style: TextStyle(color: context.brandPrimary, fontSize: 16),
            ),
          ),
        ),
        title: Text(
          loc.deviceInfo_title,
          style: TextStyle(
            color: context.textPrimaryCol,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: ListView(
        children: [
          _buildSectionHeader(loc.deviceInfo_deviceModel),
          _buildInfoRow('', widget.deviceModel),
          _buildSectionHeader(loc.deviceInfo_deviceInfo),
          ValueListenableBuilder<String?>(
            valueListenable: DeviceManager.instance.deviceNameNotifier,
            builder: (context, name, _) {
              return _buildInfoRow(
                loc.deviceInfo_deviceName,
                name ?? widget.deviceName,
              );
            },
          ),
          _buildInfoRow(loc.deviceInfo_deviceId, _deviceId),
          _buildInfoRow(
            loc.deviceInfo_deviceBattery,
            _battery != null ? '$_battery%' : loc.deviceInfo_deviceId_loading,
          ),
          _buildSectionHeader(loc.deviceInfo_deviceVersion),
          _buildInfoRow(loc.deviceInfo_hwVersion, widget.hardwareVersion),
          _buildInfoRow(loc.deviceInfo_bleVersion, widget.bleVersion),
          _buildInfoRow(
            loc.deviceInfo_turMassFirmwareVersion,
            widget.turMassFirmwareVersion,
          ),
          _buildSectionHeader(loc.deviceInfo_deviceOperation),
          _buildActionRow(
            loc.deviceInfo_disconnect,
            () => Navigator.pop(context, 'disconnect'),
          ),
          _buildActionRow(
            loc.deviceInfo_unpair,
            () => Navigator.pop(context, 'unpair'),
          ),
          Container(
            color: context.tileBackground,
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  loc.deviceInfo_activateTurMass,
                  style: TextStyle(color: context.textPrimaryCol, fontSize: 16),
                ),
                Switch(
                  value: _isTurMassActivated,
                  activeColor: context.brandPrimary,
                  onChanged: (v) {
                    setState(() => _isTurMassActivated = v);
                    // TODO: send command to device.
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Container(
      width: double.infinity,
      color: context.bgSecondary,
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Text(
        title,
        style: TextStyle(
          color: context.textSecondaryCol,
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Container(
      color: context.tileBackground,
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (label.isNotEmpty)
            Text(
              label,
              style: TextStyle(color: context.textPrimaryCol, fontSize: 16),
            ),
          Flexible(
            child: Text(
              value,
              style: TextStyle(color: context.textTertiaryCol, fontSize: 14),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionRow(String label, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Container(
        color: context.tileBackground,
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        child: Text(
          label,
          style: TextStyle(color: AppColors.error, fontSize: 16),
        ),
      ),
    );
  }
}
