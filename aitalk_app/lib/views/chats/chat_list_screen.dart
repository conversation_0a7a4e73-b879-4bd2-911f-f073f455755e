import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import '../../l10n/app_localizations.dart';
import '../../core/bluetooth/bluetooth_manager.dart';
import '../../core/constants/colors.dart';
import '../../core/device/device_manager.dart';
import 'package:flutter/cupertino.dart';
import 'dart:math' as math;
import 'widgets/chat_app_bar.dart';
import '../device/device_search_screen.dart';
import '../device/device_info_screen.dart';
import 'widgets/conversation_list.dart';
import '../../core/protocol/at_response_handler.dart';
import 'dart:async';
import '../../../core/protocol/device_control_request_sender.dart';
import '../../../core/bluetooth/bluetooth_manager.dart';
import 'create_group_screen.dart';
import 'join_group_screen.dart';
import '../../core/services/database_service.dart';
import 'package:sqflite/sqflite.dart';

class ChatListScreen extends StatefulWidget {
  const ChatListScreen({super.key});

  @override
  State<ChatListScreen> createState() => _ChatListScreenState();
}

class _ChatListScreenState extends State<ChatListScreen> {
  bool _isConnected = false;
  bool _uiToggle = true;
  int _batteryPercent = 80;
  int _unreadCount = 0;
  // 用于强制重新创建 ConversationList 以刷新数据
  Key _convListKey = UniqueKey();
  BluetoothDevice? _connectedDevice;
  String? _deviceId;
  StreamSubscription? _snSub; // 仅用于设备ID监听

  String? _firmwareVersion;
  String? _hardwareVersion;
  String? _turMassVersion; // maintained via DeviceManager

  // 监听连接设备变化的回调
  void _handleDeviceChange() {
    final device = BluetoothManager.currentDevice.value;
    if (device != null) {
      _connectedDevice = device;
      _isConnected = true;
      // 立即拉取电量与版本号

      if (mounted) setState(() {});
    } else {
      if (mounted) {
        setState(() {
          _isConnected = false;
          _connectedDevice = null;
          _batteryPercent = 0;
        });
      }
    }
  }

  // 会话数据移至 ConversationList widget

  @override
  void initState() {
    super.initState();
    // 监听全局连接设备变化
    BluetoothManager.currentDevice.addListener(_handleDeviceChange);
    // 初始化一次，避免错过已存在的连接
    // 启动设备管理器监听设备状态
    DeviceManager.instance.start();
    DeviceManager.instance.statusNotifier.addListener(_updateDeviceStatus);

    // 监听未读消息变化
    DatabaseService.groupChangedNotifier.addListener(_updateUnreadCount);
    _handleDeviceChange();
    _updateUnreadCount();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 若尚未有值，初始化为“加载中”
    _deviceId ??= AppLocalizations.of(context)!.deviceInfo_deviceId_loading;

    // 若已订阅则无需再次订阅
    _snSub ??= AtResponseHandler.instance.responses.listen((r) {
      if (r.type == AtResponseType.deviceId) {
        final id = r.payload?['deviceId'];
        if (id != null && mounted) {
          setState(() {
            _deviceId = id == 0
                ? AppLocalizations.of(context)!.deviceInfo_deviceId_failed
                : '0x${id.toRadixString(16).padLeft(8, '0').toUpperCase()}';
          });
        }
      } else if (r.type == AtResponseType.version) {
        final v = r.payload?['version'];
        if (v != null && mounted) {
          setState(() => _turMassVersion = v);
        }
      }
    });
  }

  // 设备状态回调，由 DeviceManager 统一管理
  void _updateDeviceStatus() {
    final status = DeviceManager.instance.statusNotifier.value;
    if (mounted) {
      setState(() {
        _batteryPercent = status.batteryPercent >= 0
            ? status.batteryPercent
            : 0;
        _firmwareVersion = status.firmwareVersion;
        _hardwareVersion = status.hardwareVersion;
        _turMassVersion = status.turMassVersion;
      });
    }
  }

  // 以下方法已被 DeviceManager 接管，保留以兼容旧代码但不再调用
  Future<void> _fetchBattery() async {
    // Deprecated: handled by DeviceManager
    return;
  }

  // 查询数据库获取总未读消息数
  Future<void> _updateUnreadCount() async {
    final db = await DatabaseService.instance.database;
    final res = await db.rawQuery(
      'SELECT SUM(unread_count) AS total FROM group_conversations',
    );
    final int total = Sqflite.firstIntValue(res) ?? 0;
    if (mounted) {
      setState(() {
        _unreadCount = total;
      });
    }
  }

  @override
  void dispose() {
    _snSub?.cancel();
    DeviceManager.instance.statusNotifier.removeListener(_updateDeviceStatus);
    DatabaseService.groupChangedNotifier.removeListener(_updateUnreadCount);
    BluetoothManager.currentDevice.removeListener(_handleDeviceChange);
    super.dispose();
  }

  // 打开设备搜索页面
  void _openDeviceSearch() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const DeviceSearchScreen()),
    );

    // 处理设备选择结果
    if (result != null && result['device'] != null) {
      final BluetoothDevice device = result['device'];

      try {
        // 显示连接中提示
        _showConnectionStatus(
          '${AppLocalizations.of(context)!.chatList_connectDevice} ${device.platformName}...',
        );

        // 连接设备
        await BluetoothManager.connect(device);

        // 初始化设备：查询序列号并写入默认参数
        try {
          await BluetoothManager.initializeDevice(device);
        } catch (e) {
          // 初始化失败不影响后续 UI，但记录日志并提示
          debugPrint('Device initialization failed: $e');
          _showConnectionStatus(
            AppLocalizations.of(
              context,
            )!.chatList_connectionFailed(e.toString()),
            isError: true,
          );
        }

        // 连接成功后由 DeviceManager 负责查询电量与版本号
        _connectedDevice = device;

        setState(() {
          _isConnected = true;
        });

        _showConnectionStatus(
          AppLocalizations.of(
            context,
          )!.chatList_connectionSuccess(device.platformName),
        );
      } catch (e) {
        _showConnectionStatus(
          AppLocalizations.of(context)!.chatList_connectionFailed(e.toString()),
          isError: true,
        );
      }
    }
  }

  // 显示连接状态提示
  void _showConnectionStatus(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
      ),
    );
  }

  // 断开连接
  Future<void> _disconnectDevice() async {
    if (_connectedDevice != null) {
      try {
        // 先向设备发送断开BLE命令，通知对端主动断开
        await DeviceControlRequestSender.sendDisconnectCommand(
          _connectedDevice!,
        );
        await BluetoothManager.disconnect(_connectedDevice!);
        setState(() {
          _isConnected = false;
          _connectedDevice = null;
          _batteryPercent = 0;
        });
        _showConnectionStatus(
          AppLocalizations.of(context)!.chatList_disconnectSuccess,
        );
      } catch (e) {
        _showConnectionStatus(
          AppLocalizations.of(context)!.chatList_disconnectFailed(e.toString()),
          isError: true,
        );
      }
    }
  }

  // 打开设备信息页面
  Future<void> _openDeviceInfo() async {
    if (_connectedDevice == null) return;

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => DeviceInfoScreen(
          deviceModel: 'ATK-100CN',
          deviceName: _connectedDevice!.platformName,
          deviceId:
              _deviceId ??
              AppLocalizations.of(context)!.deviceInfo_deviceId_loading,
          device: _connectedDevice!,
          hardwareVersion:
              _hardwareVersion ??
              AppLocalizations.of(context)!.deviceInfo_deviceId_loading,
          bleVersion:
              _firmwareVersion ??
              AppLocalizations.of(context)!.deviceInfo_deviceId_loading,
          turMassFirmwareVersion:
              _turMassVersion ??
              AppLocalizations.of(context)!.deviceInfo_deviceId_loading,
        ),
      ),
    );

    if (result == 'disconnect') {
      _disconnectDevice();
    } else if (result == 'unpair') {
      if (_connectedDevice != null) {
        // 发送断开BLE指令后再执行系统断连
        await DeviceControlRequestSender.sendDisconnectCommand(
          _connectedDevice!,
        );
        await BluetoothManager.disconnect(_connectedDevice!);
        setState(() {
          _isConnected = false;
          _connectedDevice = null;
          _batteryPercent = 0;
        });
        _showConnectionStatus(
          AppLocalizations.of(context)!.chatList_unpairSuccess,
        );
      }
    }
  }

  // 处理标题栏设备图标点击
  void _onDeviceIndicatorTap() {
    if (_isConnected) {
      _openDeviceInfo();
    } else {
      _openDeviceSearch();
    }
  }

  /// 显示右上角更多菜单
  void _showMoreMenu(BuildContext context) async {
    final Size size = MediaQuery.of(context).size;
    final double menuWidth = 85;
    final double left = size.width - menuWidth - 16; // 保证右侧 16dp 边距
    final double top = kToolbarHeight + MediaQuery.of(context).padding.top + 4;

    final result = await showMenu<String>(
      context: context,
      position: RelativeRect.fromLTRB(left, top, 16, 0),
      items: [
        PopupMenuItem(
          value: 'create',
          child: SizedBox(
            width: menuWidth,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Icon(Icons.group_add, size: 20),
                Text(AppLocalizations.of(context)!.chatList_createGroup),
              ],
            ),
          ),
        ),
        PopupMenuDivider(height: 1, thickness: 0.5),
        PopupMenuItem(
          value: 'join',
          child: SizedBox(
            width: menuWidth,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Icon(Icons.group, size: 20),
                Text(AppLocalizations.of(context)!.chatList_joinGroup),
              ],
            ),
          ),
        ),
      ],
    );

    switch (result) {
      case 'create':
        _createGroup();
        break;
      case 'join':
        _joinGroup();
        break;
      default:
        break;
    }
  }

  /// 创建群组（占位实现）
  // 点击“创建群组”
  void _createGroup() async {
    // 跳转到创建群组页面；返回结果为 true 表示已成功创建
    final bool? created = await Navigator.push<bool>(
      context,
      MaterialPageRoute(builder: (_) => const CreateGroupScreen()),
    );

    if (created == true && mounted) {
      // 触发刷新：生成新的 key 强制 ConversationList 重新创建
      setState(() {
        _convListKey = UniqueKey();
      });
    }
  }

  /// 加入群组（占位实现）
  void _joinGroup() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (_) => const JoinGroupScreen()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.bgPrimary,
      appBar: ChatAppBar(
        isConnected: _isConnected,
        uiToggle: _uiToggle,
        batteryPercent: _batteryPercent,
        unreadCount: _unreadCount,
        onDeviceTap: _onDeviceIndicatorTap,
        onToggleTap: () => setState(() => _uiToggle = !_uiToggle),
        onMoreTap: () => _showMoreMenu(context),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 8.0,
            ),
            child: TextField(
              decoration: InputDecoration(
                hintText: AppLocalizations.of(context)!.chatList_searchHint,
                hintStyle: TextStyle(color: context.textTertiaryCol),
                prefixIcon: Icon(Icons.search, color: context.textTertiaryCol),
                filled: true,
                fillColor: context.bgSecondary,
                contentPadding: const EdgeInsets.symmetric(vertical: 0),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                  borderSide: BorderSide.none,
                ),
              ),
            ),
          ),

          // Chat list
          Expanded(key: _convListKey, child: ConversationList()),
        ],
      ),
    );
  }
}
