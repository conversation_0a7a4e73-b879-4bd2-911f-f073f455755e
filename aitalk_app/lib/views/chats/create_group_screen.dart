import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';

import '../../core/constants/colors.dart';
import '../../l10n/app_localizations.dart';
import '../../core/services/database_service.dart';
import '../../core/services/group_encryption_key_service.dart';
import 'widgets/channel_picker.dart';
import '../../core/device/device_manager.dart';
import '../../core/user/user_profile.dart';
import '../../core/services/frequency_switcher.dart';
import '../../core/services/active_group_storage.dart';
import '../../core/services/conversation_manager.dart';
import '../../core/protocol/at_response_handler.dart';
import 'dart:async';

/// 创建群组页面
class CreateGroupScreen extends StatefulWidget {
  const CreateGroupScreen({super.key});

  @override
  State<CreateGroupScreen> createState() => _CreateGroupScreenState();
}

class _CreateGroupScreenState extends State<CreateGroupScreen> {
  // 群名称输入控制器
  final TextEditingController _nameController = TextEditingController();

  // 群密码输入控制器
  final TextEditingController _passwordController = TextEditingController();

  // 选中的信道（1-16）
  int _selectedChannel = 1;

  @override
  void dispose() {
    _nameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  /// 点击选择信道
  void _pickChannel() {
    // 调用通用信道选择器
    showChannelPicker(
      context: context,
      initialChannel: _selectedChannel,
      onSelected: (channel) {
        setState(() {
          _selectedChannel = channel;
        });
      },
    );
  }

  /// 点击完成
  void _onDone() async {
    // 校验群名称
    final String groupName = _nameController.text.trim();
    if (groupName.isEmpty) {
      // 若未填写群名称则提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.createGroup_setGroupName),
        ),
      );
      return;
    }

    // 生成唯一群组 ID (4 字节 16 进制字符串):
    // 规则：高 16 位使用当前 deviceId 的低 16 位，低 16 位使用当前时间戳秒数的低 16 位。
    // 可避免与公共群 (0x100000xx) 冲突，同时保证不同设备生成的 ID 不同。
    final int nowSec = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    // 去除前缀 0x / 0X 再解析
    String devHexStr =
        DeviceManager.instance.deviceIdNotifier.value ?? '0x0000';
    if (devHexStr.startsWith('0x') || devHexStr.startsWith('0X')) {
      devHexStr = devHexStr.substring(2);
    }
    int devInt;
    try {
      devInt = int.parse(devHexStr, radix: 16);
    } catch (_) {
      devInt = devHexStr.hashCode & 0xFFFFFFFF;
    }
    final int high = devInt & 0xFFFF;
    final int low = nowSec & 0xFFFF;
    final int gidInt = (high << 16) | low;
    final String groupId = gidInt
        .toRadixString(16)
        .padLeft(8, '0')
        .toUpperCase();

    final int nowMs = DateTime.now().millisecondsSinceEpoch;

    try {
      // 获取当前设备 ID（十六进制字符串，如 0x1234ABCD）
      final String? devHex = DeviceManager.instance.deviceIdNotifier.value;
      if (devHex == null || devHex == 'FAILED') {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('设备 ID 未获取，无法创建群组')));
        return;
      }

      // 去掉前缀 0x，存储为统一格式（保持带 0x 也行，保证一致即可）
      final String deviceId = devHex;

      // 获取当前用户昵称
      final String userName =
          UserProfile.instance.nicknameNotifier.value ?? 'User';

      // 获取数据库实例
      final Database db = await DatabaseService.instance.database;

      // 确保当前用户已存在于 contacts 表
      await db.insert('contacts', {
        'device_id': deviceId,
        'nickname': userName,
        'avatar_index': 0,
        'created_at': nowMs,
        'updated_at': nowMs,
      }, conflictAlgorithm: ConflictAlgorithm.ignore);

      // 获取群密码
      final String passwordStr = _passwordController.text.trim();
      int password = 0;
      if (passwordStr.isNotEmpty) {
        try {
          password = int.parse(passwordStr);
        } catch (e) {
          password = passwordStr.hashCode & 0xFFFFFFFF;
        }
      }

      // 插入 groups 表（私有群）
      await db.insert('groups', {
        'group_id': groupId,
        'group_name': groupName,
        'channel': _selectedChannel,
        'is_private': 1, // 私有群
        'password': password,
        'creator_id': deviceId,
        'created_at': nowMs,
        'updated_at': nowMs,
      }, conflictAlgorithm: ConflictAlgorithm.rollback);

      // 创建与之对应的会话记录
      await db.insert('group_conversations', {
        'conversation_id': groupId,
        'group_id': groupId,
        'unread_count': 0,
        'last_msg_time': nowSec,
      }, conflictAlgorithm: ConflictAlgorithm.ignore);

      // 插入当前用户为群成员（群主的member_id为0）
      await db.insert('group_members', {
        'group_id': groupId,
        'device_id': deviceId,
        'member_id': 0, // 群主的群内成员ID为0
        'nickname': userName,
        'joined_at': nowMs,
      }, conflictAlgorithm: ConflictAlgorithm.ignore);

      // 生成私有群组的加密密钥
      await GroupEncryptionKeyService.generatePrivateGroupKey(
        groupId,
        password,
        nowMs,
      );

      // 通知群组数据变更
      DatabaseService.groupChangedNotifier.value++;

      // 打印成功日志
      debugPrint('[CreateGroup] 成功创建私有群 $groupId (频道 $_selectedChannel)');

      // 1. 先切换设备频点到该私有群信道，并等待设备确认
      await FrequencySwitcher.switchPrivate(_selectedChannel);
      await _waitFreqAck();

      // 2. 设置当前会话为激活状态，并持久化保存
      ConversationManager.enter(groupId);
      await ActiveGroupStorage.save(groupId: groupId, creatorId: deviceId);
    } catch (e) {
      // 发生错误时提示
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('创建群组失败: $e')));
      debugPrint('[CreateGroup] 创建群组失败: $e');
      return;
    }

    // 返回上一页（聊天列表），ConversationList 将自动刷新并显示新群组
    if (mounted) {
      Navigator.pop(context, true);
    }
  }

  /// 等待设备返回 +FREQ 响应
  Future<void> _waitFreqAck({
    Duration timeout = const Duration(seconds: 3),
  }) async {
    final completer = Completer<void>();
    late StreamSubscription sub;

    sub = AtResponseHandler.instance.responses.listen((res) {
      if (res.type == AtResponseType.freq) {
        // 收到 +FREQ 响应，认为切换成功
        sub.cancel();
        if (!completer.isCompleted) completer.complete();
      }
    });

    try {
      await completer.future.timeout(timeout);
    } finally {
      await sub.cancel();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.bgSecondary,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(kToolbarHeight),
        child: SafeArea(
          bottom: false,
          child: Container(
            // 与页面背景颜色保持一致
            color: context.bgSecondary,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                // 取消按钮
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Text(
                    AppLocalizations.of(context)!.createGroup_cancel,
                    style: TextStyle(color: context.brandPrimary, fontSize: 20),
                  ),
                ),

                // 标题
                Expanded(
                  child: Text(
                    AppLocalizations.of(context)!.createGroup_title,
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: context.textPrimaryCol,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                // 完成按钮
                GestureDetector(
                  onTap: _onDone,
                  child: Text(
                    AppLocalizations.of(context)!.createGroup_done,
                    style: TextStyle(color: context.brandPrimary, fontSize: 20),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      body: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        behavior: HitTestBehavior.translucent,
        child: ListView(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
          children: [
            // 群名称
            _buildTextField(
              label: AppLocalizations.of(context)!.createGroup_setGroupName,
              controller: _nameController,
              trailing: const Icon(Icons.edit, size: 16),
            ),

            const SizedBox(height: 16),

            // 选择信道
            _buildChannelPicker(),

            const SizedBox(height: 16),

            // 群密码
            _buildTextField(
              label: AppLocalizations.of(context)!.createGroup_setPassword,
              controller: _passwordController,
              keyboardType: TextInputType.number,
              trailing: const Icon(Icons.edit, size: 16),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建通用输入框
  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    Widget? trailing,
    TextInputType keyboardType = TextInputType.text,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: context.bgPrimary,
        borderRadius: BorderRadius.circular(24),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // 左侧标签
          Text(
            label,
            style: TextStyle(fontSize: 16, color: context.textPrimaryCol),
          ),
          const Spacer(),
          // 右侧输入内容
          SizedBox(
            width: 180, // 限制宽度，避免占满
            child: TextField(
              controller: controller,
              keyboardType: keyboardType,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.right,
              decoration: const InputDecoration.collapsed(hintText: ''),
            ),
          ),
          if (trailing != null) ...[const SizedBox(width: 8), trailing],
        ],
      ),
    );
  }

  /// 构建信道选择行
  Widget _buildChannelPicker() {
    return GestureDetector(
      onTap: _pickChannel,
      child: Container(
        decoration: BoxDecoration(
          color: context.bgPrimary,
          borderRadius: BorderRadius.circular(24),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Expanded(
              child: Text(
                AppLocalizations.of(context)!.createGroup_selectChannel,
                style: TextStyle(fontSize: 16, color: context.textPrimaryCol),
              ),
            ),
            Text(
              AppLocalizations.of(context)!.channelPicker_channel(
                _selectedChannel.toString().padLeft(2, '0'),
              ),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: context.textPrimaryCol,
              ),
            ),
            const SizedBox(width: 8),
            Icon(Icons.arrow_drop_down, color: context.textPrimaryCol),
          ],
        ),
      ),
    );
  }
}
