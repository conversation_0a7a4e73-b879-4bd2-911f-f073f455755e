import 'dart:async';

import 'package:flutter/foundation.dart';
import '../../../core/protocol/di_data_dispatcher.dart';
import '../../../core/protocol/tk8620_frame_decoder.dart';
import '../../../core/services/database_service.dart';
import '../../../core/services/conversation_manager.dart';
import '../../../core/utils/group_util.dart';
import '../widgets/message_bubble.dart';

/// 公共 & 私有群通用的消息流监听器。
/// 根据 conversationId 过滤 TK8620 文本/语音消息，并转换为 [ChatMessage]。
class ChatMessageListener {
  ChatMessageListener._();

  /// 监听指定群组消息。
  /// [conversationId] 形如 "0x1A" 的十六进制字符串。
  /// [onMessage] 每当有新消息时回调 (ChatMessage, srcId)。
  /// 返回 [StreamSubscription]，在页面 dispose 时取消。
  static StreamSubscription listen(
    String conversationId,
    void Function(ChatMessage msg) onMessage,
  ) {
    // 预解析 conversationId -> int
    int? groupId;
    try {
      String id = conversationId;
      if (id.startsWith('0x') || id.startsWith('0X')) id = id.substring(2);
      groupId = int.parse(id, radix: 16);
    } catch (_) {
      groupId = null;
    }

    return DiDataDispatcher.instance.messages.listen((tkMsg) async {
      // 仅处理文本/语音
      if (tkMsg.payload is! TK8620TextData &&
          tkMsg.payload is! TK8620VoiceData) {
        return;
      }

      bool allow = false;

      if (tkMsg.payload is TK8620TextData) {
        // 文本帧：公共群可收广播或定向帧；私有群只收定向帧
        final bool isBroadcast = tkMsg.frame.dstId == 0xFFFFFFFF;
        final bool isForGroup = groupId != null && tkMsg.frame.dstId == groupId;

        final bool isPublic =
            groupId != null && GroupUtil.isPublicGroup(conversationId);

        if (isPublic) {
          allow = isBroadcast || isForGroup;
        } else {
          // 私有群严格匹配 dstId
          allow = isForGroup;
        }
      } else if (tkMsg.payload is TK8620VoiceData) {
        // 语音帧无 dstId，只有当前激活会话才显示
        allow =
            ConversationManager.currentConversationId.value == conversationId;
      }

      if (!allow) return;

      // 在私有群中，srcId是群内成员ID，需要转换为设备ID用于显示
      int actualSrcId = tkMsg.frame.srcId;
      if (!GroupUtil.isPublicGroup(conversationId)) {
        // 私有群：srcId是memberId，需要查找对应的设备ID
        final deviceId = await DatabaseService.instance.getDeviceIdByMemberId(
          conversationId,
          tkMsg.frame.srcId,
        );
        if (deviceId != null) {
          actualSrcId = deviceId;
          debugPrint(
            '🔄 私有群消息：memberId ${tkMsg.frame.srcId} -> 设备ID $actualSrcId',
          );
        } else {
          debugPrint('⚠️ 未找到memberId ${tkMsg.frame.srcId}对应的设备ID，跳过消息处理');
          return; // 如果找不到对应的设备ID，说明数据不一致，跳过处理
        }

        // 私有群中不需要调用ensureGroupMember，因为能发送消息的设备必然已经在群组中
        debugPrint('📝 私有群消息处理：跳过ensureGroupMember调用');
      } else {
        // 公共群：确保成员存在
        DatabaseService.instance.ensureGroupMember(conversationId, actualSrcId);
      }

      final now = DateTime.now();
      final timeStr =
          '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';

      if (tkMsg.payload is TK8620TextData) {
        final text = (tkMsg.payload as TK8620TextData).text;
        final chatMsg = ChatMessage(
          srcId: tkMsg.frame.srcId,
          content: text,
          isMine: false,
          time: timeStr,
          groupId: conversationId,
          memberId: !GroupUtil.isPublicGroup(conversationId)
              ? tkMsg.frame.srcId
              : null,
        );

        // 注意：不在此处保存到数据库，因为 MessageService 已经负责全局消息持久化
        // 避免重复保存导致数据库中出现重复记录

        onMessage(chatMsg);
      } else if (tkMsg.payload is TK8620VoiceData) {
        // 语音消息：创建占位符消息，实际内容从数据库加载
        final chatMsg = ChatMessage(
          srcId: tkMsg.frame.srcId,
          content: '',
          isMine: false,
          time: timeStr,
          isVoice: true,
          voicePath: '', // 占位符，实际路径从数据库获取
          groupId: conversationId,
          memberId: !GroupUtil.isPublicGroup(conversationId)
              ? tkMsg.frame.srcId
              : null,
        );

        // 通知UI有新的语音消息，UI层应该重新加载消息列表
        onMessage(chatMsg);
      }
    });
  }
}
