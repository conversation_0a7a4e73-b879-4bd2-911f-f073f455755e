import 'package:flutter/material.dart';
import '../../core/constants/colors.dart';
import '../../l10n/app_localizations.dart';
import 'widgets/channel_picker.dart';
import '../../core/protocol/tk8620_request_sender.dart';
import '../../core/services/frequency_switcher.dart';
import '../../core/bluetooth/bluetooth_manager.dart';
import '../../core/device/device_manager.dart';
import '../../core/user/user_profile.dart';
import '../../core/protocol/at_response_handler.dart';
import 'dart:async';
import '../../core/services/channel_manager.dart';

/// 加入群组页面
class JoinGroupScreen extends StatefulWidget {
  const JoinGroupScreen({super.key});

  @override
  State<JoinGroupScreen> createState() => _JoinGroupScreenState();
}

class _JoinGroupScreenState extends State<JoinGroupScreen> {
  final TextEditingController _passwordController = TextEditingController();
  int _selectedChannel = 1;

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }

  void _pickChannel() {
    showChannelPicker(
      context: context,
      initialChannel: _selectedChannel,
      onSelected: (channel) {
        setState(() {
          _selectedChannel = channel;
        });
      },
    );
  }

  void _onJoin() async {
    final password = _passwordController.text.trim();

    // 获取当前连接的设备
    final connectedDevice = BluetoothManager.currentDevice.value;

    if (connectedDevice == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请先连接设备')));
      return;
    }

    try {
      // 1. 发送切换私有频点指令
      await FrequencySwitcher.switchPrivate(_selectedChannel);

      // 同步更新全局当前信道
      ChannelManager.setChannel(_selectedChannel);

      // 2. 等待设备返回 +FREQ 响应后再继续
      //    若在超时时间内未收到, 视为失败
      await _waitFreqAck();

      // 3. 解析 srcId
      int srcId = 0x01;
      final devHex = DeviceManager.instance.deviceIdNotifier.value;
      if (devHex != null && devHex.startsWith('0x')) {
        try {
          srcId = int.parse(devHex.substring(2), radix: 16) & 0xFFFFFFFF;
        } catch (_) {}
      }

      // 4. 获取用户昵称
      final userName = UserProfile.instance.nicknameNotifier.value ?? 'User';

      // 5. 发送入群请求
      await TK8620RequestSender.sendJoinGroupRequest(
        connectedDevice,
        channel: _selectedChannel,
        password: password,
        srcId: srcId,
        userName: userName,
      );

      // 6. 显示成功消息
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('入群请求已发送 - 信道:$_selectedChannel')));

      Navigator.pop(context);
    } catch (e) {
      // 显示错误消息
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('发送入群请求失败: $e')));
    }
  }

  /// 等待设备发送 +FREQ 响应
  /// 若在 [timeout] 时间内未收到, 抛出 TimeoutException
  Future<void> _waitFreqAck({
    Duration timeout = const Duration(seconds: 3),
  }) async {
    final completer = Completer<void>();
    late StreamSubscription sub;

    sub = AtResponseHandler.instance.responses.listen((res) {
      if (res.type == AtResponseType.freq) {
        // 收到 +FREQ 响应, 认为切换成功
        sub.cancel();
        if (!completer.isCompleted) completer.complete();
      }
    });

    // 等待直到完成或超时
    try {
      await completer.future.timeout(timeout);
    } finally {
      await sub.cancel();
    }
  }

  @override
  Widget build(BuildContext context) {
    final locale = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: context.bgSecondary,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(kToolbarHeight),
        child: SafeArea(
          bottom: false,
          child: Container(
            color: context.bgSecondary,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Text(
                    locale.createGroup_cancel,
                    style: TextStyle(color: context.brandPrimary, fontSize: 20),
                  ),
                ),
                Expanded(
                  child: Text(
                    locale.joinGroup_title, // 新增本地化字段
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: context.textPrimaryCol,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                GestureDetector(
                  onTap: _onJoin,
                  child: Text(
                    locale.createGroup_done, // 复用“完成”按钮文案
                    style: TextStyle(color: context.brandPrimary, fontSize: 20),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      body: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        behavior: HitTestBehavior.translucent,
        child: ListView(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
          children: [
            _buildChannelPicker(),
            const SizedBox(height: 16),
            _buildTextField(
              label: locale.createGroup_setPassword,
              controller: _passwordController,
              keyboardType: TextInputType.number,
              trailing: const Icon(Icons.edit, size: 16),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    Widget? trailing,
    TextInputType keyboardType = TextInputType.text,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: context.bgPrimary,
        borderRadius: BorderRadius.circular(24),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          Text(
            label,
            style: TextStyle(fontSize: 16, color: context.textPrimaryCol),
          ),
          const Spacer(),
          SizedBox(
            width: 180,
            child: TextField(
              controller: controller,
              keyboardType: keyboardType,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.right,
              decoration: const InputDecoration.collapsed(hintText: ''),
            ),
          ),
          if (trailing != null) ...[const SizedBox(width: 8), trailing],
        ],
      ),
    );
  }

  Widget _buildChannelPicker() {
    final locale = AppLocalizations.of(context)!;

    return GestureDetector(
      onTap: _pickChannel,
      child: Container(
        decoration: BoxDecoration(
          color: context.bgPrimary,
          borderRadius: BorderRadius.circular(24),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Expanded(
              child: Text(
                locale.createGroup_selectChannel,
                style: TextStyle(fontSize: 16, color: context.textPrimaryCol),
              ),
            ),
            Text(
              locale.channelPicker_channel(
                _selectedChannel.toString().padLeft(2, '0'),
              ),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: context.textPrimaryCol,
              ),
            ),
            const SizedBox(width: 8),
            Icon(Icons.arrow_drop_down, color: context.textPrimaryCol),
          ],
        ),
      ),
    );
  }
}
