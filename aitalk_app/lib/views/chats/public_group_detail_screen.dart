import 'package:flutter/material.dart';
import '../../core/constants/colors.dart';
import '../../l10n/app_localizations.dart';
import 'widgets/channel_picker.dart';
import '../../core/services/database_service.dart';

/// 公共群详情页，展示群名称、信道信息，并提供搜索 / 清空聊天记录等操作。
class PublicGroupDetailScreen extends StatefulWidget {
  final String conversationId;
  final String groupName;
  final int initialChannel;

  const PublicGroupDetailScreen({
    super.key,
    required this.conversationId,
    required this.groupName,
    required this.initialChannel,
  });

  @override
  State<PublicGroupDetailScreen> createState() =>
      _PublicGroupDetailScreenState();
}

class _PublicGroupDetailScreenState extends State<PublicGroupDetailScreen> {
  late int _selectedChannel;
  bool get _hasChanged => _selectedChannel != widget.initialChannel;

  @override
  void initState() {
    super.initState();
    _selectedChannel = widget.initialChannel;
  }

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: AppBar(
        title: Text(loc.publicGroup_detail_title),
        centerTitle: true,
        actions: [
          if (_hasChanged)
            TextButton(
              onPressed: () {
                Navigator.pop(context, _selectedChannel);
              },
              child: Text(
                loc.createGroup_done,
                style: TextStyle(color: context.brandPrimary, fontSize: 16),
              ),
            ),
        ],
      ),
      body: ListView(
        children: [
          // 聊天信息
          _buildSectionHeader(context, loc.publicGroup_chatInfo),
          _buildSettingItem(
            context: context,
            title: loc.publicGroup_groupName,
            trailing: Text(
              loc.publicGroup_name(_selectedChannel.toString()),
              style: TextStyle(color: context.textSecondaryCol, fontSize: 16),
            ),
            onTap: () {},
          ),
          _buildSettingItem(
            context: context,
            title: loc.publicGroup_editChannel,
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  loc.channelPicker_channel(
                    _selectedChannel.toString().padLeft(2, '0'),
                  ),
                  style: TextStyle(color: context.brandPrimary, fontSize: 16),
                ),
                const SizedBox(width: 8),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: context.textSecondaryCol,
                ),
              ],
            ),
            onTap: _showChannelPicker,
          ),

          // 聊天记录
          _buildSectionHeader(context, loc.publicGroup_chatHistory),
          _buildSettingItem(
            context: context,
            title: loc.publicGroup_searchHistory,
            onTap: () {
              // TODO: 搜索聊天记录
            },
          ),
          _buildSettingItem(
            context: context,
            title: loc.publicGroup_deleteHistory,
            titleColor: Theme.of(context).colorScheme.error,
            onTap: () => _showDeleteConfirm(context, loc),
          ),

          const SizedBox(height: 40),
        ],
      ),
    );
  }

  void _showChannelPicker() {
    showChannelPicker(
      context: context,
      initialChannel: _selectedChannel,
      onSelected: (ch) {
        setState(() {
          _selectedChannel = ch;
        });
      },
    );
  }

  void _showDeleteConfirm(BuildContext context, AppLocalizations loc) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(loc.publicGroup_deleteHistory),
        content: Text('${loc.publicGroup_deleteHistory}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(ctx),
            child: Text(loc.channelPicker_cancel),
          ),
          TextButton(
            onPressed: () async {
              await _deleteGroupHistory();
              Navigator.pop(ctx);
              if (mounted) Navigator.pop(context);
            },
            child: Text(loc.createGroup_done),
          ),
        ],
      ),
    );
  }

  /// 删除指定群组的聊天记录和成员信息，只保留群组本身。
  Future<void> _deleteGroupHistory() async {
    final db = await DatabaseService.instance.database;

    // 1. 删除成员信息
    await db.delete(
      'group_members',
      where: 'group_id = ?',
      whereArgs: [widget.conversationId],
    );

    // 2. 删除聊天记录
    await db.delete(
      'group_messages',
      where: 'group_id = ?',
      whereArgs: [widget.conversationId],
    );

    // 3. 重置会话信息
    await db.update(
      'group_conversations',
      {'unread_count': 0, 'last_msg_time': 0},
      where: 'conversation_id = ?',
      whereArgs: [widget.conversationId],
    );

    // 通知会话列表刷新
    DatabaseService.groupChangedNotifier.value++;
  }

  // ======= 公共的分组标题和设置项构建 =======
  Widget _buildSectionHeader(BuildContext context, String title) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.only(left: 16, top: 12, bottom: 4),
      color: context.bgSecondary,
      child: Text(
        title,
        textAlign: TextAlign.left,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.normal,
          color: context.textSecondaryCol,
        ),
      ),
    );
  }

  Widget _buildSettingItem({
    required BuildContext context,
    required String title,
    Widget? trailing,
    Color? titleColor,
    required VoidCallback onTap,
  }) {
    return Container(
      color: context.tileBackground,
      child: ListTile(
        title: Text(
          title,
          style: TextStyle(
            color: titleColor ?? context.textPrimaryCol,
            fontSize: 16,
          ),
        ),
        trailing:
            trailing ??
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: context.textSecondaryCol,
            ),
        onTap: onTap,
      ),
    );
  }
}
