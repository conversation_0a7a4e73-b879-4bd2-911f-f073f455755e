import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:sqflite/sqflite.dart';
import '../../../core/services/database_service.dart';
import '../../../core/constants/colors.dart';
import '../../../l10n/app_localizations.dart' as l10n_deps;
import 'chat_list_item.dart';
import '../../../core/utils/group_util.dart';
import '../../../core/services/conversation_display_service.dart';

/// 用于在聊天首页展示最近进入的公共群会话列表。
class ConversationList extends StatefulWidget {
  const ConversationList({super.key});

  @override
  State<ConversationList> createState() => _ConversationListState();
}

class _ConversationListState extends State<ConversationList> {
  List<Map<String, dynamic>> _chatItems = [];

  @override
  void initState() {
    super.initState();
    _loadConversations();

    // 监听数据库群组变更，以刷新未读数 / 最新消息
    DatabaseService.groupChangedNotifier.addListener(_loadConversations);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 每次依赖变化（如从子页面返回）时刷新会话
    _loadConversations();
  }

  @override
  void dispose() {
    DatabaseService.groupChangedNotifier.removeListener(_loadConversations);
    super.dispose();
  }

  Future<void> _loadConversations() async {
    final db = await DatabaseService.instance.database;

    // 查询最近会话（包含私有群 & 公共群），按最后消息时间倒序
    final result = await db.rawQuery('''
      SELECT gc.conversation_id, g.group_name, gc.unread_count, gc.last_msg_time
      FROM group_conversations gc
      JOIN groups g ON gc.group_id = g.group_id
      ORDER BY gc.last_msg_time DESC
    ''');

    // 如果表为空，则确保至少插入公共群 1 的会话
    if (result.isEmpty) {
      final nowSec = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      await db.insert('group_conversations', {
        'conversation_id': GroupUtil.publicGroupId(1),
        'group_id': GroupUtil.publicGroupId(1),
        'unread_count': 0,
        'last_msg_time': nowSec,
      }, conflictAlgorithm: ConflictAlgorithm.ignore);
    }

    // 重新查询一次（保证有数据）
    final rows = await db.rawQuery('''
      SELECT gc.conversation_id, g.group_name, gc.unread_count, gc.last_msg_time
      FROM group_conversations gc
      JOIN groups g ON gc.group_id = g.group_id
      ORDER BY gc.last_msg_time DESC
    ''');

    final DateFormat fmtDate = DateFormat('MM/dd HH:mm');

    final List<Map<String, dynamic>> filtered = [];
    bool pubAdded = false;
    for (final r in rows) {
      final String convId = r['conversation_id'] as String;

      // 保证仅保留一个公共群
      if (GroupUtil.isPublicGroup(convId)) {
        if (pubAdded) continue;
        pubAdded = true;
      }

      // 查询最新一条消息
      String preview = '';
      int? previewTsMs;
      final msgRows = await db.query(
        'group_messages',
        where: 'group_id = ?',
        whereArgs: [convId],
        orderBy: 'created_at DESC',
        limit: 1,
      );

      if (msgRows.isNotEmpty) {
        final msg = msgRows.first;
        final int type = msg['message_type'] as int? ?? 0;
        previewTsMs = msg['created_at'] as int?;

        switch (type) {
          case 0: // 文本
            String content = (msg['content'] as String? ?? '');
            if (content.length > 10) content = content.substring(0, 10);
            preview = content;
            break;
          case 2: // 语音
            preview = l10n_deps.AppLocalizations.of(
              context,
            )!.chatList_voicePreview;
            break;
          case 3: // 位置
            preview = l10n_deps.AppLocalizations.of(
              context,
            )!.chatList_locationPreview;
            break;
          default:
            preview = l10n_deps.AppLocalizations.of(
              context,
            )!.chatList_messagePreview;
        }
      }

      // fallback 时间
      int lastSec = r['last_msg_time'] as int? ?? 0;
      DateTime dt;
      if (previewTsMs != null) {
        dt = DateTime.fromMillisecondsSinceEpoch(previewTsMs);
      } else {
        dt = DateTime.fromMillisecondsSinceEpoch(lastSec * 1000);
      }

      // 统计最近 24 小时内活跃用户数（基于最后一条消息的时间）
      int activeUsers = 0;
      if (GroupUtil.isPublicGroup(convId) && previewTsMs != null) {
        final int windowStart = previewTsMs - 24 * 3600 * 1000;
        final cntRows = await db.rawQuery(
          'SELECT COUNT(DISTINCT src_id) AS cnt FROM group_messages WHERE group_id = ? AND created_at BETWEEN ? AND ?',
          [convId, windowStart, previewTsMs],
        );
        activeUsers = Sqflite.firstIntValue(cntRows) ?? 0;
      }

      final String baseName = (r['group_name'] as String?) ?? '';
      final String displayName =
          GroupUtil.isPublicGroup(convId) && activeUsers > 0
          ? '$baseName ($activeUsers)'
          : baseName;

      // 将显示名写入全局服务，供其他页面同步
      ConversationDisplayService.instance.setDisplayName(convId, displayName);

      filtered.add({
        'conversationId': convId,
        'name': displayName,
        'message': preview,
        'time': fmtDate.format(dt),
        'unreadCount': r['unread_count'],
        'avatar': null,
      });
    }

    // 修改：在调用setState之前检查组件是否仍然挂载，避免在dispose后调用导致异常
    if (!mounted) return;
    setState(() {
      _chatItems = filtered;
    });
  }

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      itemCount: _chatItems.length,
      separatorBuilder: (context, index) => Divider(
        height: 1,
        indent: 72,
        color: context.bgSecondary.withOpacity(0.5),
      ),
      itemBuilder: (context, index) {
        final chat = _chatItems[index];
        return ChatListItem(
          conversationId: chat['conversationId'],
          name: chat['name'],
          message: chat['message'],
          time: chat['time'],
          unreadCount: chat['unreadCount'],
          avatar: chat['avatar'],
          onOpen: _loadConversations,
        );
      },
    );
  }
}
