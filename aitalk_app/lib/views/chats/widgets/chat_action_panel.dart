// 创建新的聊天功能面板，用于显示底部加号展开的功能项
import 'package:flutter/material.dart';
import '../../../l10n/app_localizations.dart';
import '../../../core/constants/colors.dart';

/// 聊天底部功能面板
///
/// 当前仅包含一个“PTT 对讲”按钮，后续可扩展更多功能。
class ChatActionPanel extends StatelessWidget {
  /// 是否可见
  final bool visible;

  /// 点击 PTT 对讲回调
  final VoidCallback? onPttPressed;

  /// 点击实时通话回调
  final VoidCallback? onVoiceCallPressed;

  const ChatActionPanel({
    super.key,
    required this.visible,
    this.onPttPressed,
    this.onVoiceCallPressed,
  });

  @override
  Widget build(BuildContext context) {
    // 使用 AnimatedSwitcher 进行显隐动画
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 200),
      switchInCurve: Curves.easeOut,
      switchOutCurve: Curves.easeIn,
      child: visible
          ? Container(
              key: const ValueKey(true),
              height: 120,
              padding: const EdgeInsets.only(top: 16, bottom: 12),
              color: context.bgSecondary,
              child: GridView.count(
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 4,
                children: [
                  _buildActionItem(
                    context,
                    icon: Icons.record_voice_over,
                    label: AppLocalizations.of(context)!.chatAction_ptt,
                    onTap: onPttPressed,
                  ),
                  _buildActionItem(
                    context,
                    icon: Icons.call,
                    label: AppLocalizations.of(context)!.chatAction_voiceCall,
                    onTap: onVoiceCallPressed,
                  ),
                ],
              ),
            )
          : const SizedBox.shrink(key: ValueKey(false)),
    );
  }

  Widget _buildActionItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: context.brandPrimary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: context.brandPrimary, size: 28),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(color: context.textPrimaryCol, fontSize: 12),
          ),
        ],
      ),
    );
  }
}
