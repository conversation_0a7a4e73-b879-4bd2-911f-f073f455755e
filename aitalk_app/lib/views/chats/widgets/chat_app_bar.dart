import 'dart:math' as math;

import 'package:flutter/material.dart';
import '../../../core/constants/colors.dart';
import '../../../l10n/app_localizations.dart';

/// 聊天界面通用标题栏。
class ChatAppBar extends StatelessWidget implements PreferredSizeWidget {
  final bool isConnected;
  final bool uiToggle;
  final int batteryPercent;
  final int unreadCount;
  final VoidCallback onDeviceTap;
  final VoidCallback onToggleTap;
  final VoidCallback onMoreTap;

  const ChatAppBar({
    super.key,
    required this.isConnected,
    required this.uiToggle,
    required this.batteryPercent,
    required this.unreadCount,
    required this.onDeviceTap,
    required this.onToggleTap,
    required this.onMoreTap,
  });

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    // 根据电量百分比动态选择图标与颜色
    IconData _batteryIcon(int p) {
      if (p >= 90) return Icons.battery_full; // 100-90
      if (p >= 80) return Icons.battery_6_bar; // 89-80
      if (p >= 60) return Icons.battery_5_bar; // 79-60
      if (p >= 40) return Icons.battery_4_bar; // 59-40
      if (p >= 20) return Icons.battery_3_bar; // 39-20
      if (p >= 10) return Icons.battery_2_bar; // 19-10
      if (p > 0) return Icons.battery_1_bar; // 9-1
      return Icons.battery_0_bar; // 0 or unknown
    }

    Color _batteryColor(int p) {
      if (p >= 76) return AppColors.success; // 76-100 绿色
      if (p >= 51) return AppColors.batteryMediumHigh; // 51-75 黄色
      if (p >= 26) return AppColors.warning; // 26-50 橙色
      return AppColors.error; // 0-25 红色
    }

    return AppBar(
      backgroundColor: context.bgPrimary,
      elevation: 0,
      leadingWidth: isConnected ? 150 : 90,
      leading: Padding(
        padding: const EdgeInsets.only(left: 8.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              iconSize: 30,
              icon: Image.asset(
                'assets/images/device_status_icon.png',
                width: 30,
                height: 30,
                color: isConnected ? Colors.green : context.textTertiaryCol,
              ),
              onPressed: onDeviceTap,
              tooltip: isConnected
                  ? AppLocalizations.of(context)!.chatList_disconnectDevice
                  : AppLocalizations.of(context)!.chatList_connectDevice,
            ),
            if (isConnected)
              GestureDetector(
                onTap: onToggleTap,
                child: Padding(
                  padding: const EdgeInsets.only(right: 2.0),
                  child: Icon(
                    uiToggle ? Icons.toggle_on : Icons.toggle_off,
                    color: uiToggle
                        ? context.brandPrimary
                        : context.textTertiaryCol,
                    size: 34,
                  ),
                ),
              ),
          ],
        ),
      ),
      title: Text(
        _buildTitle(context),
        style: TextStyle(
          color: context.textPrimaryCol,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
      centerTitle: true,
      actions: [
        if (isConnected)
          Padding(
            padding: const EdgeInsets.only(right: 2.0),
            child: GestureDetector(
              onTap: onDeviceTap,
              child: Transform.rotate(
                angle: math.pi / 2,
                child: Icon(
                  _batteryIcon(batteryPercent),
                  color: _batteryColor(batteryPercent),
                  size: 26,
                ),
              ),
            ),
          ),
        IconButton(
          icon: Image.asset(
            'assets/images/more_icon.png',
            width: 24,
            height: 24,
            color: context.textPrimaryCol,
          ),
          onPressed: onMoreTap,
        ),
        // 右侧留白，使电量与更多图标整体左移，与通话页对齐
        const SizedBox(width: 16),
      ],
    );
  }

  String _buildTitle(BuildContext context) {
    final base = AppLocalizations.of(context)!.global_appTitle;
    if (unreadCount > 0) {
      return '$base ($unreadCount)';
    }
    return base;
  }
}
