import 'package:flutter/material.dart';
import '../../../core/constants/colors.dart';
import '../../../l10n/app_localizations.dart';

/// 显示信道选择底部弹窗。
/// [initialChannel] 当前选中的信道 (1-16)。
/// [onSelected] 选择信道后的回调。
Future<void> showChannelPicker({
  required BuildContext context,
  required int initialChannel,
  required ValueChanged<int> onSelected,
}) {
  return showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
    ),
    builder: (context) {
      return FractionallySizedBox(
        heightFactor: 0.8, // 占屏幕高度 80%，防止溢出
        child: _ChannelPickerSheet(
          initialChannel: initialChannel,
          onSelected: onSelected,
        ),
      );
    },
  );
}

class _ChannelPickerSheet extends StatelessWidget {
  const _ChannelPickerSheet({
    required this.initialChannel,
    required this.onSelected,
  });

  final int initialChannel;
  final ValueChanged<int> onSelected;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 顶部标题行
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Text(
                    AppLocalizations.of(context)!.channelPicker_cancel,
                    style: TextStyle(color: context.brandPrimary, fontSize: 16),
                  ),
                ),
                Expanded(
                  child: Text(
                    AppLocalizations.of(context)!.channelPicker_title,
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: context.textPrimaryCol),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(width: 48), // 占位保持居中
              ],
            ),
          ),
          Divider(height: 1, color: context.bgSecondary.withOpacity(0.5)),
          Expanded(
            child: ListView.builder(
              itemCount: 16,
              itemBuilder: (context, index) {
                final channel = index + 1;
                final isSelected = channel == initialChannel;
                return ListTile(
                  onTap: () {
                    onSelected(channel);
                    Navigator.pop(context);
                  },
                  horizontalTitleGap: 8,
                  title: Text(
                    AppLocalizations.of(context)!
                        .channelPicker_channel(channel.toString().padLeft(2, '0')),
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: context.textPrimaryCol),
                  ),
                  trailing: Opacity(
                    opacity: isSelected ? 1.0 : 0.0,
                    child: Icon(Icons.check, color: context.brandPrimary),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
} 