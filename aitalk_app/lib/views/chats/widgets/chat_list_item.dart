import 'package:flutter/material.dart';
import '../../../core/constants/colors.dart';
import '../public_group_chat_screen.dart';
import '../private_group_chat_screen.dart';
import '../../../core/services/conversation_manager.dart';
import '../../../core/utils/group_util.dart';
import '../../../core/services/channel_manager.dart';
import '../../../core/services/frequency_switcher.dart';
import '../../../core/services/database_service.dart';

class ChatListItem extends StatelessWidget {
  final String conversationId;
  final String name;
  final String message;
  final String time;
  final int unreadCount;
  final String? avatar;
  final VoidCallback? onOpen;

  const ChatListItem({
    super.key,
    required this.conversationId,
    required this.name,
    required this.message,
    required this.time,
    required this.unreadCount,
    this.avatar,
    this.onOpen,
  });

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<String?>(
      valueListenable: ConversationManager.lastConversationId,
      builder: (context, activeId, _) {
        final bool isActive = activeId == conversationId;
        return ListTile(
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16.0,
            vertical: 8.0,
          ),
          leading: _buildAvatar(context, isActive),
          title: Text(
            name,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: context.textPrimaryCol,
            ),
          ),
          subtitle: Text(
            message,
            style: TextStyle(
              fontSize: 14,
              color: context.textSecondaryCol,
              overflow: TextOverflow.ellipsis,
            ),
            maxLines: 1,
          ),
          trailing: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                time,
                style: TextStyle(fontSize: 12, color: context.textTertiaryCol),
              ),
              const SizedBox(height: 4),
            ],
          ),
          onTap: () async {
            if (GroupUtil.isPublicGroup(conversationId)) {
              // 公共群：根据 groupId 解析信道并切换频点
              final int ch = GroupUtil.channelFromGroupId(conversationId) ?? 1;
              ChannelManager.setChannel(ch);
              await FrequencySwitcher.switchPublic(ch);

              await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => PublicGroupChatScreen(
                    groupName: name,
                    conversationId: conversationId,
                  ),
                ),
              );
            } else {
              // 私有群：先查询 groups 表获取信道，再切换频点
              int ch = 1;
              try {
                final db = await DatabaseService.instance.database;
                final rows = await db.query(
                  'groups',
                  columns: ['channel'],
                  where: 'group_id = ?',
                  whereArgs: [conversationId],
                  limit: 1,
                );
                if (rows.isNotEmpty && rows.first['channel'] != null) {
                  ch = rows.first['channel'] as int;
                }
              } catch (_) {}

              ChannelManager.setChannel(ch);
              await FrequencySwitcher.switchPrivate(ch);

              // 视为私有群
              await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => PrivateGroupChatScreen(
                    groupName: name,
                    conversationId: conversationId,
                  ),
                ),
              );
            }

            // 返回后刷新列表
            onOpen?.call();
          },
        );
      },
    );
  }

  Widget _buildAvatar(BuildContext context, bool isActive) {
    Widget core;
    if (avatar != null) {
      core = CircleAvatar(radius: 24, backgroundImage: NetworkImage(avatar!));
      // 若为激活会话，为头像添加绿色边框
      if (isActive) {
        core = Container(
          padding: const EdgeInsets.all(2),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: Colors.green, width: 2),
          ),
          child: core,
        );
      }
    } else {
      final String initial = name.isNotEmpty ? name[0] : '';
      core = CircleAvatar(
        radius: 24,
        backgroundColor: isActive ? Colors.green : context.bgSecondary,
        child: Text(
          initial,
          style: const TextStyle(
            fontSize: 20,
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }

    // 若有未读消息，在右上角叠加红点数字
    if (unreadCount > 0) {
      return Stack(
        clipBehavior: Clip.none,
        children: [
          core,
          Positioned(
            right: -2,
            top: -2,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
              decoration: BoxDecoration(
                color: AppColors.error,
                borderRadius: BorderRadius.circular(10),
              ),
              constraints: const BoxConstraints(minWidth: 18),
              child: Text(
                unreadCount.toString(),
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
            ),
          ),
        ],
      );
    }

    return core;
  }
}
