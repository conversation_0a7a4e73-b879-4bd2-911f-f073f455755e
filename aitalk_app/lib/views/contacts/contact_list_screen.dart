import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:intl/intl.dart';
import 'package:sqflite/sqflite.dart';
import '../../l10n/app_localizations.dart';
import '../../core/bluetooth/bluetooth_manager.dart';
import '../../core/device/device_manager.dart';
import '../../core/constants/colors.dart';
import '../../core/services/database_service.dart';
import 'dart:math' as math;
import '../device/device_search_screen.dart';
import '../device/device_info_screen.dart';
import 'widgets/contact_app_bar.dart';
import '../../core/protocol/at_response_handler.dart';
import 'dart:async';
import '../../../core/protocol/device_control_request_sender.dart';
import '../../core/utils/group_util.dart';
import '../../core/services/conversation_manager.dart';
import 'add_contact_screen.dart';

/// 联系人页面
class ContactListScreen extends StatefulWidget {
  const ContactListScreen({super.key});

  @override
  State<ContactListScreen> createState() => _ContactListScreenState();
}

class _ContactListScreenState extends State<ContactListScreen> {
  bool _isConnected = false;
  bool _uiToggle = true;
  BluetoothDevice? _connectedDevice;
  String? _deviceId;
  StreamSubscription? _snSub;
  // 实时数据通过 DeviceManager.statusNotifier 获取，无需本地缓存
  // 联系人原始列表
  List<Map<String, dynamic>> _contacts = [];

  Map<String, List<Map<String, dynamic>>> _groupedContacts = {};

  // 当前已加载的公共群 ID（如 10000002），用于判断是否需要刷新
  String? _loadedPublicGroupId;

  // turMass 固件版本号（通过 DeviceManager.statusNotifier 获取实时值，也可回退到此字段）
  String? _turMassVersion;

  // 监听连接设备变化的回调
  void _handleDeviceChange() {
    final device = BluetoothManager.currentDevice.value;
    if (device != null) {
      _connectedDevice = device;
      _isConnected = true;

      if (mounted) setState(() {});
    } else {
      if (mounted) {
        setState(() {
          _isConnected = false;
          _connectedDevice = null;
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();
    // 监听全局连接设备变化
    BluetoothManager.currentDevice.addListener(_handleDeviceChange);
    // 初始化一次，避免错过已存在的连接
    _handleDeviceChange();
    _loadContacts();

    // 监听群组变更
    DatabaseService.groupChangedNotifier.addListener(_loadContacts);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 若尚未有值，初始化为"加载中"
    _deviceId ??= AppLocalizations.of(context)!.deviceInfo_deviceId_loading;

    // 若已订阅则无需再次订阅
    _snSub ??= AtResponseHandler.instance.responses.listen((r) {
      if (r.type == AtResponseType.deviceId) {
        final id = r.payload?['deviceId'];
        if (id != null && mounted) {
          setState(() {
            _deviceId = id == 0
                ? AppLocalizations.of(context)!.deviceInfo_deviceId_failed
                : '0x${id.toRadixString(16).padLeft(8, '0').toUpperCase()}';
          });
        }
        // turMass 版本自动通过 DeviceManager 获取
      } else if (false && r.type == AtResponseType.version) {
        final v = r.payload?['version'];
        if (v != null && mounted) {
          setState(() => _turMassVersion = v);
        }
      }
      // 依赖变化时检查是否需要刷新公共群
      _refreshContactsIfNeeded();
    });

    // 每次重新依赖时检查一次
    _refreshContactsIfNeeded();
  }

  /// 加载联系人数据（从数据库获取群组信息）
  Future<void> _loadContacts() async {
    final db = await DatabaseService.instance.database;

    // 1. 取得最近一次进入的公共群（若无则默认为 01）
    String selectedPublicGroupId = GroupUtil.publicGroupId(1);
    final List<Map<String, Object?>> lastPubRes = await db.rawQuery('''
      SELECT group_id
      FROM group_conversations
      WHERE group_id LIKE '100000%'
      ORDER BY last_msg_time DESC
      LIMIT 1
    ''');
    if (lastPubRes.isNotEmpty && lastPubRes.first['group_id'] is String) {
      selectedPublicGroupId = lastPubRes.first['group_id'] as String;
    }

    // 查询所有群组
    final result = await db.rawQuery('''
      SELECT g.group_name, g.group_id
      FROM groups g
      ORDER BY g.group_name
    ''');

    final List<Map<String, dynamic>> contacts = [];

    // 添加数据库中的群组
    for (final row in result) {
      // 若为公共群，仅保留最近选中的那一个
      final String gid = row['group_id'] as String;
      if (GroupUtil.isPublicGroup(gid) && gid != selectedPublicGroupId) {
        // 跳过非当前公共群
        continue;
      }

      contacts.add({'name': row['group_name'], 'groupId': row['group_id']});
    }

    // 如果没有数据，创建一个默认的公共群01
    if (contacts.isEmpty) {
      await db.insert('groups', {
        'group_id': GroupUtil.publicGroupId(1),
        'group_name': '公共群 - 1',
        'channel': 1,
      }, conflictAlgorithm: ConflictAlgorithm.ignore);

      contacts.add({'name': '公共群 - 1', 'groupId': GroupUtil.publicGroupId(1)});
    }

    // 按首字母分组
    final Map<String, List<Map<String, dynamic>>> grouped = {};
    for (final contact in contacts) {
      final name = contact['name'] as String;
      String firstChar;

      if (name.isNotEmpty) {
        final firstCodeUnit = name.codeUnitAt(0);
        // 判断是否为中文字符
        if (firstCodeUnit >= 0x4e00 && firstCodeUnit <= 0x9fff) {
          // 中文字符，使用拼音首字母（简化处理，这里直接用字符）
          firstChar = _getChinesePinyinFirstLetter(name[0]);
        } else {
          // 英文字符
          firstChar = name[0].toUpperCase();
        }
      } else {
        firstChar = '#';
      }

      if (!grouped.containsKey(firstChar)) {
        grouped[firstChar] = [];
      }
      grouped[firstChar]!.add(contact);
    }

    // 对每个分组内的联系人进行排序
    grouped.forEach((key, value) {
      value.sort(
        (a, b) => (a['name'] as String).compareTo(b['name'] as String),
      );
    });

    setState(() {
      _contacts = contacts;
      _groupedContacts = grouped;

      // 记录已加载的公共群，避免重复刷新
      _loadedPublicGroupId = selectedPublicGroupId;
    });
  }

  /// 若公共群变更则刷新联系人列表
  Future<void> _refreshContactsIfNeeded() async {
    final db = await DatabaseService.instance.database;
    final res = await db.rawQuery('''
      SELECT group_id
      FROM group_conversations
      WHERE group_id LIKE '100000%'
      ORDER BY last_msg_time DESC
      LIMIT 1
    ''');
    String latestId = GroupUtil.publicGroupId(1);
    if (res.isNotEmpty && res.first['group_id'] is String) {
      latestId = res.first['group_id'] as String;
    }

    if (latestId != _loadedPublicGroupId) {
      await _loadContacts();
    }
  }

  /// 获取中文字符的拼音首字母（简化实现）
  String _getChinesePinyinFirstLetter(String char) {
    // 这里是一个简化的实现，实际项目中可能需要使用专门的拼音库
    switch (char) {
      case '公':
        return 'G';
      case '森':
        return 'S';
      case '张':
        return 'Z';
      default:
        return char.toUpperCase();
    }
  }

  @override
  void dispose() {
    DatabaseService.groupChangedNotifier.removeListener(_loadContacts);
    _snSub?.cancel();
    BluetoothManager.currentDevice.removeListener(_handleDeviceChange);
    super.dispose();
  }

  // 打开设备搜索页面
  void _openDeviceSearch() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const DeviceSearchScreen()),
    );

    // 处理设备选择结果
    if (result != null && result['device'] != null) {
      final BluetoothDevice device = result['device'];

      try {
        // 显示连接中提示
        _showConnectionStatus(
          '${AppLocalizations.of(context)!.chatList_connectDevice} ${device.platformName}...',
        );

        // 连接设备
        await BluetoothManager.connect(device);

        // 初始化设备：查询序列号并写入默认参数
        try {
          await BluetoothManager.initializeDevice(device);
        } catch (e) {
          // 初始化失败不影响后续 UI，但记录日志并提示
          debugPrint('Device initialization failed: $e');
          _showConnectionStatus(
            AppLocalizations.of(
              context,
            )!.chatList_connectionFailed(e.toString()),
            isError: true,
          );
        }

        _connectedDevice = device;

        setState(() {
          _isConnected = true;
        });

        _showConnectionStatus(
          AppLocalizations.of(
            context,
          )!.chatList_connectionSuccess(device.platformName),
        );
      } catch (e) {
        _showConnectionStatus(
          AppLocalizations.of(context)!.chatList_connectionFailed(e.toString()),
          isError: true,
        );
      }
    }
  }

  // 显示连接状态提示
  void _showConnectionStatus(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
      ),
    );
  }

  // 断开连接
  Future<void> _disconnectDevice() async {
    if (_connectedDevice != null) {
      try {
        // 先向设备发送断开BLE命令，通知对端主动断开
        await DeviceControlRequestSender.sendDisconnectCommand(
          _connectedDevice!,
        );
        await BluetoothManager.disconnect(_connectedDevice!);

        setState(() {
          _isConnected = false;
          _connectedDevice = null;
        });
        _showConnectionStatus(
          AppLocalizations.of(context)!.chatList_disconnectSuccess,
        );
      } catch (e) {
        _showConnectionStatus(
          AppLocalizations.of(context)!.chatList_disconnectFailed(e.toString()),
          isError: true,
        );
      }
    }
  }

  // 打开设备信息页面
  Future<void> _openDeviceInfo() async {
    if (_connectedDevice == null) return;

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => DeviceInfoScreen(
          deviceModel: 'ATK-100CN',
          deviceName: _connectedDevice!.platformName,
          deviceId:
              _deviceId ??
              AppLocalizations.of(context)!.deviceInfo_deviceId_loading,
          device: _connectedDevice!,
          hardwareVersion:
              DeviceManager.instance.statusNotifier.value.hardwareVersion ??
              AppLocalizations.of(context)!.deviceInfo_deviceId_loading,
          bleVersion:
              DeviceManager.instance.statusNotifier.value.firmwareVersion ??
              AppLocalizations.of(context)!.deviceInfo_deviceId_loading,
          turMassFirmwareVersion:
              DeviceManager.instance.statusNotifier.value.turMassVersion ??
              AppLocalizations.of(context)!.deviceInfo_deviceId_loading,
        ),
      ),
    );

    if (result == 'disconnect') {
      _disconnectDevice();
    } else if (result == 'unpair') {
      if (_connectedDevice != null) {
        // 发送断开BLE指令后再执行系统断连
        await DeviceControlRequestSender.sendDisconnectCommand(
          _connectedDevice!,
        );
        await BluetoothManager.disconnect(_connectedDevice!);

        setState(() {
          _isConnected = false;
          _connectedDevice = null;
        });
        _showConnectionStatus(
          AppLocalizations.of(context)!.chatList_unpairSuccess,
        );
      }
    }
  }

  // 处理标题栏设备图标点击
  void _onDeviceIndicatorTap() {
    if (_isConnected) {
      _openDeviceInfo();
    } else {
      _openDeviceSearch();
    }
  }

  // 添加好友
  void _addFriend() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddContactScreen()),
    );
  }

  // 处理联系人点击
  void _onContactTap(Map<String, dynamic> contact) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('点击了联系人: ${contact['name']}'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // 页面构建时检查公共群是否需要刷新
    _refreshContactsIfNeeded();
    return Scaffold(
      backgroundColor: context.bgPrimary,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(kToolbarHeight),
        child: ValueListenableBuilder<DeviceStatus>(
          valueListenable: DeviceManager.instance.statusNotifier,
          builder: (context, status, _) {
            return ContactAppBar(
              isConnected: _isConnected,
              uiToggle: _uiToggle,
              batteryPercent: status.batteryPercent,
              onDeviceTap: _onDeviceIndicatorTap,
              onToggleTap: () => setState(() => _uiToggle = !_uiToggle),
              onAddFriend: _addFriend,
            );
          },
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 8.0,
            ),
            child: TextField(
              decoration: InputDecoration(
                hintText: AppLocalizations.of(context)!.chatList_searchHint,
                hintStyle: TextStyle(color: context.textTertiaryCol),
                prefixIcon: Icon(Icons.search, color: context.textTertiaryCol),
                filled: true,
                fillColor: context.bgSecondary,
                contentPadding: const EdgeInsets.symmetric(vertical: 0),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                  borderSide: BorderSide.none,
                ),
              ),
            ),
          ),

          // Contacts list
          Expanded(child: _buildContactsList()),
        ],
      ),
    );
  }

  /// 构建联系人列表
  Widget _buildContactsList() {
    if (_groupedContacts.isEmpty) {
      return Center(
        child: Text(
          '暂无联系人',
          style: TextStyle(fontSize: 16, color: context.textTertiaryCol),
        ),
      );
    }

    final sortedKeys = _groupedContacts.keys.toList()..sort();

    return ListView.builder(
      itemCount: sortedKeys.length,
      itemBuilder: (context, index) {
        final letter = sortedKeys[index];
        final contacts = _groupedContacts[letter]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 字母分组标题
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 8.0,
              ),
              color: context.bgPrimary,
              child: Text(
                letter,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: context.textSecondaryCol,
                ),
              ),
            ),
            // 该字母下的联系人
            ...contacts
                .map((contact) => _buildContactItem(context, contact))
                .toList(),
          ],
        );
      },
    );
  }

  /// 构建联系人项
  Widget _buildContactItem(BuildContext context, Map<String, dynamic> contact) {
    return ValueListenableBuilder<String?>(
      valueListenable: ConversationManager.lastConversationId,
      builder: (context, activeId, _) {
        final String? groupId = contact['groupId'] as String?;
        final bool isActive = activeId == groupId;

        return Column(
          children: [
            ListTile(
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 8.0,
              ),
              leading: _buildAvatar(context, contact['name'], isActive),
              title: Text(
                contact['name'],
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: context.textPrimaryCol,
                ),
              ),
              onTap: () => _onContactTap(contact),
            ),
            Divider(
              height: 1,
              indent: 72,
              color: context.bgSecondary.withValues(alpha: 0.5),
            ),
          ],
        );
      },
    );
  }

  /// 构建头像
  Widget _buildAvatar(BuildContext context, String name, bool isActive) {
    // 生成占位头像，使用联系人姓名的第一个字符
    final String initial = name.isNotEmpty ? name[0] : '';

    // 根据是否为激活群组决定背景颜色
    Color backgroundColor = isActive ? Colors.green : context.bgSecondary;

    return CircleAvatar(
      radius: 24,
      backgroundColor: backgroundColor,
      child: Text(
        initial,
        style: const TextStyle(
          fontSize: 20,
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
