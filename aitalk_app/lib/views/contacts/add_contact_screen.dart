import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../../core/constants/colors.dart';
import '../../core/models/user_qr_data.dart';
import '../../core/services/contact_service.dart';
import '../../core/services/database_service.dart';
import '../../core/device/device_manager.dart';
import 'qr_scanner_screen.dart';

/// 添加联系人页面
class AddContactScreen extends StatefulWidget {
  const AddContactScreen({super.key});

  @override
  State<AddContactScreen> createState() => _AddContactScreenState();
}

class _AddContactScreenState extends State<AddContactScreen> {
  /// 扫描群组二维码
  void _scanGroupQR() async {
    try {
      final result = await Navigator.push<String>(
        context,
        MaterialPageRoute(builder: (context) => const QrScannerScreen()),
      );

      if (result != null && mounted) {
        // 处理群组二维码扫描结果
        _handleGroupQRResult(result);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${AppLocalizations.of(context)!.qrScanner_scanFailed}: $e',
            ),
          ),
        );
      }
    }
  }

  /// 扫描联系人二维码
  void _scanContactQR() async {
    try {
      final result = await Navigator.push<String>(
        context,
        MaterialPageRoute(builder: (context) => const QrScannerScreen()),
      );

      if (result != null && mounted) {
        // 处理联系人二维码扫描结果
        _handleContactQRResult(result);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${AppLocalizations.of(context)!.qrScanner_scanFailed}: $e',
            ),
          ),
        );
      }
    }
  }

  /// 处理群组二维码扫描结果
  void _handleGroupQRResult(String qrData) async {
    print('🔍 扫描到二维码原始数据: $qrData');

    // 尝试解码为群组二维码
    final groupData = GroupQrData.fromQrString(qrData);
    if (groupData != null) {
      print('✅ 成功解码群组二维码:');
      print('  - 群组ID: ${groupData.groupId}');
      print('  - 群组名称: ${groupData.groupName}');
      print('  - 信道: ${groupData.channel}');
      print('  - 密码: ${groupData.password}');
      print('  - 成员数量: ${groupData.memberCount}');
      print('  - 成员列表: ${groupData.members}');

      // 获取当前用户设备ID
      final currentDeviceId = DeviceManager.instance.deviceIdNotifier.value;
      if (currentDeviceId == null || currentDeviceId == 'FAILED') {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('无法获取设备ID，请先连接设备')));
        }
        return;
      }

      // 加入群组
      final success = await ContactService.instance.joinGroup(
        groupData,
        currentDeviceId,
      );
      if (success && mounted) {
        print('✅ 成功加入群组: ${groupData.groupName}');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('成功加入群组: ${groupData.groupName}')),
        );
        // 通知联系人列表刷新
        DatabaseService.groupChangedNotifier.value++;
      } else if (mounted) {
        print('❌ 加入群组失败');
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('加入群组失败，请重试')));
      }
    } else {
      print('❌ 无法解码为群组二维码');
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('无效的群组二维码')));
    }
  }

  /// 处理联系人二维码扫描结果
  void _handleContactQRResult(String qrData) {
    print('🔍 扫描到二维码原始数据: $qrData');

    // 尝试解码为用户二维码
    final userData = UserQrData.fromQrString(qrData);
    if (userData != null) {
      print('✅ 成功解码联系人二维码:');
      print('  - 昵称: ${userData.nickname}');
      print('  - 设备ID: ${userData.deviceId}');
      print(
        '  - 频点: ${userData.frequency} Hz (${userData.frequencyDisplayText})',
      );
      print('  - 速率模式: ${userData.rateMode}');

      // TODO: 联系人功能暂不实现
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('扫描到联系人: ${userData.nickname} (功能开发中)')),
      );
    } else {
      print('❌ 无法解码为联系人二维码');
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('无效的联系人二维码')));
    }
  }

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: context.bgPrimary,
      appBar: AppBar(
        backgroundColor: context.bgPrimary,
        elevation: 0,
        title: Text(
          loc.addContact_title,
          style: TextStyle(
            color: context.textPrimaryCol,
            fontSize: 18,
            fontWeight: FontWeight.normal,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: context.textPrimaryCol),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            const SizedBox(height: 20),

            // 扫描群组二维码选项
            _buildScanOption(
              context: context,
              icon: Icons.group,
              title: loc.addContact_scanGroupQR,
              description: loc.addContact_scanGroupQRDesc,
              onTap: _scanGroupQR,
            ),

            const SizedBox(height: 16),

            // 扫描联系人二维码选项
            _buildScanOption(
              context: context,
              icon: Icons.person_add,
              title: loc.addContact_scanContactQR,
              description: loc.addContact_scanContactQRDesc,
              onTap: _scanContactQR,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建扫描选项卡片
  Widget _buildScanOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String description,
    required VoidCallback onTap,
  }) {
    return Card(
      color: context.bgSecondary,
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: context.brandPrimary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Icon(icon, color: context.brandPrimary, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        color: context.textPrimaryCol,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        color: context.textSecondaryCol,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: context.textTertiaryCol,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
