import 'dart:async';
import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';
import '../../core/constants/colors.dart';
import '../../l10n/app_localizations.dart';
import '../../core/bluetooth/bluetooth_manager.dart';
import '../../core/device/device_manager.dart';
import '../../core/protocol/tk8620_request_sender.dart';

/// 实时通话拨通界面
/// 显示群组头像、名称、等待接听状态和挂断按钮
class VoiceCallDialingScreen extends StatefulWidget {
  final String conversationId;
  final String groupName;

  const VoiceCallDialingScreen({
    super.key,
    required this.conversationId,
    required this.groupName,
  });

  @override
  State<VoiceCallDialingScreen> createState() => _VoiceCallDialingScreenState();
}

class _VoiceCallDialingScreenState extends State<VoiceCallDialingScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  // 定时器和音频播放
  Timer? _requestTimer;
  Timer? _ringtoneTimer;
  AudioPlayer? _audioPlayer;
  bool _isDialing = true;

  @override
  void initState() {
    super.initState();

    // 创建脉冲动画控制器
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // 开始脉冲动画
    _pulseController.repeat(reverse: true);

    // 开始拨通流程
    _startDialing();
  }

  /// 开始拨通流程：播放响铃音并定时发送通话请求
  void _startDialing() {
    // 播放响铃音（循环播放）
    _playRingtone();

    // 立即发送第一次请求
    _sendCreateTalkRequest();

    // 每隔5秒发送一次建立通话请求
    _requestTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (_isDialing) {
        _sendCreateTalkRequest();
      }
    });
  }

  /// 播放响铃音
  void _playRingtone() async {
    try {
      // 尝试播放自定义响铃音频文件
      _audioPlayer = AudioPlayer();
      await _audioPlayer!.setReleaseMode(ReleaseMode.loop);
      await _audioPlayer!.play(AssetSource('audio/ringtone.mp3'));
      debugPrint('🔔 开始播放自定义响铃音频文件');
    } catch (e) {
      debugPrint('⚠️ 无法播放自定义音频文件，使用模拟响铃: $e');
      // 备用方案：使用模拟响铃
      _simulateRingtone();
    }
  }

  /// 模拟响铃音效（通过定时器实现）
  void _simulateRingtone() {
    // 每2秒打印一次响铃提示，模拟响铃效果
    _ringtoneTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      if (!_isDialing) {
        timer.cancel();
        return;
      }
      debugPrint('🔔 响铃中...');
    });
  }

  /// 发送建立实时通话通道请求
  void _sendCreateTalkRequest() async {
    try {
      final device = BluetoothManager.currentDevice.value;
      if (device == null) {
        debugPrint('❌ 没有连接的设备，无法发送通话请求');
        return;
      }

      // 获取当前会话ID（群组ID是8位十六进制字符串）
      int sessionId = 0;
      String groupIdStr = widget.conversationId;

      // 移除可能的0x前缀
      if (groupIdStr.startsWith('0x') || groupIdStr.startsWith('0X')) {
        groupIdStr = groupIdStr.substring(2);
      }

      // 解析十六进制群组ID
      try {
        sessionId = int.parse(groupIdStr, radix: 16);
        debugPrint(
          '✅ 解析群组ID成功: $groupIdStr -> 0x${sessionId.toRadixString(16).padLeft(8, '0')}',
        );
      } catch (e) {
        debugPrint('❌ 解析群组ID失败: $groupIdStr, 错误: $e');
        debugPrint('❌ 群组ID格式无效，停止发送通话请求');
        return; // 群组ID无效时不发送请求
      }

      // 获取源设备ID（使用完整的4字节设备ID）
      int srcId = 0x01;
      final deviceIdHex = DeviceManager.instance.deviceIdNotifier.value;
      if (deviceIdHex != null && deviceIdHex.startsWith('0x')) {
        try {
          srcId = int.parse(deviceIdHex.substring(2), radix: 16);
          debugPrint(
            '✅ 获取设备ID成功: $deviceIdHex -> 0x${srcId.toRadixString(16).padLeft(8, '0')}',
          );
        } catch (e) {
          debugPrint('❌ 解析设备ID失败: $deviceIdHex, 错误: $e');
          // 使用默认值
        }
      }

      // 发送建立通话请求
      await TK8620RequestSender.sendCreateTalkRequest(
        device,
        sessionId: sessionId,
        srcId: srcId,
        dstId: sessionId, // 目标ID设置为群组ID
      );

      debugPrint(
        '📞 发送建立通话请求完成 -> sessionId: 0x${sessionId.toRadixString(16).padLeft(8, '0')}',
      );
    } catch (e) {
      debugPrint('❌ 发送建立通话请求失败: $e');
    }
  }

  @override
  void dispose() {
    // 停止拨通流程
    _stopDialing();

    _pulseController.dispose();
    super.dispose();
  }

  /// 停止拨通流程：停止定时器和音频播放
  void _stopDialing() {
    _isDialing = false;
    _requestTimer?.cancel();
    _requestTimer = null;

    // 停止模拟响铃定时器
    _ringtoneTimer?.cancel();
    _ringtoneTimer = null;

    // 停止自定义音频播放器
    if (_audioPlayer != null) {
      try {
        _audioPlayer!.stop();
        _audioPlayer!.dispose();
        _audioPlayer = null;
        debugPrint('🔇 停止自定义音频播放器');
      } catch (e) {
        debugPrint('⚠️ 停止自定义音频播放器失败: $e');
      }
    }

    debugPrint('📞 停止拨通流程');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: BoxDecoration(
          // 背景使用头像的模糊效果
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              context.brandPrimary.withValues(alpha: 0.1),
              context.brandPrimary.withValues(alpha: 0.05),
              Colors.white.withValues(alpha: 0.9),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 顶部返回按钮
              Align(
                alignment: Alignment.topLeft,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: GestureDetector(
                    onTap: () {
                      _stopDialing();
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.arrow_back_ios_new,
                        color: Colors.black54,
                        size: 20,
                      ),
                    ),
                  ),
                ),
              ),

              const Spacer(flex: 2),

              // 群组头像（带脉冲动画）
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: Container(
                      width: 160,
                      height: 160,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: context.brandPrimary,
                        boxShadow: [
                          BoxShadow(
                            color: context.brandPrimary.withValues(alpha: 0.3),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: Center(
                        child: Text(
                          widget.groupName.isNotEmpty
                              ? widget.groupName[0]
                              : '',
                          style: const TextStyle(
                            fontSize: 80,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: 32),

              // 群组名称
              Text(
                widget.groupName,
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.w600,
                  color: context.textPrimaryCol,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 16),

              // 等待接听状态
              Text(
                AppLocalizations.of(context)!.voiceCall_waitingAnswer,
                style: TextStyle(fontSize: 16, color: context.textSecondaryCol),
                textAlign: TextAlign.center,
              ),

              const Spacer(flex: 3),

              // 挂断按钮
              GestureDetector(
                onTap: () {
                  _stopDialing();
                  Navigator.of(context).pop();
                },
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black26,
                        blurRadius: 10,
                        offset: Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.call_end,
                    color: Colors.white,
                    size: 40,
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // 挂断文字
              Text(
                AppLocalizations.of(context)!.voiceCall_hangUp,
                style: TextStyle(fontSize: 16, color: context.textSecondaryCol),
              ),

              const Spacer(flex: 2),
            ],
          ),
        ),
      ),
    );
  }
}
