import 'dart:async';
import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';
import '../../core/constants/colors.dart';
import '../../l10n/app_localizations.dart';
import '../../core/bluetooth/bluetooth_manager.dart';
import '../../core/device/device_manager.dart';
import '../../core/protocol/tk8620_request_sender.dart';
import 'voice_call_active_screen.dart';
import '../../core/protocol/at_commands.dart';
import '../../core/bluetooth/passthrough_gatt_helper.dart';
import '../../core/protocol/work_mode_constants.dart';

/// 实时通话来电接听界面
/// 显示来电信息和接听/拒绝按钮
class VoiceCallIncomingScreen extends StatefulWidget {
  final String conversationId;
  final String groupName;
  final int sessionId;
  final int callerSrcId;

  const VoiceCallIncomingScreen({
    super.key,
    required this.conversationId,
    required this.groupName,
    required this.sessionId,
    required this.callerSrcId,
  });

  @override
  State<VoiceCallIncomingScreen> createState() =>
      _VoiceCallIncomingScreenState();
}

class _VoiceCallIncomingScreenState extends State<VoiceCallIncomingScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  Timer? _ringtoneTimer;
  AudioPlayer? _audioPlayer;
  bool _isCallActive = true;

  @override
  void initState() {
    super.initState();

    // 初始化脉冲动画
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // 开始脉冲动画
    _pulseController.repeat(reverse: true);

    // 开始播放响铃音
    _playRingtone();
  }

  @override
  void dispose() {
    _stopRingtone();
    _pulseController.dispose();
    super.dispose();
  }

  /// 播放响铃音
  void _playRingtone() async {
    try {
      // 尝试播放自定义响铃音频文件
      _audioPlayer = AudioPlayer();
      await _audioPlayer!.setReleaseMode(ReleaseMode.loop);
      await _audioPlayer!.play(AssetSource('audio/ringtone.mp3'));
      debugPrint('🔔 开始播放来电铃声');
    } catch (e) {
      debugPrint('⚠️ 无法播放自定义音频文件，使用模拟响铃: $e');
      // 备用方案：使用模拟响铃
      _simulateRingtone();
    }
  }

  /// 模拟响铃音效（通过定时器实现）
  void _simulateRingtone() {
    // 每2秒打印一次响铃提示，模拟响铃效果
    _ringtoneTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      if (!_isCallActive) {
        timer.cancel();
        return;
      }
      debugPrint('🔔 来电响铃中...');
    });
  }

  /// 停止响铃音
  void _stopRingtone() {
    _isCallActive = false;
    _ringtoneTimer?.cancel();
    _ringtoneTimer = null;

    // 停止自定义音频播放器
    if (_audioPlayer != null) {
      try {
        _audioPlayer!.stop();
        _audioPlayer!.dispose();
        _audioPlayer = null;
        debugPrint('🔇 停止来电铃声');
      } catch (e) {
        debugPrint('⚠️ 停止来电铃声失败: $e');
      }
    }
  }

  /// 接听通话
  void _acceptCall() async {
    _stopRingtone();

    try {
      final device = BluetoothManager.currentDevice.value;
      if (device == null) {
        debugPrint('❌ 没有连接的设备，无法发送通话响应');
        return;
      }

      // 获取源设备ID（使用完整的4字节设备ID）
      int srcId = 0x01;
      final deviceIdHex = DeviceManager.instance.deviceIdNotifier.value;
      if (deviceIdHex != null && deviceIdHex.startsWith('0x')) {
        try {
          srcId = int.parse(deviceIdHex.substring(2), radix: 16);
          debugPrint(
            '✅ 获取设备ID成功: $deviceIdHex -> 0x${srcId.toRadixString(16).padLeft(8, '0')}',
          );
        } catch (e) {
          debugPrint('❌ 解析设备ID失败: $deviceIdHex, 错误: $e');
          // 使用默认值
        }
      }

      // 发送接听响应（responseCode: 0 表示同意）
      await TK8620RequestSender.sendCreateTalkResponse(
        device,
        responseCode: 0, // 0: 同意接听
        sessionId: widget.sessionId,
        srcId: srcId,
        dstId: widget.callerSrcId, // 回复给发起方
      );

      debugPrint(
        '✅ 发送接听响应完成 -> sessionId: 0x${widget.sessionId.toRadixString(16).padLeft(8, '0')}',
      );

      // 等待1秒，确保发起方收到响应
      debugPrint('⏳ 等待1秒后配置接收方工作模式和时隙...');
      await Future.delayed(const Duration(milliseconds: 200));

      // 配置接收方时隙和工作模式：rxdata 30字节 + txdata 30字节
      await _configureReceiverSlots(device);

      // 跳转到实时通话界面
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => VoiceCallActiveScreen(
              conversationId: widget.conversationId,
              groupName: widget.groupName,
              sessionId: widget.sessionId,
              callerSrcId: widget.callerSrcId,
            ),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ 发送接听响应失败: $e');
    }
  }

  /// 拒绝通话
  void _rejectCall() async {
    _stopRingtone();

    try {
      final device = BluetoothManager.currentDevice.value;
      if (device == null) {
        debugPrint('❌ 没有连接的设备，无法发送通话响应');
        return;
      }

      // 获取源设备ID（使用完整的4字节设备ID）
      int srcId = 0x01;
      final deviceIdHex = DeviceManager.instance.deviceIdNotifier.value;
      if (deviceIdHex != null && deviceIdHex.startsWith('0x')) {
        try {
          srcId = int.parse(deviceIdHex.substring(2), radix: 16);
          debugPrint(
            '✅ 获取设备ID成功: $deviceIdHex -> 0x${srcId.toRadixString(16).padLeft(8, '0')}',
          );
        } catch (e) {
          debugPrint('❌ 解析设备ID失败: $deviceIdHex, 错误: $e');
          // 使用默认值
        }
      }

      // 发送拒绝响应（responseCode: 1 表示拒绝）
      await TK8620RequestSender.sendCreateTalkResponse(
        device,
        responseCode: 1, // 1: 拒绝接听
        sessionId: widget.sessionId,
        srcId: srcId,
        dstId: widget.callerSrcId, // 回复给发起方
      );

      debugPrint(
        '✅ 发送拒绝响应完成 -> sessionId: 0x${widget.sessionId.toRadixString(16).padLeft(8, '0')}',
      );

      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      debugPrint('❌ 发送拒绝响应失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black87,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              const Spacer(flex: 2),

              // 来电提示
              Text(
                AppLocalizations.of(context)!.voiceCall_incomingCall,
                style: const TextStyle(fontSize: 18, color: Colors.white70),
              ),

              const SizedBox(height: 32),

              // 群组头像（带脉冲动画）
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: Container(
                      width: 160,
                      height: 160,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: context.brandPrimary,
                        boxShadow: [
                          BoxShadow(
                            color: context.brandPrimary.withValues(alpha: 0.3),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: Center(
                        child: Text(
                          widget.groupName.isNotEmpty
                              ? widget.groupName[0]
                              : '',
                          style: const TextStyle(
                            fontSize: 80,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: 32),

              // 群组名称
              Text(
                widget.groupName,
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 16),

              // 通话类型提示
              Text(
                AppLocalizations.of(context)!.voiceCall_realTimeCall,
                style: const TextStyle(fontSize: 16, color: Colors.white70),
              ),

              const Spacer(flex: 3),

              // 操作按钮
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // 拒绝按钮
                  GestureDetector(
                    onTap: _rejectCall,
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.red,
                      ),
                      child: const Icon(
                        Icons.call_end,
                        color: Colors.white,
                        size: 40,
                      ),
                    ),
                  ),

                  // 接听按钮
                  GestureDetector(
                    onTap: _acceptCall,
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.green,
                      ),
                      child: const Icon(
                        Icons.call,
                        color: Colors.white,
                        size: 40,
                      ),
                    ),
                  ),
                ],
              ),

              const Spacer(flex: 1),
            ],
          ),
        ),
      ),
    );
  }

  /// 配置接收方时隙、工作模式和速率：rxdata 30字节 + txdata 30字节 + 同步从模式 + 速率11
  /// 注意：此方法在发送接听响应并等待1秒后调用
  Future<void> _configureReceiverSlots(device) async {
    try {
      debugPrint('📡 开始配置接收方时隙结构、工作模式和速率');

      // 1. 配置速率模式为实时通话专用速率
      final rateBytes = getAtCommandBytes(
        AtCommandType.setRate,
        params: {'rateMode': RateMode.realTimeCall},
      );

      await PassthroughGattHelper.sendAtCommand(
        device,
        rateBytes,
        withoutResponse: true,
      );

      debugPrint(
        '✅ 接收方速率模式配置完成: ${RateMode.getDescription(RateMode.realTimeCall)}',
      );

      // 等待速率配置生效
      await Future.delayed(const Duration(milliseconds: 300));

      // 2. 配置工作模式为同步从模式
      final workModeBytes = getAtCommandBytes(
        AtCommandType.workMode,
        params: {'mode': RealTimeCallWorkMode.receiver}, // 同步从工作模式
      );

      await PassthroughGattHelper.sendAtCommand(
        device,
        workModeBytes,
        withoutResponse: true,
      );

      debugPrint(
        '✅ 接收方工作模式配置完成: ${WorkMode.getDescription(RealTimeCallWorkMode.receiver)}',
      );

      // 等待工作模式配置生效
      await Future.delayed(const Duration(milliseconds: 300));

      // 3. 配置时隙结构：接收数据时隙35字节 + 发送数据时隙35字节
      final slots = [
        {'type': 8, 'length': 48}, // 接收数据时隙，120字节
        {'type': 7, 'length': 48}, // 发送数据时隙，120字节
      ];

      final frameConfigBytes = getAtCommandBytes(
        AtCommandType.frameConfig,
        params: {'slots': slots},
      );

      await PassthroughGattHelper.sendAtCommand(
        device,
        frameConfigBytes,
        withoutResponse: true,
      );

      debugPrint('✅ 接收方时隙配置完成: rxdata 30字节, txdata 30字节');

      // 等待时隙配置生效
      await Future.delayed(const Duration(milliseconds: 300));
    } catch (e) {
      debugPrint('❌ 配置接收方时隙和工作模式失败: $e');
    }
  }
}
