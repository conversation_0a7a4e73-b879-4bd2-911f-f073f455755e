import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import '../../l10n/app_localizations.dart';
import '../../core/bluetooth/bluetooth_manager.dart';
import '../../core/device/device_manager.dart';
import '../../core/constants/colors.dart';
import 'dart:async';
import 'dart:math' as math;
import '../device/device_search_screen.dart';
import '../device/device_info_screen.dart';
import 'widgets/call_list.dart';
import 'widgets/call_app_bar.dart';
import '../../core/protocol/at_response_handler.dart';
import '../../../core/protocol/device_control_request_sender.dart';

class CallListScreen extends StatefulWidget {
  const CallListScreen({super.key});

  @override
  State<CallListScreen> createState() => _CallListScreenState();
}

class _CallListScreenState extends State<CallListScreen> {
  bool _isConnected = false;
  bool _uiToggle = true;
  BluetoothDevice? _connectedDevice;
  String? _deviceId;
  StreamSubscription? _snSub;
  // 监听连接设备变化的回调
  void _handleDeviceChange() {
    final device = BluetoothManager.currentDevice.value;
    if (device != null) {
      _connectedDevice = device;
      _isConnected = true;
      if (mounted) setState(() {});
    } else {
      if (mounted) {
        setState(() {
          _isConnected = false;
          _connectedDevice = null;
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();
    // 监听全局连接设备变化
    BluetoothManager.currentDevice.addListener(_handleDeviceChange);
    // 初始化一次，避免错过已存在的连接
    _handleDeviceChange();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 若尚未有值，初始化为"加载中"
    _deviceId ??= AppLocalizations.of(context)!.deviceInfo_deviceId_loading;

    // 若已订阅则无需再次订阅
    _snSub ??= AtResponseHandler.instance.responses.listen((r) {
      if (r.type == AtResponseType.deviceId) {
        final id = r.payload?['deviceId'];
        if (id != null && mounted) {
          setState(() {
            _deviceId = id == 0
                ? AppLocalizations.of(context)!.deviceInfo_deviceId_failed
                : '0x${id.toRadixString(16).padLeft(8, '0').toUpperCase()}';
          });
        }
      }
    });
  }

  @override
  void dispose() {
    _snSub?.cancel();
    BluetoothManager.currentDevice.removeListener(_handleDeviceChange);
    super.dispose();
  }

  // 打开设备搜索页面
  void _openDeviceSearch() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const DeviceSearchScreen()),
    );
    
    // 处理设备选择结果
    if (result != null && result['device'] != null) {
      final BluetoothDevice device = result['device'];
      
      try {
        // 显示连接中提示
        _showConnectionStatus('${AppLocalizations.of(context)!.chatList_connectDevice} ${device.platformName}...');
        
        // 连接设备
        await BluetoothManager.connect(device);

        // 初始化设备：查询序列号并写入默认参数
        try {
          await BluetoothManager.initializeDevice(device);
        } catch (e) {
          // 初始化失败不影响后续 UI，但记录日志并提示
          debugPrint('Device initialization failed: $e');
          _showConnectionStatus(AppLocalizations.of(context)!.chatList_connectionFailed(e.toString()), isError: true);
        }
        
        _connectedDevice = device;

        setState(() {
          _isConnected = true;
        });
        
        _showConnectionStatus(AppLocalizations.of(context)!.chatList_connectionSuccess(device.platformName));
      } catch (e) {
        _showConnectionStatus(AppLocalizations.of(context)!.chatList_connectionFailed(e.toString()), isError: true);
      }
    }
  }
  
  // 显示连接状态提示
  void _showConnectionStatus(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
      ),
    );
  }
  
  // 断开连接
  Future<void> _disconnectDevice() async {
    if (_connectedDevice != null) {
      try {
        // 先向设备发送断开BLE命令，通知对端主动断开
        await DeviceControlRequestSender.sendDisconnectCommand(_connectedDevice!);
        await BluetoothManager.disconnect(_connectedDevice!);
        setState(() {
          _isConnected = false;
          _connectedDevice = null;
        });
        _showConnectionStatus(AppLocalizations.of(context)!.chatList_disconnectSuccess);
      } catch (e) {
        _showConnectionStatus(AppLocalizations.of(context)!.chatList_disconnectFailed(e.toString()), isError: true);
      }
    }
  }

  // 打开设备信息页面
  Future<void> _openDeviceInfo() async {
    if (_connectedDevice == null) return;

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => DeviceInfoScreen(
          deviceModel: 'ATK-100CN',
          deviceName: _connectedDevice!.platformName,
          deviceId: _deviceId ?? AppLocalizations.of(context)!.deviceInfo_deviceId_loading,
          device: _connectedDevice!,
          hardwareVersion: DeviceManager.instance.statusNotifier.value.hardwareVersion ?? AppLocalizations.of(context)!.deviceInfo_deviceId_loading,
          bleVersion: DeviceManager.instance.statusNotifier.value.firmwareVersion ?? AppLocalizations.of(context)!.deviceInfo_deviceId_loading,
          turMassFirmwareVersion: DeviceManager.instance.statusNotifier.value.turMassVersion ?? AppLocalizations.of(context)!.deviceInfo_deviceId_loading,
        ),
      ),
    );

    if (result == 'disconnect') {
      _disconnectDevice();
    } else if (result == 'unpair') {
      if (_connectedDevice != null) {
        // 发送断开BLE指令后再执行系统断连
        await DeviceControlRequestSender.sendDisconnectCommand(_connectedDevice!);
        await BluetoothManager.disconnect(_connectedDevice!);
        setState(() {
          _isConnected = false;
          _connectedDevice = null;
        });
        _showConnectionStatus(AppLocalizations.of(context)!.chatList_unpairSuccess);
      }
    }
  }

  // 处理标题栏设备图标点击
  void _onDeviceIndicatorTap() {
    if (_isConnected) {
      _openDeviceInfo();
    } else {
      _openDeviceSearch();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.bgPrimary,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(kToolbarHeight),
        child: ValueListenableBuilder<DeviceStatus>(
        valueListenable: DeviceManager.instance.statusNotifier,
        builder: (context, status, _) {
          return CallAppBar(
            isConnected: _isConnected,
            uiToggle: _uiToggle,
            batteryPercent: status.batteryPercent,
            onDeviceTap: _onDeviceIndicatorTap,
            onToggleTap: () => setState(() => _uiToggle = !_uiToggle),
          );
        },
      ),
    ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: TextField(
              decoration: InputDecoration(
                hintText: AppLocalizations.of(context)!.callList_searchHint,
                hintStyle: TextStyle(color: context.textTertiaryCol),
                prefixIcon: Icon(Icons.search, color: context.textTertiaryCol),
                filled: true,
                fillColor: context.bgSecondary,
                contentPadding: const EdgeInsets.symmetric(vertical: 0),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                  borderSide: BorderSide.none,
                ),
              ),
            ),
          ),
          
          // Call list
          const Expanded(child: CallList()),
        ],
      ),
    );
  }


}