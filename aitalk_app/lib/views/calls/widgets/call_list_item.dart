import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/constants/colors.dart';
import '../../../core/models/call_record.dart';

/// 通话记录列表项组件
class CallListItem extends StatelessWidget {
  final CallRecord callRecord;
  final VoidCallback? onTap;

  const CallListItem({
    super.key,
    required this.callRecord,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      leading: _buildAvatar(context),
      title: Row(
        children: [
          Expanded(
            child: Text(
              callRecord.contactName,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: context.textPrimaryCol,
              ),
            ),
          ),
          _buildCallIcon(context),
        ],
      ),
      subtitle: Row(
        children: [
          _buildCallTypeIcon(),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              _getSubtitleText(),
              style: TextStyle(
                fontSize: 14,
                color: (callRecord.callType == CallType.missed || !callRecord.isAnswered)
                    ? AppColors.callMissed
                    : context.textSecondaryCol,
                overflow: TextOverflow.ellipsis,
              ),
              maxLines: 1,
            ),
          ),
        ],
      ),
      trailing: Text(
        _getTimeText(),
        style: TextStyle(
          fontSize: 12,
          color: context.textTertiaryCol,
        ),
      ),
      onTap: onTap,
    );
  }

  /// 构建头像
  Widget _buildAvatar(BuildContext context) {
    if (callRecord.contactAvatar != null) {
      return CircleAvatar(
        radius: 24,
        backgroundImage: NetworkImage(callRecord.contactAvatar!),
      );
    } else {
      // 生成占位头像，使用联系人姓名的第一个字符
      final String initial = callRecord.contactName.isNotEmpty 
          ? callRecord.contactName[0] 
          : '';
      return CircleAvatar(
        radius: 24,
        backgroundColor: context.bgSecondary,
        child: Text(
          initial,
          style: const TextStyle(
            fontSize: 20,
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }
  }

  /// 构建通话图标（右侧）
  Widget _buildCallIcon(BuildContext context) {
    return Icon(
      Icons.call,
      size: 20,
      color: context.brandPrimary,
    );
  }

  /// 构建通话类型图标（左侧）
  Widget _buildCallTypeIcon() {
    IconData iconData;
    Color iconColor;

    switch (callRecord.callType) {
      case CallType.incoming:
        iconData = callRecord.isAnswered 
            ? Icons.call_received 
            : Icons.call_received;
        iconColor = callRecord.isAnswered 
            ? AppColors.callIncoming 
            : AppColors.callMissed;
        break;
      case CallType.outgoing:
        iconData = Icons.call_made;
        iconColor = AppColors.callOutgoing;
        break;
      case CallType.missed:
        iconData = Icons.call_received;
        iconColor = AppColors.callMissed;
        break;
    }

    return Icon(
      iconData,
      size: 16,
      color: iconColor,
    );
  }

  /// 获取副标题文本
  String _getSubtitleText() {
    final statusText = callRecord.getCallStatusText();
    final durationText = callRecord.getDurationText();
    
    if (durationText.isNotEmpty) {
      return '$statusText $durationText';
    } else {
      return statusText;
    }
  }

  /// 获取副标题颜色
  // _getSubtitleColor removed as logic moved to build()

  /// 获取时间文本
  String _getTimeText() {
    final now = DateTime.now();
    final callTime = callRecord.callTime;
    
    // 如果是今天，显示时间
    if (callTime.year == now.year && 
        callTime.month == now.month && 
        callTime.day == now.day) {
      return DateFormat('HH:mm').format(callTime);
    }
    
    // 如果是昨天，显示"昨天"
    final yesterday = now.subtract(const Duration(days: 1));
    if (callTime.year == yesterday.year && 
        callTime.month == yesterday.month && 
        callTime.day == yesterday.day) {
      return '昨天';
    }
    
    // 如果是今年，显示月日
    if (callTime.year == now.year) {
      return DateFormat('MM/dd').format(callTime);
    }
    
    // 其他情况显示年月日
    return DateFormat('yyyy/MM/dd').format(callTime);
  }
}