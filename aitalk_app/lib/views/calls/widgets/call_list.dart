import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/services/database_service.dart';
import '../../../core/constants/colors.dart';
import '../../../core/services/call_history_storage.dart';
import '../../../core/utils/group_util.dart';
import '../../../l10n/app_localizations.dart';
import '../../chats/ptt_talk_screen.dart';
import '../voice_call_screen.dart';

/// 通话记录列表组件
class CallList extends StatefulWidget {
  const CallList({super.key});

  @override
  State<CallList> createState() => _CallListState();
}

class _CallListState extends State<CallList> {
  List<Map<String, dynamic>> _callItems = [];
  // 监听通话历史变化
  void _onHistoryChanged() {
    _loadCallRecords();
  }

  @override
  void initState() {
    super.initState();
    _loadCallRecords();
    CallHistoryStorage.changedNotifier.addListener(_onHistoryChanged);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 每次依赖变化（如从子页面返回）时刷新通话记录
    _loadCallRecords();
  }

  @override
  void dispose() {
    CallHistoryStorage.changedNotifier.removeListener(_onHistoryChanged);
    super.dispose();
  }

  /// 加载通话记录数据（只显示有通话历史的群组）
  Future<void> _loadCallRecords() async {
    final db = await DatabaseService.instance.database;

    // 获取已接通会话集合（有通话历史的群组）
    final connectedIds = await CallHistoryStorage.instance.getConnectedIds();

    // 检查组件是否仍然挂载
    if (!mounted) return;

    // 获取国际化字符串
    final connectedStatus = AppLocalizations.of(context)!.callList_connected;

    final List<Map<String, dynamic>> callItems = [];

    // 只查询有通话历史的群组
    for (final conversationId in connectedIds) {
      try {
        // 查询群组信息
        final result = await db.rawQuery(
          '''
          SELECT gc.conversation_id, g.group_name, gc.unread_count, gc.last_msg_time
          FROM group_conversations gc
          JOIN groups g ON gc.group_id = g.group_id
          WHERE gc.conversation_id = ?
        ''',
          [conversationId],
        );

        if (result.isNotEmpty) {
          final row = result.first;
          final lastMsgTime = row['last_msg_time'] as int;
          final DateTime dt = DateTime.fromMillisecondsSinceEpoch(
            lastMsgTime * 1000,
          );
          final String timeStr = _formatCallTime(dt);

          callItems.add({
            'conversationId': row['conversation_id'],
            'name': row['group_name'],
            'time': timeStr,
            'callStatus': connectedStatus,
            'avatar': null,
            'lastMsgTime': lastMsgTime, // 保存原始时间戳用于排序
          });
        }
      } catch (e) {
        debugPrint('❌ 查询群组 $conversationId 信息失败: $e');
      }
    }

    // 按时间排序（最新的在前面）
    callItems.sort((a, b) {
      final timeA = a['lastMsgTime'] as int;
      final timeB = b['lastMsgTime'] as int;
      return timeB.compareTo(timeA); // 降序排列，最新的在前面
    });

    setState(() {
      _callItems = callItems;
    });
  }

  /// 格式化通话时间
  String _formatCallTime(DateTime callTime) {
    final now = DateTime.now();

    // 如果是今天，显示时间
    if (callTime.year == now.year &&
        callTime.month == now.month &&
        callTime.day == now.day) {
      return DateFormat('HH:mm').format(callTime);
    }

    // 如果是昨天，显示"昨天"
    final yesterday = now.subtract(const Duration(days: 1));
    if (callTime.year == yesterday.year &&
        callTime.month == yesterday.month &&
        callTime.day == yesterday.day) {
      return AppLocalizations.of(context)!.callList_yesterday;
    }

    // 如果是今年，显示月日
    if (callTime.year == now.year) {
      return DateFormat('MM/dd').format(callTime);
    }

    // 其他情况显示年月日
    return DateFormat('yyyy/MM/dd').format(callTime);
  }

  /// 获取通话状态颜色
  Color _getCallStatusColor(String status) {
    final loc = AppLocalizations.of(context)!;

    if (status == loc.callList_connected) {
      return AppColors.callIncoming;
    } else if (status == loc.callList_outgoing) {
      return AppColors.callOutgoing;
    } else if (status == loc.callList_missed) {
      return AppColors.callMissed;
    } else {
      return AppColors.textSecondary;
    }
  }

  /// 处理通话记录点击
  void _onCallRecordTap(Map<String, dynamic> record) async {
    final String conversationId = record['conversationId'] as String;
    final String groupName = record['name'] as String;

    // 检查是否为公共群
    final bool isPublicGroup = GroupUtil.isPublicGroup(conversationId);

    if (isPublicGroup) {
      // 公共群直接进入PTT对讲界面，不管多少人
      _navigateToPttTalk(conversationId, groupName);
      return;
    }

    try {
      // 私有群需要查询群组成员数量
      final memberCount = await _getGroupMemberCount(conversationId);

      // 检查组件是否仍然挂载
      if (!mounted) return;

      if (memberCount > 2) {
        // 超过2个人，直接进入PTT对讲界面
        _navigateToPttTalk(conversationId, groupName);
      } else if (memberCount == 2) {
        // 正好2个人，弹出选择框
        _showCallTypeDialog(conversationId, groupName);
      } else {
        // 少于2个人，提示无法通话
        _showSnackBar(AppLocalizations.of(context)!.callList_noMembersError);
      }
    } catch (e) {
      debugPrint('❌ 获取群组成员数量失败: $e');
      if (mounted) {
        _showSnackBar(AppLocalizations.of(context)!.callList_loadMembersError);
      }
    }
  }

  /// 获取群组成员数量
  Future<int> _getGroupMemberCount(String conversationId) async {
    final db = await DatabaseService.instance.database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM group_members WHERE group_id = ?',
      [conversationId],
    );

    if (result.isNotEmpty) {
      return result.first['count'] as int? ?? 0;
    }
    return 0;
  }

  /// 显示通话类型选择对话框
  void _showCallTypeDialog(String conversationId, String groupName) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context)!.callList_selectCallType),
          content: Text(
            AppLocalizations.of(context)!.callList_selectCallTypeDesc,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _navigateToPttTalk(conversationId, groupName);
              },
              child: Text(AppLocalizations.of(context)!.callList_pttTalk),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _navigateToVoiceCall(conversationId, groupName);
              },
              child: Text(AppLocalizations.of(context)!.callList_voiceCall),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(AppLocalizations.of(context)!.global_cancel),
            ),
          ],
        );
      },
    );
  }

  /// 导航到PTT对讲界面
  void _navigateToPttTalk(String conversationId, String groupName) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) =>
            PttTalkScreen(conversationId: conversationId, groupName: groupName),
      ),
    );
  }

  /// 导航到实时语音通话界面
  void _navigateToVoiceCall(String conversationId, String groupName) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => VoiceCallScreen(
          conversationId: conversationId,
          groupName: groupName,
        ),
      ),
    );
  }

  /// 显示提示消息
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), duration: const Duration(seconds: 2)),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_callItems.isEmpty) {
      return Center(
        child: Text(
          AppLocalizations.of(context)!.callList_noRecords,
          style: TextStyle(fontSize: 16, color: context.textTertiaryCol),
        ),
      );
    }

    return ListView.separated(
      itemCount: _callItems.length,
      separatorBuilder: (context, index) => Divider(
        height: 1,
        indent: 72,
        color: context.bgSecondary.withValues(alpha: 0.5),
      ),
      itemBuilder: (context, index) {
        final record = _callItems[index];
        return ListTile(
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16.0,
            vertical: 8.0,
          ),
          leading: _buildAvatar(context, record['name']),
          title: Text(
            record['name'],
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: context.textPrimaryCol,
            ),
          ),
          subtitle: Text(
            record['callStatus'],
            style: TextStyle(
              fontSize: 14,
              color: _getCallStatusColor(record['callStatus']),
              overflow: TextOverflow.ellipsis,
            ),
            maxLines: 1,
          ),
          trailing: Text(
            record['time'],
            style: TextStyle(fontSize: 12, color: context.textTertiaryCol),
          ),
          onTap: () => _onCallRecordTap(record),
        );
      },
    );
  }

  /// 构建头像
  Widget _buildAvatar(BuildContext context, String name) {
    // 生成占位头像，使用联系人姓名的第一个字符
    final String initial = name.isNotEmpty ? name[0] : '';
    return CircleAvatar(
      radius: 24,
      backgroundColor: context.bgSecondary,
      child: Text(
        initial,
        style: const TextStyle(
          fontSize: 20,
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
