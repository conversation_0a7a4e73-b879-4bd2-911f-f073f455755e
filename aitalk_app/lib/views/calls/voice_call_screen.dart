import 'package:flutter/material.dart';
import '../../core/constants/colors.dart';
import '../../l10n/app_localizations.dart';

/// 实时语音通话界面（占位符实现）
/// 用于1对1实时语音通话
class VoiceCallScreen extends StatefulWidget {
  final String conversationId;
  final String groupName;

  const VoiceCallScreen({
    super.key,
    required this.conversationId,
    required this.groupName,
  });

  @override
  State<VoiceCallScreen> createState() => _VoiceCallScreenState();
}

class _VoiceCallScreenState extends State<VoiceCallScreen> {
  bool _isMuted = false;
  bool _isSpeakerOn = false;
  bool _isCallActive = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.bgPrimary,
      appBar: AppBar(
        title: Text(widget.groupName),
        centerTitle: true,
        backgroundColor: context.bgPrimary,
        elevation: 0,
      ),
      body: Column(
        children: [
          const SizedBox(height: 40),

          // 通话状态
          Text(
            _isCallActive
                ? AppLocalizations.of(context)!.voiceCall_calling
                : AppLocalizations.of(context)!.voiceCall_ended,
            style: TextStyle(fontSize: 18, color: context.textSecondaryCol),
          ),

          const SizedBox(height: 60),

          // 头像区域
          CircleAvatar(
            radius: 80,
            backgroundColor: context.brandPrimary,
            child: Text(
              widget.groupName.isNotEmpty ? widget.groupName[0] : '',
              style: const TextStyle(
                fontSize: 60,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          const SizedBox(height: 40),

          // 通话时长
          Text(
            '00:00',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: context.textPrimaryCol,
            ),
          ),

          const Spacer(),

          // 控制按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // 静音按钮
              _buildControlButton(
                icon: _isMuted ? Icons.mic_off : Icons.mic,
                color: _isMuted ? Colors.red : context.textSecondaryCol,
                onPressed: () {
                  setState(() {
                    _isMuted = !_isMuted;
                  });
                },
              ),

              // 挂断按钮
              _buildControlButton(
                icon: Icons.call_end,
                color: Colors.red,
                size: 60,
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),

              // 扬声器按钮
              _buildControlButton(
                icon: _isSpeakerOn ? Icons.volume_up : Icons.volume_down,
                color: _isSpeakerOn
                    ? context.brandPrimary
                    : context.textSecondaryCol,
                onPressed: () {
                  setState(() {
                    _isSpeakerOn = !_isSpeakerOn;
                  });
                },
              ),
            ],
          ),

          const SizedBox(height: 60),

          // 提示文本
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              AppLocalizations.of(context)!.voiceCall_demoMessage,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 14, color: context.textTertiaryCol),
            ),
          ),

          const SizedBox(height: 40),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required Color color,
    required VoidCallback onPressed,
    double size = 50,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          shape: BoxShape.circle,
          border: Border.all(color: color, width: 2),
        ),
        child: Icon(icon, color: color, size: size * 0.5),
      ),
    );
  }
}
