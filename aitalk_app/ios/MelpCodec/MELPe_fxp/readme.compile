
gcc -I.  sc12dec.c melp_ana.c melp_chn.c melp_sub.c melp_syn.c classify.c coeff.c dsp_sub.c fec_code.c fft_lib.c fs_lib.c global.c harm.c lpc_lib.c mat_lib.c math_lib.c mathdp31.c msvq_cb.c npp.c pit_lib.c pitch.c postfilt.c qnt12.c qnt12_cb.c vq_lib.c transcode.c Win32/mathhalf.c fsvq_cb.c -o sc12dec -lm -Wall -Wextra -O2
gcc -I.  sc12enc.c melp_ana.c melp_chn.c melp_sub.c melp_syn.c classify.c coeff.c dsp_sub.c fec_code.c fft_lib.c fs_lib.c global.c harm.c lpc_lib.c mat_lib.c math_lib.c mathdp31.c msvq_cb.c npp.c pit_lib.c pitch.c postfilt.c qnt12.c qnt12_cb.c vq_lib.c transcode.c Win32/mathhalf.c fsvq_cb.c -o sc12enc -lm -Wall -Wextra -O2

gcc -I.  sc24dec.c melp_ana.c melp_chn.c melp_sub.c melp_syn.c classify.c coeff.c dsp_sub.c fec_code.c fft_lib.c fs_lib.c global.c harm.c lpc_lib.c mat_lib.c math_lib.c mathdp31.c msvq_cb.c npp.c pit_lib.c pitch.c postfilt.c qnt12.c qnt12_cb.c vq_lib.c transcode.c Win32/mathhalf.c fsvq_cb.c -o sc24dec -lm -Wall -Wextra -O2
gcc -I.  sc24enc.c melp_ana.c melp_chn.c melp_sub.c melp_syn.c classify.c coeff.c dsp_sub.c fec_code.c fft_lib.c fs_lib.c global.c harm.c lpc_lib.c mat_lib.c math_lib.c mathdp31.c msvq_cb.c npp.c pit_lib.c pitch.c postfilt.c qnt12.c qnt12_cb.c vq_lib.c transcode.c Win32/mathhalf.c fsvq_cb.c -o sc24enc -lm -Wall -Wextra -O2

./sc24enc ./C_Clean_38_5720_38_5720_20170916161415_8k.raw ./C_Clean_38_5720_38_5720_20170916161415_8k.bit
./sc12enc ./C_Clean_38_5720_38_5720_20170916161415_8k.raw ./C_Clean_38_5720_38_5720_20170916161415_8k.bit
./sc24dec ./C_Clean_38_5720_38_5720_20170916161415_8k.bit  ./C_Clean_24_out.raw
./sc12dec ./C_Clean_38_5720_38_5720_20170916161415_8k.bit  ./C_Clean_12_out.raw


gcc -I.  melp_dec.c melp_ana.c melp_chn.c melp_sub.c melp_syn.c classify.c coeff.c dsp_sub.c fec_code.c fft_lib.c fs_lib.c global.c harm.c lpc_lib.c mat_lib.c math_lib.c mathdp31.c msvq_cb.c npp.c pit_lib.c pitch.c postfilt.c qnt12.c qnt12_cb.c vq_lib.c transcode.c Win32/mathhalf.c fsvq_cb.c -o melp_dec -lm -Wall -Wextra -O2
gcc -I.  melp_enc.c melp_ana.c melp_chn.c melp_sub.c melp_syn.c classify.c coeff.c dsp_sub.c fec_code.c fft_lib.c fs_lib.c global.c harm.c lpc_lib.c mat_lib.c math_lib.c mathdp31.c msvq_cb.c npp.c pit_lib.c pitch.c postfilt.c qnt12.c qnt12_cb.c vq_lib.c transcode.c Win32/mathhalf.c fsvq_cb.c -o melp_enc -lm -Wall -Wextra -O2


./melp_enc -r 1200 ./C_Clean_38_5720_38_5720_20170916161415_8k.raw ./C_Clean_38_5720_38_5720_20170916161415_8k.bit
./melp_dec -r 1200 ./C_Clean_38_5720_38_5720_20170916161415_8k.bit  ./C_Clean_12_out.raw
./melp_enc -r 2400 ./C_Clean_38_5720_38_5720_20170916161415_8k.raw ./C_Clean_38_5720_38_5720_20170916161415_8k.bit
./melp_dec -r 2400 ./C_Clean_38_5720_38_5720_20170916161415_8k.bit  ./C_Clean_24_out.raw