Pod::Spec.new do |s|
  s.name             = 'MelpCodec'
  s.version          = '1.0.0'
  s.summary          = 'MELP Audio Codec for iOS'
  s.description      = <<-DESC
                       MELP (Mixed Excitation Linear Prediction) audio codec implementation for iOS.
                       Supports 1200bps and 2400bps encoding/decoding.
                       DESC
  s.homepage         = 'https://github.com/example/melp-codec'
  s.license          = { :type => 'MIT', :file => 'LICENSE' }
  s.author           = { 'aiTalk' => '<EMAIL>' }
  s.source           = { :path => '.' }

  s.ios.deployment_target = '15.0'

  # Source files
  s.source_files = [
    'wrapper/*.{h,c}',
    'MELPe_fxp/*.{h,c}',
    'MELPe_fxp/Win32/*.{h,c}'
  ]
  
  # Exclude files with main() functions
  s.exclude_files = [
    'MELPe_fxp/melp_enc.c',
    'MELPe_fxp/melp_dec.c',
    'MELPe_fxp/sc12enc.c',
    'MELPe_fxp/sc24enc.c',
    'MELPe_fxp/sc12dec.c',
    'MELPe_fxp/sc24dec.c',
    'MELPe_fxp/sc1200.c'
  ]

  # Public headers
  s.public_header_files = 'wrapper/*.h'
  
  # Header search paths
  s.header_mappings_dir = '.'
  
  # Compiler flags
  s.compiler_flags = [
    '-U__arm__',
    '-DIOS_BUILD',
    '-Wno-unused-variable',
    '-Wno-unused-function'
  ]
  
  # Libraries
  s.libraries = 'm'
  
  # Framework dependencies
  s.frameworks = 'Foundation'
end
