import 'package:flutter_demo_ios/core/models/user_qr_data.dart';
import 'package:flutter_demo_ios/core/services/group_qr_service.dart';

/// 群组二维码功能演示
/// 
/// 这个演示展示了如何：
/// 1. 创建群组二维码数据
/// 2. 生成二维码字符串
/// 3. 解析二维码字符串
/// 4. 验证二维码数据
void main() {
  print('=== 群组二维码功能演示 ===\n');

  // 1. 创建群组二维码数据
  print('1. 创建群组二维码数据');
  final groupQrData = GroupQrData(
    groupId: 'ABC12345',
    groupName: '测试私有群组',
    password: 123456,
    channel: 5,
    members: {
      0: '0x12345678', // 群主
      1: '0x87654321', // 成员1
      2: '0xABCDEF00', // 成员2
    },
  );

  print('群组ID: ${groupQrData.groupId}');
  print('群组名称: ${groupQrData.groupName}');
  print('信道: ${groupQrData.channelDisplayText}');
  print('密码: ${groupQrData.passwordDisplayText}');
  print('成员数量: ${groupQrData.memberCount}');
  print('成员列表:');
  groupQrData.members.forEach((memberId, deviceId) {
    print('  成员ID $memberId: $deviceId');
  });
  print('');

  // 2. 生成二维码字符串
  print('2. 生成二维码字符串');
  final qrString = groupQrData.toQrString();
  print('二维码字符串长度: ${qrString.length}');
  print('二维码字符串前50个字符: ${qrString.substring(0, 50)}...');
  print('');

  // 3. 解析二维码字符串
  print('3. 解析二维码字符串');
  final parsedData = GroupQrData.fromQrString(qrString);
  if (parsedData != null) {
    print('✅ 解析成功!');
    print('解析后的群组ID: ${parsedData.groupId}');
    print('解析后的群组名称: ${parsedData.groupName}');
    print('解析后的信道: ${parsedData.channel}');
    print('解析后的密码: ${parsedData.password}');
    print('解析后的成员数量: ${parsedData.memberCount}');
    
    // 验证数据一致性
    final isEqual = groupQrData == parsedData;
    print('数据一致性检查: ${isEqual ? "✅ 通过" : "❌ 失败"}');
  } else {
    print('❌ 解析失败!');
  }
  print('');

  // 4. 验证二维码数据
  print('4. 验证二维码数据');
  final isValid = GroupQrService.validateGroupQrData(groupQrData);
  print('数据有效性: ${isValid ? "✅ 有效" : "❌ 无效"}');
  print('');

  // 5. 测试无效数据
  print('5. 测试无效数据');
  
  // 测试无效的群组ID
  final invalidGroupId = GroupQrData(
    groupId: '', // 空群组ID
    groupName: '测试群组',
    password: 123456,
    channel: 5,
    members: {0: '0x12345678'},
  );
  final isInvalidGroupIdValid = GroupQrService.validateGroupQrData(invalidGroupId);
  print('空群组ID验证: ${isInvalidGroupIdValid ? "❌ 意外通过" : "✅ 正确拒绝"}');

  // 测试无效的信道
  final invalidChannel = GroupQrData(
    groupId: 'ABC12345',
    groupName: '测试群组',
    password: 123456,
    channel: 17, // 超出范围
    members: {0: '0x12345678'},
  );
  final isInvalidChannelValid = GroupQrService.validateGroupQrData(invalidChannel);
  print('无效信道验证: ${isInvalidChannelValid ? "❌ 意外通过" : "✅ 正确拒绝"}');
  print('');

  // 6. 测试用户二维码兼容性
  print('6. 测试用户二维码兼容性');
  const userQrData = UserQrData(
    frequency: 483600000,
    rateMode: 6,
    nickname: '测试用户',
    deviceId: '0x12345678',
  );
  final userQrString = userQrData.toQrString();
  final parsedAsGroup = GroupQrData.fromQrString(userQrString);
  print('用户二维码解析为群组: ${parsedAsGroup == null ? "✅ 正确拒绝" : "❌ 意外通过"}');
  print('');

  print('=== 演示完成 ===');
}
