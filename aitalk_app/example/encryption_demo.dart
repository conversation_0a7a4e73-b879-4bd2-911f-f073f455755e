import 'dart:typed_data';
import 'package:flutter/foundation.dart';

import '../lib/core/services/group_encryption_key_service.dart';
import '../lib/core/services/group_data_encryption.dart';
import '../lib/core/utils/encryption_utils.dart';

/// 群组加密功能演示
///
/// 这个演示展示了如何：
/// 1. 生成群组加密密钥
/// 2. 加密和解密文本消息
/// 3. 加密和解密语音数据
/// 4. 使用便捷的工具类接口
void main() async {
  print('=== 群组加密功能演示 ===\n');

  // 测试数据
  const testGroupId = 'ABC12345'; // 私有群组ID
  const testPassword = 123456;
  const testMessage = '这是一条测试消息 Hello World! 🚀';
  final testVoiceData = Uint8List.fromList([
    0x01,
    0x02,
    0x03,
    0x04,
    0x05,
    0x06,
    0x07,
    0x08,
    0x09,
    0x0A,
    0x0B,
    0x0C,
    0x0D,
    0x0E,
    0x0F,
    0x10,
  ]);

  try {
    // 1. 生成私有群组密钥
    print('1. 生成私有群组密钥');
    final keyGenerated =
        await GroupEncryptionKeyService.generatePrivateGroupKey(
          testGroupId,
          testPassword,
          DateTime.now().millisecondsSinceEpoch,
        );
    print('密钥生成结果: ${keyGenerated ? "✅ 成功" : "❌ 失败"}');

    if (!keyGenerated) {
      print('❌ 密钥生成失败，无法继续测试');
      return;
    }

    // 2. 检查加密是否启用
    print('\n2. 检查加密状态');
    final encryptionEnabled =
        await GroupEncryptionKeyService.isEncryptionEnabled(testGroupId);
    print('加密状态: ${encryptionEnabled ? "✅ 已启用" : "❌ 未启用"}');

    // 3. 获取加密密钥
    print('\n3. 获取加密密钥');
    final key = await GroupEncryptionKeyService.getEncryptionKey(testGroupId);
    if (key != null) {
      print('✅ 密钥获取成功，长度: ${key.length}字节');
      print(
        '密钥前8字节: ${key.sublist(0, 8).map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ')}',
      );
    } else {
      print('❌ 密钥获取失败');
      return;
    }

    // 4. 测试文本消息加密
    print('\n4. 测试文本消息加密');
    print('原始消息: "$testMessage"');
    print(
      '原始消息长度: ${testMessage.length}字符 (${testMessage.codeUnits.length}字节)',
    );

    final encryptedText = await GroupDataEncryption.encryptTextMessage(
      testGroupId,
      testMessage,
    );
    if (encryptedText != null) {
      print('✅ 文本加密成功，密文长度: ${encryptedText.length}字节');
      print('密文组成: IV(16字节) + 密文(${encryptedText.length - 16}字节)');
      print(
        '密文前16字节(IV): ${encryptedText.sublist(0, 16).map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ')}',
      );
      print(
        '密文数据: ${encryptedText.sublist(16).map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ')}',
      );

      // 5. 测试文本消息解密
      print('\n5. 测试文本消息解密');
      final decryptedText = await GroupDataEncryption.decryptTextMessage(
        testGroupId,
        encryptedText,
      );
      if (decryptedText != null) {
        print('✅ 文本解密成功');
        print('解密消息: "$decryptedText"');
        print('解密正确性: ${decryptedText == testMessage ? "✅ 正确" : "❌ 错误"}');
      } else {
        print('❌ 文本解密失败');
      }
    } else {
      print('❌ 文本加密失败');
    }

    // 6. 测试语音数据加密
    print('\n6. 测试语音数据加密');
    print('原始语音数据长度: ${testVoiceData.length}字节');
    print(
      '原始语音数据: ${testVoiceData.map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ')}',
    );

    final encryptedVoice = await GroupDataEncryption.encryptVoiceData(
      testGroupId,
      testVoiceData,
    );
    if (encryptedVoice != null) {
      print('✅ 语音加密成功，密文长度: ${encryptedVoice.length}字节');
      print(
        '密文前16字节: ${encryptedVoice.sublist(0, 16).map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ')}',
      );

      // 7. 测试语音数据解密
      print('\n7. 测试语音数据解密');
      final decryptedVoice = await GroupDataEncryption.decryptVoiceData(
        testGroupId,
        encryptedVoice,
      );
      if (decryptedVoice != null) {
        print('✅ 语音解密成功，明文长度: ${decryptedVoice.length}字节');
        print(
          '解密语音数据: ${decryptedVoice.map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ')}',
        );

        // 验证数据一致性
        bool isEqual = true;
        if (decryptedVoice.length == testVoiceData.length) {
          for (int i = 0; i < testVoiceData.length; i++) {
            if (decryptedVoice[i] != testVoiceData[i]) {
              isEqual = false;
              break;
            }
          }
        } else {
          isEqual = false;
        }
        print('解密正确性: ${isEqual ? "✅ 正确" : "❌ 错误"}');
      } else {
        print('❌ 语音解密失败');
      }
    } else {
      print('❌ 语音加密失败');
    }

    // 8. 测试便捷工具类
    print('\n8. 测试便捷工具类');

    // 测试文本消息工具
    final utilEncryptedText = await EncryptionUtils.encryptTextMessage(
      testGroupId,
      testMessage,
    );
    print('工具类文本加密长度: ${utilEncryptedText.length}字节');

    final utilDecryptedText = await EncryptionUtils.decryptTextMessage(
      testGroupId,
      utilEncryptedText,
    );
    print('工具类文本解密结果: "$utilDecryptedText"');
    print('工具类文本正确性: ${utilDecryptedText == testMessage ? "✅ 正确" : "❌ 错误"}');

    // 测试语音数据工具
    final utilEncryptedVoice = await EncryptionUtils.encryptVoiceData(
      testGroupId,
      testVoiceData,
    );
    print('工具类语音加密长度: ${utilEncryptedVoice.length}字节');

    final utilDecryptedVoice = await EncryptionUtils.decryptVoiceData(
      testGroupId,
      utilEncryptedVoice,
    );
    print('工具类语音解密长度: ${utilDecryptedVoice.length}字节');

    bool utilVoiceEqual = true;
    if (utilDecryptedVoice.length == testVoiceData.length) {
      for (int i = 0; i < testVoiceData.length; i++) {
        if (utilDecryptedVoice[i] != testVoiceData[i]) {
          utilVoiceEqual = false;
          break;
        }
      }
    } else {
      utilVoiceEqual = false;
    }
    print('工具类语音正确性: ${utilVoiceEqual ? "✅ 正确" : "❌ 错误"}');

    // 9. 测试公共群组密钥
    print('\n9. 测试公共群组密钥');
    const publicGroupId = '10000001'; // 公共群组ID（信道1）
    final publicKey = await GroupEncryptionKeyService.getEncryptionKey(
      publicGroupId,
    );
    if (publicKey != null) {
      print('✅ 公共群组密钥获取成功，长度: ${publicKey.length}字节');
      print(
        '公共群组密钥前8字节: ${publicKey.sublist(0, 8).map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ')}',
      );

      // 测试公共群组加密
      final publicEncrypted = await GroupDataEncryption.encryptTextMessage(
        publicGroupId,
        testMessage,
      );
      if (publicEncrypted != null) {
        final publicDecrypted = await GroupDataEncryption.decryptTextMessage(
          publicGroupId,
          publicEncrypted,
        );
        print('公共群组加密测试: ${publicDecrypted == testMessage ? "✅ 正确" : "❌ 错误"}');
      }
    } else {
      print('❌ 公共群组密钥获取失败');
    }

    print('\n=== 测试完成 ===');
  } catch (e) {
    print('❌ 测试过程中发生异常: $e');
  }
}
