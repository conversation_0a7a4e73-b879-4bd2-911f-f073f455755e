# 群组二维码功能实现文档

## 功能概述

为aiTalk应用实现了完整的私有群组二维码生成和显示功能，包含以下信息：
1. 群组名称
2. 群组密码
3. 群组ID
4. 群组信道
5. 群组成员列表（memberid + deviceid的对应矩阵）

## 实现的文件

### 1. 数据模型 (`lib/core/models/user_qr_data.dart`)
- 新增 `GroupQrData` 类
- 包含群组的所有必要信息
- 支持二维码字符串的编码和解码
- 使用Base64编码和aiTalk前缀标识
- 支持国际化的显示文本

### 2. 服务层 (`lib/core/services/group_qr_service.dart`)
- `GroupQrService` 类提供群组二维码相关服务
- 从数据库获取群组信息和成员列表
- 生成二维码字符串
- 验证二维码数据有效性
- 解析二维码字符串

### 3. UI界面 (`lib/views/chats/group_qr_code_screen.dart`)
- `GroupQrCodeScreen` 群组二维码显示界面
- 显示二维码图片
- 显示群组基本信息
- 显示详细的成员列表，包括：
  - 成员ID和设备ID的对应关系
  - 群主标识（memberid = 0）
  - 当前成员数量
- 支持保存二维码到相册
- 完全国际化支持

### 4. 国际化文本 (`lib/l10n/`)
- 中文 (`app_zh.arb`) 和英文 (`app_en.arb`) 支持
- 包含所有界面文本的国际化

### 5. 测试文件
- `test/group_qr_service_test.dart` - 数据模型和服务的单元测试
- `test/group_qr_ui_test.dart` - UI组件的测试

## 功能特性

### 二维码数据结构
```json
{
  "type": "group",
  "groupId": "ABC12345",
  "groupName": "测试群组",
  "password": 123456,
  "channel": 5,
  "members": {
    "0": "0x12345678",
    "1": "0x87654321",
    "2": "0xABCDEF00"
  },
  "version": 1
}
```

### 成员列表显示
- 每个成员显示为一行，包含：
  - 圆形成员ID标识（群主为橙色，普通成员为灰色）
  - 设备ID（使用等宽字体显示）
  - 群主标识标签（仅群主显示）

### 安全性
- 使用Base64编码隐藏明文信息
- 添加aiTalk前缀标识，防止与其他应用二维码混淆
- 支持密码保护的群组

### 兼容性
- 与现有用户二维码系统兼容
- 群组二维码和用户二维码可以正确区分
- 支持不同版本的协议扩展

## 使用方法

### 1. 在私有群组详情页面
```dart
// 点击"群二维码"按钮
void _showQRCode() {
  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => GroupQrCodeScreen(
        groupId: widget.conversationId,
        groupName: widget.groupName,
      ),
    ),
  );
}
```

### 2. 生成群组二维码数据
```dart
// 获取群组二维码数据
final qrData = await GroupQrService.getGroupQrData(groupId);

// 生成二维码字符串
final qrString = await GroupQrService.generateGroupQrString(groupId);
```

### 3. 解析二维码
```dart
// 解析二维码字符串
final groupData = GroupQrData.fromQrString(qrString);

// 验证数据有效性
final isValid = GroupQrService.validateGroupQrData(groupData);
```

## 数据库依赖

功能依赖以下数据库表：
- `groups` - 群组基本信息
- `group_members` - 群组成员关系

## 国际化支持

所有用户可见的文本都支持中英文切换：
- 群组信息标签
- 成员列表标题
- 错误提示信息
- 保存成功/失败提示

## 测试覆盖

- 数据模型的编码/解码测试
- 二维码字符串生成和解析测试
- 数据验证测试
- UI组件渲染测试
- 国际化文本测试

## 性能考虑

- 二维码生成使用异步操作，不阻塞UI
- 成员列表支持大量成员显示
- 图片保存使用高分辨率（3.0倍像素比）
- 错误处理和加载状态管理

## 扩展性

- 支持协议版本号，便于未来扩展
- 数据结构设计支持添加新字段
- UI组件模块化，易于定制
- 完整的国际化框架支持添加新语言
