# Flutter iOS Demo

这是一个使用 Flutter 创建的简单 iOS 计数器应用示例。

这个项目是为了演示 Flutter iOS 应用的基础开发流程，包括：
- 使用 `flutter create` 创建项目。
- 在 iOS 模拟器上运行应用。
- 在 Xcode 中打开和管理 iOS 部分的原生代码。

## 如何运行

### 1. 准备环境
确保你已经安装并配置好了 Flutter SDK 和 Xcode。

### 2. 获取依赖
在项目根目录运行：
```bash
flutter pub get
```

### 3. 运行在 iOS 模拟器上
- 启动一个 iOS 模拟器。
- 运行以下命令：
```bash
flutter run
```

##如何在 Xcode 中打开

1.  导航到项目的 `ios` 目录。
2.  使用 Xcode 打开 `Runner.xcworkspace` 文件。 **注意**: 请务必打开 `.xcworkspace` 文件，而不是 `.xcodeproj` 文件。

    ```bash
    open ios/Runner.xcworkspace
    ```

3.  在 Xcode 中，您可以选择一个目标设备，然后点击 "Run" (▶️) 按钮来编译和运行该应用。
