# 音频资源文件夹

## 响铃音频播放优先级

应用会按以下优先级播放响铃音：

1. **自定义音频文件**（优先级最高）
   - 将响铃音频文件命名为 `ringtone.mp3` 并放置在此文件夹中
   - 支持格式：MP3、WAV、AAC、OGG
   - 自动循环播放

2. **模拟响铃**（备用方案）
   - 当自定义音频无法播放时使用
   - 通过定时器在控制台输出响铃提示

## 自定义音频文件要求

如果要使用自定义响铃音频，建议：
- 时长：2-3秒
- 音质：适中，不要过大的文件
- 音量：适中，不要过于刺耳
- 循环播放：音频应该设计为可以无缝循环播放

## 使用方法

1. **添加自定义响铃音频**：
   - 将音频文件重命名为 `ringtone.mp3`
   - 放置在 `assets/audio/` 文件夹中
   - 应用会自动检测并播放

2. **测试响铃功能**：
   - 在私有群聊界面点击加号按钮
   - 选择"实时通话"发起通话
   - 接收端会自动播放响铃音

## 注意事项

- 如果没有自定义音频文件，应用会使用模拟响铃（控制台输出）
- 响铃音频会自动循环播放直到用户接听或拒绝
- 建议音频文件大小控制在1MB以内以确保快速加载
