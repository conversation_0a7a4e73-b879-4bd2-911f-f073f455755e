# 多帧MELP编解码实现方案

## 概述
将MELP 1.2kbps编解码器从每1帧发送一包数据改为每5帧发送一包数据，以减少网络传输频率，提高传输效率。

## 技术方案

### 1. 多帧语音组装器
**新增文件**: `lib/core/audio/multi_frame_voice_assembler.dart`

**核心功能**:
- **打包**: 将5个MELP编码帧（每帧11字节）合并为一个数据包（56字节）
- **解包**: 将接收到的数据包拆分为5个MELP帧
- **解码**: 逐帧解码MELP数据为PCM音频
- **合并**: 将多个PCM帧合并为连续音频数据

**数据格式**:
```
[帧数量1字节][帧1数据11字节][帧2数据11字节]...[帧N数据11字节]
```

### 2. 发送端修改

#### VoiceMessageProcessor
**文件**: `lib/core/audio/voice_message_processor.dart`

**关键修改**:
- 将`_framesPerSend`从1改为5
- 使用`MultiFrameVoiceAssembler.packFrames()`打包多帧数据
- 在停止录音时处理剩余帧的打包发送

**发送流程**:
1. 录音 → PCM数据
2. 编码 → MELP帧（11字节）
3. 累积5帧 → 打包为数据包（56字节）
4. 发送数据包

### 3. 接收端修改

#### VoicePlayer
**文件**: `lib/core/audio/voice_player.dart`

**新增方法**:
- `playMultiFramePacket()`: 处理多帧数据包的解码和播放

**播放流程**:
1. 接收数据包 → 解包为5个MELP帧
2. 逐帧解码 → PCM音频数据
3. 逐帧播放 → 实时音频输出

#### DiDataDispatcher
**文件**: `lib/core/protocol/di_data_dispatcher.dart`

**智能检测**:
- `_isMultiFramePacket()`: 自动检测数据包格式
- 兼容新旧格式，确保向后兼容性

**检测逻辑**:
```dart
bool _isMultiFramePacket(Uint8List audioData) {
  // 检查第一个字节是否为合理的帧数量（1-5）
  // 检查数据长度是否匹配多帧格式
  // 排除单帧数据的误判
}
```

## 数据对比

### 传输效率提升
| 项目 | 原方案（1帧/包） | 新方案（5帧/包） | 提升 |
|------|------------------|------------------|------|
| MELP帧大小 | 11字节 | 11字节 | - |
| 数据包大小 | 11字节 | 56字节 | - |
| 传输频率 | 每67.5ms | 每337.5ms | 5倍减少 |
| 网络开销 | 高 | 低 | 显著降低 |

### 音频质量
- **采样率**: 8kHz（不变）
- **比特率**: 1.2kbps（不变）
- **帧长度**: 67.5ms（不变）
- **延迟**: 增加约270ms（可接受范围内）

## 兼容性设计

### 向后兼容
- 接收端自动检测数据包格式
- 同时支持单帧和多帧数据包
- 不影响现有的单帧发送逻辑

### 检测机制
```dart
// 多帧数据包特征：
// 1. 第一个字节是帧数量（1-5）
// 2. 数据长度 = 1 + 帧数量 * 11
// 3. 排除长度正好为11字节的单帧数据
```

## 测试验证

### 单元测试
**文件**: `test/multi_frame_voice_assembler_test.dart`

**测试覆盖**:
- ✅ 单帧打包/解包
- ✅ 多帧打包/解包
- ✅ 空数据处理
- ✅ 错误数据处理
- ✅ PCM帧合并
- ✅ 常量验证

**测试结果**: 所有9个测试用例通过

## 使用示例

### 发送端
```dart
// 自动累积5帧后发送
final processor = await VoiceMessageProcessor.create();
await processor.startStreamingRecording();
// 内部自动处理多帧打包
```

### 接收端
```dart
// 自动检测并处理多帧数据包
final player = await VoicePlayer.create();
await player.playMultiFramePacket(packedData);
```

## 性能优化

### 网络传输
- 传输频率降低5倍
- 减少协议开销
- 提高带宽利用率

### 音频处理
- 批量处理提高效率
- 减少系统调用次数
- 优化内存使用

## 注意事项

### 延迟增加
- 发送端需累积5帧（约337.5ms）
- 对实时性要求极高的场景需权衡

### 错误恢复
- 单个数据包丢失影响5帧音频
- 建议配合重传机制使用

### 内存使用
- 发送端需缓存更多帧数据
- 接收端需处理更大的数据包

## 未来扩展

### 自适应帧数
- 根据网络状况动态调整帧数
- 在延迟和效率间找到平衡

### 压缩优化
- 对多帧数据进行二次压缩
- 进一步减少传输数据量

### 错误纠正
- 添加前向纠错码
- 提高数据包的抗干扰能力
