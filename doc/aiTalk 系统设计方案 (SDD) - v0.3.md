<div style="text-align: center; page-break-after: always; padding: 100px 40px; min-height: 80vh; display: flex; flex-direction: column; justify-content: center;">


# aiTalk

## 系统设计方案

<br/><br/>

**版本：** V0.2  

</div>

<div style="page-break-after: always;">


## 文档信息

| 项目     | 内容          |
| -------- | ------------- |
| 文档版本 | V0.1          |
| 创建日期 | 2025年7月5日 |
| 撰写人   |             |
| 审核人   | 何总          |
| 批准人   | 待定          |
| 文档状态 | 初稿          |

## 修订记录

| 版本 | 日期          | 修订内容                               | 撰写人 |
| ---- | ------------- | -------------------------------------- | ------ |
| V0.3 | 2025年7月23日 | 补充应用协议，添加低功耗机制设计          | 李子豪 |
| V0.2 | 2025年7月14日 | 补充应用协议                           | 李子豪 |
| V0.1 | 2025年7月5日 | 初版创建                               | 胡亨卓 |

</div>

<div style="page-break-after: always;">

## 目录

1. [引言](#1-引言-introduction)
   1. [文档目的](#11-文档目的)
   2. [文档范围](#12-文档范围)
   3. [目标读者](#13-目标读者)
   4. [参考文档](#14-参考文档)
2. [系统概述](#2-系统概述-system-overview)
   1. [产品构成](#21-产品构成)
   2. [核心系统能力](#22-核心系统能力)
   3. [系统架构图](#23-系统架构图-high-level-system-architecture)
3. [功能定义与模块划分](#3-功能定义与模块划分-functional-definition--module-division)
   1. [aiTalk 设备模块划分](#31-aitalk-设备模块划分-hardware--firmware)
      1. [硬件模块](#311-硬件模块)
      2. [固件模块](#312-固件模块-运行在蓝牙芯片内置-mcu-上)
   2. [aiTalk App 模块划分](#32-aitalk-app-模块划分-flutter-cross-platform)
      1. [表现层](#321-表现层-uiux-layer---flutter)
      2. [业务逻辑层](#322-业务逻辑层-business-logic-layer---flutter)
         1. [设备连接管理](#3221-设备连接管理)
         2. [群组管理逻辑](#3222-群组管理逻辑)
         3. [消息处理逻辑](#3223-消息处理逻辑)
         4. [语音处理逻辑](#3224-语音处理逻辑)
         5. [数据缓存逻辑](#3225-数据缓存逻辑)
         6. [离线地图逻辑](#3226-离线地图逻辑)
         7. [TurMass 网络协议栈](#3227-turmass-网络协议栈)
      3. [数据管理层](#323-数据管理层-data-management-layer---flutter--native)
         1. [本地数据库模块](#3231-本地数据库模块)
         2. [文件存储模块](#3232-文件存储模块)
         3. [消息缓存队列](#3233-消息缓存队列)
         4. [语音数据缓存](#3234-语音数据缓存)
         5. [网络状态缓存](#3235-网络状态缓存)
         6. [射频数据缓存](#3236-射频数据缓存)
   4. [设备通信层](#324-设备通信层-device-communication-layer---flutter--native)
   5. [离线地图模块](#325-离线地图模块-offline-map-module---flutter--native)
4. [接口设计](#4-接口设计-interface-design)
   1. [aiTalk 应用协议 (App端实现)](#41-aitalk-应用协议-app端实现)
      1. [应用协议概述](#411-应用协议概述)
      2. [业务功能实现](#412-业务功能实现)
      3. [应用通信协议详细规范](#413-应用通信协议详细规范)
         1. [App 协议帧总格式](#4131-app-协议帧总格式)
         2. [TK8620 射频通信协议](#4132-tk8620-射频通信协议)
         3. [帧载荷定义 (FPayload)](#4133-帧载荷定义-fpayload)
         4. [蓝牙设备控制协议](#4134-蓝牙设备控制协议)
         5. [SessionID 生成规则](#4135-sessionid-生成规则)
   2. [BLE 透传接口](#42-ble-透传接口-app与蓝牙芯片)
      1. [BLE 连接管理](#421-ble-连接管理)
      2. [数据透传机制](#422-数据透传机制)
      3. [BLE 接口规范](#423-ble-接口规范)
   3. [UART 透传接口](#43-uart-透传接口-蓝牙芯片与tk8620)
      1. [UART 配置参数](#431-uart-配置参数)
      2. [数据透传流程](#432-数据透传流程)
      3. [错误处理机制](#433-错误处理机制)
   4. [AT 指令接口](#44-at-指令接口-app与tk8620控制交互)
      1. [AT 指令概述](#441-at-指令概述)
      2. [指令分类](#442-指令分类)
   5. [数据转换与转义协议](#45-数据转换与转义协议-data-conversion--escape-protocol)
      1. [协议概述](#451-协议概述)
      2. [数据格式转换](#452-数据格式转换)
      3. [转义协议设计](#453-转义协议设计)
      4. [AT+SENDB指令处理流程](#454-atsendb指令处理流程)
      5. [转义协议示例](#455-转义协议示例)
   6. [设备内部接口](#46-设备内部接口)
      1. [MCU 与电源管理接口](#461-mcu-与电源管理接口)
      2. [MCU 与用户接口模块](#462-mcu-与用户接口模块)
   7. [协议扩展性设计](#47-协议扩展性设计)
      1. [协议版本管理](#471-协议版本管理)
      2. [功能模块化设计](#472-功能模块化设计)
      3. [前向兼容性保证](#473-前向兼容性保证)
5. [数据设计](#5-数据设计-data-design)
   1. [本地数据存储](#51-本地数据存储-app-端)
   2. [数据同步](#52-数据同步-仅与设备进行)
   3. [核心数据模型](#53-核心数据模型-app-端示例)
6. [工作模式](#6-工作模式-working-modes)
   1. [设备配对](#61-设备配对-device-pairing)
      1. [配对流程概述](#611-配对流程概述)
      2. [配对操作步骤](#612-配对操作步骤)
      3. [配对安全机制](#613-配对安全机制)
      4. [配对状态管理](#614-配对状态管理)
      5. [配对故障处理](#615-配对故障处理)
      6. [配对时序图](#616-配对时序图)
   2. [建群模式](#62-建群模式-group-creation-mode)
      1. [群组类型](#621-群组类型)
      2. [建群方式](#622-建群方式)
      3. [建群时序图](#623-建群时序图)
   3. [PTT模式](#63-ptt模式-ptt-mode)
      1. [通信机制](#631-通信机制)
      2. [工作流程](#632-工作流程)
      3. [PTT模式时序图](#633-ptt模式时序图)
   4. [实时对讲模式](#64-实时对讲模式-real-time-voice-mode)
      1. [通信机制](#641-通信机制)
      2. [工作流程](#642-工作流程)
      3. [实时对讲模式时序图](#643-实时对讲模式时序图)
7. [工作机制](#7-工作机制-working-mechanisms)
   1. [自动通信速率调整机制](#71-自动通信速率调整机制)
   2. [语音丢包处理机制](#72-语音丢包处理机制)
   3. [低功耗管理机制](#73-低功耗管理机制)
8. [系统状态机设计](#8-系统状态机设计-system-state-machine-design)
   1. [设备状态机](#81-设备状态机)
   2. [App 状态机](#82-app-状态机)
9. [错误处理与异常恢复](#9-错误处理与异常恢复-error-handling--recovery)
    1. [错误分类与处理策略](#91-错误分类与处理策略)
    2. [异常恢复流程](#92-异常恢复流程)
10. [非功能性考虑](#10-非功能性考虑-non-functional-considerations)
    1. [性能](#101-性能)
    2. [可靠性](#102-可靠性)
    3. [安全性](#103-安全性)
    4. [可扩展性](#104-可扩展性)
    5. [全球合规性](#105-全球合规性)
11. [风险与挑战](#11-风险与挑战-risks--challenges)
12. [总结与展望](#12-总结与展望-summary--outlook)
    1. [系统设计总结](#121-系统设计总结)
    2. [技术创新点](#122-技术创新点)
    3. [未来发展方向](#123-未来发展方向)
    4. [市场前景](#124-市场前景)
13. [附录](#13-附录-appendix)
    1. [术语表](#131-术语表-glossary)
    2. [协议状态码定义](#132-协议状态码定义)
    3. [参考资料](#133-参考资料-references)
    4. [接口规范汇总](#134-接口规范汇总)
       1. [BLE GATT 服务 UUID](#1341-ble-gatt-服务-uuid)
       2. [TurMass SubG 信道配置](#1342-turmass-subg-信道配置)
       3. [性能基准参数](#1343-性能基准参数)

</div>

## 1. 引言 (Introduction)

### 1.1. 文档目的

- 本文档旨在提供 aiTalk 产品的系统级功能定义、模块划分、工作机制及关键工作流程的详细描述。它将作为所有产品设计、硬件开发、固件开发和 App 开发团队的统一参考，确保各部分协同工作，实现产品核心价值。

### 1.2. 文档范围

- 本文档涵盖 aiTalk 设备的硬件（蓝牙芯片内置 MCU、TurMass 芯片、电源）、固件（BLE 协议栈、TurMass SubG 协议栈、应用逻辑）以及 aiTalk App（基于 Flutter 的跨平台应用）的整体系统设计。它将详细说明各组件之间的接口和交互，以及数据在整个系统中的流转方式。
- **不包含** 具体模块的代码实现细节、详细电路图或 UI 界面设计稿，这些将在各自的详细设计文档中体现。

### 1.3. 目标读者

- 产品经理、系统架构师、硬件工程师、嵌入式固件工程师、App 开发工程师 (Flutter)、测试工程师。

### 1.4. 参考文档

- aiTalk 产品需求文档 (PRD) [aiTalk 产品需求文档 - v0.5]
- aiTalk App 设计方案 (ADD) [aiTalk App 设计方案 - v0.1]
- TurMass SubG 技术白皮书
- 蓝牙 BLE 核心规范

## 2. 系统概述 (System Overview)

### 2.1. 产品构成

aiTalk 系统由以下核心组件构成：

- **aiTalk 设备 (型号：ATK-100CN):** 电池供电的硬件附件，包含 TurMass SubG 无线芯片、蓝牙芯片（内置 MCU）、按键、双色指示灯和内置天线。负责 TurMass SubG 射频通信，作为 App 的射频收发器。
- **aiTalk App:** 运行在手机（iOS/Android）上的跨平台应用程序，通过蓝牙 BLE 与 aiTalk 设备连接。负责实现完整的 TurMass 网络协议栈、语音处理、PTT 逻辑、数据缓存，以及提供用户界面进行通信管理、设备控制、消息交互和离线地图功能。

### 2.2. 核心系统能力

- **无网长距离通信:** 核心基于独创的 TurMass SubG 技术，允许用户在无蜂窝网络覆盖区域进行长距离通信。
- **多媒体消息交互:** 支持语音 (PTT/实时通话)、文字消息。
- **灵活的组网模式:** 支持点对点、多人群组以及 TurMass Mesh 自组织网络。
- **端到端本地通信:** 所有通信数据直接在设备和 App 之间传输，**无需云服务器**。
- **跨平台用户体验:** App 基于 Flutter 开发，在 iOS 和 Android 提供一致的用户体验。
- **低功耗设计:** 硬件和固件深度优化，确保设备长时间续航。

### 2.3. 系统架构图 (High-Level System Architecture)

```mermaid
graph LR
    subgraph "Mobile Device"
        App["📱 aiTalk App (Flutter)"]
        BLE_Native["BLE Native Module"]
        OfflineMap_Native["Offline Map Native SDK"]
        App -- "Platform Channels" --> BLE_Native
        App -- "Platform Channels" --> OfflineMap_Native
    end

    subgraph "aiTalk Device (ATK-100CN)"
        BLE_Chip["Bluetooth Chip (with MCU)"]
        TurMass_Chip["TurMass SubG Chip"]
        Power_Management["Power Management Unit"]
        User_Interface["Buttons & LEDs"]
        Antenna["Built-in Antenna"]

        BLE_Chip -- "Control & Data" --> TurMass_Chip
        BLE_Chip -- "Power" --> Power_Management
        BLE_Chip -- "Control" --> User_Interface
        TurMass_Chip -- "RF" --> Antenna
        BLE_Chip -- "RF" --> Antenna
    end

    App_Group_A["📱 aiTalk App (Group A)"]
    Device_Group_A["🔌 aiTalk Device (Group A)"]
    App_Group_B["📱 aiTalk App (Group B)"]
    Device_Group_B["🔌 aiTalk Device (Group B)"]
    
    App -- "BLE Link" --> BLE_Chip
    BLE_Chip -- "TurMass Link" --> TurMass_Chip
    
    TurMass_Chip -- "TurMass SubG Network (Mesh)" --> Device_Group_A
    TurMass_Chip -- "TurMass SubG Network (Mesh)" --> Device_Group_B
    
    Device_Group_A -- "BLE Link" --> App_Group_A
    Device_Group_B -- "BLE Link" --> App_Group_B

    style App fill:#f9f,stroke:#333,stroke-width:2px
    style BLE_Native fill:#f9f,stroke:#333,stroke-width:2px
    style OfflineMap_Native fill:#f9f,stroke:#333,stroke-width:2px
    style BLE_Chip fill:#ccf,stroke:#333,stroke-width:2px
    style TurMass_Chip fill:#ccf,stroke:#333,stroke-width:2px
    style Power_Management fill:#ccf,stroke:#333,stroke-width:2px
    style User_Interface fill:#ccf,stroke:#333,stroke-width:2px
    style Antenna fill:#ccf,stroke:#333,stroke-width:2px
    style App_Group_A fill:#f9f,stroke:#333,stroke-width:2px
    style Device_Group_A fill:#ccf,stroke:#333,stroke-width:2px
    style App_Group_B fill:#f9f,stroke:#333,stroke-width:2px
    style Device_Group_B fill:#ccf,stroke:#333,stroke-width:2px
```

**[ aiTalk 系统架构图 ]**

- **描述:** 该图展示 aiTalk 设备和 aiTalk App 如何协同工作，以及它们之间的主要通信链路。
  - **aiTalk App (手机端):** 包含 UI/UX 层、业务逻辑层、数据管理层，并通过 Flutter Platform Channels 调用原生 BLE 模块和离线地图 SDK。
  - **蓝牙 BLE 通信链路:** App 通过手机的原生 BLE 模块与 aiTalk 设备上的蓝牙芯片进行短距离连接，负责射频数据传输、设备状态同步及设备配置指令的下发。
  - **aiTalk 设备 (ATK-100CN):** 核心由蓝牙芯片（内置 MCU）和 TurMass SubG 芯片组成，并集成电源管理、用户接口和内置天线。
  - **TurMass SubG 无线通信链路:** aiTalk 设备之间通过 TurMass SubG 芯片直接进行长距离无线通信，形成 Mesh 网络，承载射频数据的透传。所有业务层处理由 App 端完成。
  - **无云端服务:** 明确系统不依赖任何外部云服务器。

## 3. 功能定义与模块划分 (Functional Definition & Module Division)

### 3.1. aiTalk 设备模块划分 (Hardware & Firmware)

#### 3.1.1. 硬件模块

- **蓝牙芯片 (内置 MCU):**
  - **功能:** 负责设备整体运行逻辑、任务调度、外设控制。
  - **组成:** 集成微控制器单元 (MCU) 和蓝牙 BLE 模块。
  - **接口:** 与 TurMass 芯片、电源管理单元、用户接口模块通信。
- **TurMass SubG 芯片:**
  - **功能:** 实现 TurMass SubG 无线电信号的发送和接收，以及 TurMass 协议的核心处理。
  - **组成:** 专用 SubG 射频收发器和 TurMass 协议栈硬件加速器。
  - **接口:** 与蓝牙芯片（MCU）进行数据和控制信号交换。
- **电源管理单元 (PMU):**
  - **功能:** 负责电池充电、放电管理、电量检测、低功耗模式控制。
  - **接口:** 与蓝牙芯片（MCU）通信，提供电量状态。
- **用户接口模块:**
  - **功能:** 处理用户输入（开关/配对按钮、PTT 按钮）和提供视觉反馈（双色指示灯）。
  - **接口:** 与蓝牙芯片（MCU）连接。
- **内置天线:**
  - **功能:** 为蓝牙和 TurMass SubG 提供射频收发能力。
  - **接口:** 连接蓝牙芯片和 TurMass 芯片。

#### 3.1.2. 固件模块 (运行在蓝牙芯片内置 MCU 上)

- **操作系统层 (RTOS/Bare-metal):** 设备固件运行的基础环境，负责任务调度和资源管理。
- **蓝牙 BLE 协议栈:**
  - **功能:** 实现 BLE GATT 服务和特征，处理与 App 的连接、数据收发。
  - **接口:** 对外暴露 GATT 接口，对内与设备应用逻辑模块交互。
- **TurMass SubG 协议栈驱动层:**
  - **功能:** 负责与 TurMass SubG 芯片的底层通信和数据交换。
  - **接口:** 对外暴露 TurMass 应用层接口，对内与 TurMass 芯片通信。
- **电源管理模块:**
  - **功能:** 监控电池状态，控制设备进入/退出低功耗模式，管理充电状态。
  - **接口:** 与 PMU 硬件交互，并向 App 报告电量。
- **外设驱动模块:**
  - **功能:** 控制双色指示灯的显示模式，读取开关/配对按钮和 PTT 按钮的状态。
  - **接口:** 与用户接口硬件交互。
- **设备应用逻辑模块:**
  - **功能:** 协调 BLE 和 TurMass 物理层，提供纯粹的射频通信能力和固件 OTA 升级逻辑。所有语音处理、PTT 逻辑、数据缓存和网络协议栈都由 App 实现。
  - **接口:** 接收来自 App 通过 BLE 的射频数据指令，通过 TurMass 物理层进行射频发送，并将接收到的射频数据通过 BLE 返回给 App。
- **OTA 升级模块:**
  - **功能:** 负责接收、校验和烧录新固件，并支持更新失败回滚。
  - **接口:** 与 BLE 协议栈交互接收固件包，与 MCU 内部 Flash 交互。

### 3.2. aiTalk App 模块划分 (Flutter Cross-Platform)

#### 3.2.1. 表现层 (UI/UX Layer - Flutter)

- **功能:** 负责所有用户界面的渲染、用户交互事件处理。
- **组成:** Flutter Widgets，公共组件库，页面管理（路由），动画与交互。

#### 3.2.2. 业务逻辑层 (Business Logic Layer - Flutter)

- **功能:** 实现 App 的核心业务逻辑，处理用户操作，协调各数据源。
- **组成:**

##### 3.2.2.1. 设备连接管理
管理 BLE 连接状态、设备列表刷新、配对流程。

##### 3.2.2.2. 群组管理逻辑
处理群组创建、加入、成员管理，并实现 TurMass Mesh 网络协议栈进行群组数据同步。

##### 3.2.2.3. 消息处理逻辑
负责消息的发送、接收、解析、显示，包括文字消息的处理和消息缓存管理。

##### 3.2.2.4. 语音处理逻辑
负责语音录制、编解码、播放，PTT 语音会话管理，实时语音通话的建立与维护。

##### 3.2.2.5. 数据缓存逻辑
管理离线消息缓存、语音数据缓存、网络状态缓存等所有本地数据缓存。

##### 3.2.2.6. 离线地图逻辑
管理地图下载、加载、显示和数据缓存。

##### 3.2.2.7. TurMass 网络协议栈
实现 TurMass Mesh 网络的所有上层协议，包括路由发现、数据转发、网络拓扑管理、安全加密等功能。

#### 3.2.3. 数据管理层 (Data Management Layer - Flutter & Native)

- **功能:** 负责 App 数据的本地存储、消息缓存、语音缓存、网络状态缓存等所有数据缓存功能。
- **组成:**

##### 3.2.3.1. 本地数据库模块
存储聊天记录、群组信息、设备信息、用户设置、离线地图元数据。
- **Flutter 侧:** 使用 Sqflite 或 Hive 等纯 Dart 数据库。
- **原生侧 (可选):** 如果需要高性能或复杂查询，可通过 Flutter Platform Channels 调用原生数据库（如 iOS Core Data/Realm, Android Room/Realm）。

##### 3.2.3.2. 文件存储模块
存储语音文件、离线地图瓦片数据。

##### 3.2.3.3. 消息缓存队列
存储待发送或待同步的离线消息。

##### 3.2.3.4. 语音数据缓存
存储 PTT 语音数据、实时通话数据、语音编解码缓存。

##### 3.2.3.5. 网络状态缓存
存储 TurMass 网络拓扑、路由表、节点信息等网络状态数据。

##### 3.2.3.6. 射频数据缓存
缓存待发送的射频数据，处理 BLE 传输中断和重传。

#### 3.2.4. 设备通信层 (Device Communication Layer - Flutter & Native)

- **功能:** 负责 App 与 aiTalk 设备之间的蓝牙 BLE 通信。

- **组成:**

  - **Flutter BLE 插件:** 如 `flutter_blue_plus` 或 `flutter_reactive_ble`，提供高层 BLE API。
  - **原生 BLE 模块 (iOS/Android):** 通过 Flutter Platform Channels 与系统 Core Bluetooth (iOS) / Android Bluetooth API (Android) 进行通信，处理 BLE 设备发现、连接、GATT 操作，并监听设备通知。
  - **数据协议解析/封装:** 负责将应用层数据封装为 BLE 可传输的数据包，并解析接收到的数据。
  - **TurMass 协议栈通信接口:** 将 TurMass 网络协议栈生成的射频数据通过 BLE 发送给设备，并接收设备返回的射频数据进行协议栈处理。

- **接口定义:**
  - **BLE 连接管理:** 提供设备扫描、连接、断开、重连等功能
  - **数据传输管理:** 处理大数据包的分片传输和重组
  - **状态监控:** 监控 BLE 连接状态、信号强度等信息
  - **错误处理:** 处理连接异常、数据传输错误等情况

#### 3.2.5. 离线地图模块 (Offline Map Module - Flutter & Native)

- **功能:** 提供地图渲染和离线地图管理。
- **组成:**
  - **地图渲染引擎:** Flutter 侧地图 Widgets (如 `flutter_map` )。
  - **离线地图 SDK 集成:** 通过 Flutter Platform Channels 调用 iOS/Android 原生离线地图 SDK (如 Mapbox Mobile SDK 或高德地图 SDK 的离线地图部分)，进行地图瓦片下载、缓存和渲染管理。
  - **位置传感器接口:** 获取手机 GPS/网络定位数据。

## 4. 接口设计 (Interface Design)

### 4.1. aiTalk 应用协议 (App端实现)

#### 4.1.1. 应用协议概述

aiTalk 应用协议是在 App 端完整实现的业务协议栈，负责处理所有的业务逻辑和用户功能。该协议运行在 App 层，通过透明的通信链路与 TK8620 芯片进行交互。

**协议架构概述:**

aiTalk 应用协议采用分层架构设计，从上到下依次为：

**App 端协议栈层次:**
- **业务应用层**: 建群管理、入群流程、会话控制
- **数据处理层**: 消息收发、语音编解码、文件传输  
- **会话管理层**: 用户认证、权限控制、状态同步
- **网络协议层**: 路由算法、组网逻辑、拓扑发现

**透传通信链路:**
- **BLE 透传接口**: App 与蓝牙芯片的数据通道
- **UART 透传接口**: 蓝牙芯片与TK8620的串口通道
- **AT 指令接口**: App 与TK8620的控制交互

**TK8620 射频芯片:**
- **AT 指令解析**: 解析来自App的控制指令
- **基带处理**: 射频信号的数字信号处理
- **射频收发器**: 最终的射频信号发射和接收

**数据流向**: `App协议栈 → BLE透传 → 蓝牙芯片 → UART透传 → TK8620 → 射频收发`

#### 4.1.2. 业务功能实现

**核心业务功能 (全部在 App 端实现):**

1. **建群功能**
   - 群组创建逻辑
   - 成员邀请机制
   - 群组权限管理
   - 群组信息同步

2. **入群功能**
   - 群组发现机制
   - 入群申请处理
   - 身份验证流程
   - 成员状态管理

3. **数据收发**
   - 消息可靠传输
   - 数据包分片重组
   - 消息确认机制
   - 消息排序处理

4. **会话管理**
   - 会话建立与维护
   - 会话状态同步
   - 用户在线状态
   - 会话安全控制

5. **语音通信**
   - PTT 对讲控制
   - 实时语音编解码
   - 语音数据压缩
   - 语音质量控制

**数据流向说明:**
- **上行**: App协议栈 → BLE透传 → 蓝牙芯片透传 → UART透传 → TK8620 → 射频发射
- **下行**: 射频接收 → TK8620 → UART透传 → 蓝牙芯片透传 → BLE透传 → App协议栈

#### 4.1.3. 应用通信协议详细规范

本节详细描述了 aiTalk App 与设备之间的应用层通信协议规范。

##### 4.1.3.1. App 协议帧总格式

**PHY Payload 结构:**

| 字段 | 长度 | 说明 |
|------|------|------|
| FHDR | 4-13 Bytes | 帧头部 |
| FPayload | 7-2076 Bytes | 帧载荷 |

**根据通信对象的不同，PHY Payload 内容分为:**

1. **面向 TK8620 的射频通信:**
   - PHY Payload = AT指令 + 射频数据(FHDR + FPayload)
   - 蓝牙芯片接收后直接通过UART转发给TK8620执行

2. **面向蓝牙芯片的设备控制:**
   - PHY Payload = FHDR + FPayload
   - 蓝牙芯片解析并执行配置、控制操作

##### 4.1.3.2. TK8620 射频通信协议

**帧头结构 (FHDR):**

| 字节偏移 | 字段名称 | 长度 | 说明 |
|----------|----------|------|------|
| 0 | FrameCtrl | 1 | 帧控制字段 |
| 1-2 | FrameCnt | 0/2 | 帧序号 (非语音帧时) |
| 3-3/3-6 | SrcID | 1/4 | 源设备地址（一般1字节，会话加入请求/响应时4字节） |
| 4-7 | DstID | 0/4 | 群组ID (非语音帧时) |
| 8 | SubPkgNum | 0/1 | 分包数量 (非语音帧时) |
| 9 | SubPkgNo | 0/1 | 分包序号 (非语音帧时) |

**帧控制字段 (FrameCtrl) 详细定义:**

| 位域 | 字段名称 | 长度 | 取值 | 说明 |
|------|----------|------|------|------|
| 7:6 | RFU | 2 | - | 保留字段 |
| 5:4 | Version | 2 | 00 | 协议版本 aiTalk_1.0 |
| 3:2 | FrameType | 2 | 00: 会话帧<br>01: 命令帧<br>10: 数据帧<br>11: 语音帧 | 帧类型 |
| 1:0 | CommunicationMode | 2 | 00: 数据通信<br>01: PTT<br>10: 实时对讲 | 通信模式 |

**语音帧优化:**
- 当 FrameType = 11 (语音帧) 时，为适应低速率模式传输，帧头简化为:
  - FrameCtrl (1字节) + SrcID (1字节) + SubPkgNo (1字节) = 3字节

**SrcID长度特殊说明:**
- 一般情况下，SrcID为1字节长度
- 对于会话帧中的"加入会话请求"和"加入会话响应"，SrcID使用4字节长度（大端序）
- 这种设计保证了会话建立过程中设备地址的唯一性和可识别性

##### 4.1.3.3. 帧载荷定义 (FPayload)

**1. 会话消息 (Session Msg)**

| 控制码 | 名称 | 说明 |
|--------|------|------|
| 0000 | 加入会话请求 | 建立私聊、入群、PTT通道请求 |
| 0001 | 加入会话响应 | 对会话请求的响应 |
| 0010 | 建立通话请求 | 建立实时对讲通道请求 |
| 0011 | 建立通话响应 | 对建立实时对讲通道请求的响应 |
| 0100 | 加入会话通知 | 通知群组内其他成员有新成员加入 |

**会话请求载荷 (Join Request):**

| 字段 | 长度 | 说明 |
|------|------|------|
| SessionPassWd | 4 | 会话密码 |
| UserName | 32 | 用户名称 (UTF-8编码) |

**注意事项:**
- 会话帧SrcID字段使用4字节编码（大端序）
- 广播地址使用0xFFFFFFFF（4字节全F）
- 这确保了会话建立过程中的设备唯一标识

**会话响应载荷 (Join Response):**

| 字段 | 长度 | 说明 |
|------|------|------|
| ResponseCode | 1 | 响应码，0 表示成功，其它表示失败原因 |
| MemberId | 1 | 群内成员 ID，按加入顺序自增，0 表示群主 |
| SessionID | 4 | 会话 ID，由群主当前 DeviceID 生成 |
| GroupName | 24 | 群名称，UTF-8 编码，不足 24 字节时右侧填 0x00 |
| Members | 可变 | 群成员列表，数量取决于当前已入群成员数 |

成员列表子结构（重复出现 *N* 次）：

| 子字段 | 长度 | 说明 |
|--------|------|------|
| MemberId | 1 | 群内成员 ID，与上表 MemberId 规则一致 |
| DeviceID | 4 | 对应成员的 DeviceID (4 字节，大端) |

**建立实时对讲通道请求载荷 (Create Talk Request):**

| 字段 | 长度 | 说明 |
|------|------|------|
| SessionID | 4 | 群组会话 ID |

**建立实时对讲通道响应载荷 (Create Talk Response):**

| 字段 | 长度 | 说明 |
|------|------|------|
| ResponseCode | 1 | 响应码，0 表示成功，其它表示失败原因 |
| SessionID | 4 | 群组会话 ID |

**加入会话通知载荷 (Join Notification):**

| 字段 | 长度 | 说明 |
|------|------|------|
| MemberId | 1 | 新加入成员的群内成员 ID |

**注意事项:**
- 加入会话通知帧的SrcID字段使用4字节编码（大端序），值为新加入成员的DeviceID
- 加入会话通知帧的DstID字段为群组ID（SessionID）
- 此通知在新成员成功加入群组后发送，用于同步群组成员信息
- DeviceID通过帧头的SrcID传递，SessionID通过帧头的DstID传递，载荷中只需携带MemberID

**2. 数据消息 (Data Msg)**

| 类型码 | 数据类型 | 说明 |
|--------|----------|------|
| 0000 | 文本数据 | 纯文本消息 |
| 0001 | 图像数据 | 图片消息 |
| 0010 | 语音数据 | 录音消息 |
| 0011 | GPS数据 | 位置信息 |

**语音数据载荷格式:**

| 字段 | 长度 | 说明 |
|------|------|------|
| AudioData | PTT时44字节，实时语音10字节 | 13bit压缩音频数据 |

**语音数据处理流程:**

```c
// 语音数据包结构
typedef struct {
    uint8_t  paddingBytes;      // 填充字节信息
    uint8_t  audioData[0];      // 压缩音频数据
} __attribute__((packed)) AudioPacket;

// 音频数据编码流程
int EncodeAudioPacket(const uint16_t *audio16bit, uint8_t *packet, 
                     int audioLen, bool isLastPacket) {
    AudioPacket *pkt = (AudioPacket *)packet;
    
    // 1. 13bit压缩处理
    int compressedLen = Compress16To13(audio16bit, pkt->audioData, audioLen);
    int paddingBytes = (audioLen * 2) - compressedLen;
    
    // 2. 设置填充字节信息
    pkt->paddingBytes = paddingBytes & AUDIO_PADDING_MASK;
    if (isLastPacket) {
        pkt->paddingBytes |= AUDIO_LAST_PACKET_FLAG;
    }
    
    return sizeof(AudioPacket) + compressedLen;
}
```

**GPS数据载荷格式:**

| 字段 | 长度 | 说明 |
|------|------|------|
| Latitude | 4 | 纬度 |
| Longitude | 4 | 经度 |
| Altitude | 4 | 海拔高度 |

##### 4.1.3.4. 蓝牙设备控制协议

**蓝牙帧头定义 (FHDR):**

| 字段 | 长度 | 取值 | 说明 |
|------|------|------|------|
| FrameHeader | 2 | 0x55AA | 帧头标识 |
| FrameType | 1 | 00: 配置帧<br>01: 命令帧<br>10: 查询帧<br>11: 应答帧 | 帧类型 |
| FrameLen | 1 | 0-255 | 帧载荷长度 |

 - **配置帧类型:**

| 类型码 | 配置项 | 说明 |
|--------|--------|------|
| 0000 | 波特率 | 串口波特率设置 |
| 0001 | 设备名称 | BLE设备名称 |
| 0010 | 发射功率 | 射频发射功率 |

 - **配置参数示例:**

| 配置项 | 参数类型 | 参数范围 |
|--------|---------|--------|
| 波特率 | uint32_t | 任意四字节数据（例如 9600, 19200, 115200, 921600等） |
| 设备名称 | string | 最大32字节 ASCII 字符 |
| 发射功率 | uint8_t | 0: 低功率模式, 1: 高功率模式 |

 - **查询帧类型:**

| 类型码 | 查询项 | 说明 |
|--------|--------|------|
| 0000 | 波特率 | 查询当前波特率设置 |
| 0001 | 设备名称 | 查询当前设备名称 |
| 0010 | 发射功率 | 查询当前发射功率设置 |
| 0011 | 固件版本 | 查询蓝牙芯片固件版本号 |
| 0100 | 硬件版本 | 查询设备硬件版本号 |
| 0101 | 设备电量 | 查询设备当前电量 |

 - **命令帧类型:**

| 类型码 | 命令项 | 说明 |
|--------|--------|------|
| 0000 | 复位BLE | 复位蓝牙模块并重新进入广播状态 |
| 0001 | 断开BLE | 主动断开当前BLE连接 |

 - **应答帧定义:**

| 字段 | 长度 | 说明 |
|------|------|------|
| ResponseCode | 1 | 应答状态码 |
| RequestType | 1 | 原请求类型 (00:配置, 01:命令, 10:查询) |
| RequestCode | 1 | 原请求类型码 |
| PayloadLen | 1 | 应答数据长度 |
| Payload | 0-250 | 应答数据内容 |

 - **应答状态码定义:**

| 状态码 | 名称 | 描述 |
|--------|------|------|
| 0x00 | SUCCESS | 操作成功 |
| 0x01 | INVALID_PARAMETER | 参数无效 |
| 0x02 | NOT_SUPPORTED | 不支持的操作 |
| 0x03 | TIMEOUT | 操作超时 |
| 0x04 | AUTHENTICATION_ERROR | 认证错误 |
| 0xFF | UNKNOWN_ERROR | 未知错误 |

 - **应答参数示例:**

| 应答类型 | 参数类型 | 参数值示例 | 说明 |
|---------|---------|-----------|------|
| 查询波特率应答 | uint32_t | 0x0001C200 | 115200 bps |
| 查询设备名称应答 | string | "aiTalk-A102" | 设备名称字符串 |
| 查询设备电量应答 | uint8_t | 0x4B | 75% 电量 |
| 查询固件版本应答 | 3字节BCD码 | 0x010305 | 固件版本 v1.3.5 |
| 查询硬件版本应答 | 2字节BCD码 | 0x0201 | 硬件版本 v2.1 |
| 配置成功应答 | 无数据 | ResponseCode=0x00 | 配置操作成功 |
| 参数无效应答 | 无数据 | ResponseCode=0x01 | 参数无效错误 |
| 操作超时应答 | 无数据 | ResponseCode=0x04 | 操作超时错误 |

##### 4.1.3.5. SessionID 生成规则

**生成算法:**

```c
// SessionID生成结构
typedef struct {
    uint16_t devicePrefix;      // 设备前缀 (SrcID低16位)
    uint16_t sessionCounter;    // 会话计数器
} __attribute__((packed)) SessionID;

// 生成SessionID
uint32_t GenerateSessionID(uint32_t deviceID) {
    static uint16_t sessionCounter = 0;
    SessionID id;
    
    // 使用设备ID的低16位作为前缀
    id.devicePrefix = deviceID & 0xFFFF;
    
    // 递增会话计数器
    id.sessionCounter = ++sessionCounter;
    
    // 组合成32位SessionID
    return *(uint32_t *)&id;
}
```

### 4.2. BLE 透传接口 (App与蓝牙芯片)

#### 4.2.1. BLE 连接管理

**连接架构:**

```mermaid
graph LR
    subgraph "App 端"
        A1["BLE Client"]
        A2["GATT Profile"]
        A3["数据封装层"]
    end
    
    subgraph "蓝牙芯片"
        B1["BLE Server"]
        B2["GATT Services"]
        B3["数据透传层"]
    end
    
    A1 <--> B1
    A2 <--> B2
    A3 <--> B3
```

**BLE 连接参数:**
- **连接间隔**: 7.5ms - 4s (可协商)
- **连接延迟**: 0 - 499
- **监管超时**: 100ms - 32s
- **MTU 大小**: 20 - 512 字节 (动态协商)

**GATT 服务定义:**

| 服务名称 | UUID | 特征 | 属性 | 说明 |
|----------|------|------|------|------|
| aiTalk Data Service | 6E400001-B5A3-F393-E0A9-E50E24DCCA9E | TX | Write | App向设备发送数据 |
| | | RX | Notify | 设备向App发送数据 |
| | | Control | Write | 控制指令通道 |
| Device Info Service | 180A | Model | Read | 设备型号信息 |
| | | Firmware | Read | 固件版本信息 |
| | | Battery | Read/Notify | 电池电量状态 |

#### 4.2.2. 数据透传机制

**透传原理:**
蓝牙芯片作为纯透传设备，不对数据内容进行任何解析或处理，只负责数据的可靠传输。

**数据包封装格式:**

```c
// BLE 透传数据包结构
typedef struct {
    uint8_t  header;             // 包头标识: 0xAA
    uint8_t  length;             // 数据长度
    uint8_t  sequence;           // 序列号
    uint8_t  data[0];            // 透传数据
    uint8_t  checksum;           // 校验和
} __attribute__((packed)) BLE_TransparentPacket;

// 数据发送函数
int BLE_SendTransparentData(const uint8_t *data, uint16_t length) {
    BLE_TransparentPacket packet;
    packet.header = 0xAA;
    packet.length = length;
    packet.sequence = GetNextSequence();
    
    // 计算校验和
    packet.checksum = CalculateChecksum(data, length);
    
    // 通过BLE GATT发送
    return GATT_WriteCharacteristic(TX_CHAR_UUID, &packet, 
                                   sizeof(packet) + length);
}
```

**流控机制:**
- **窗口大小**: 支持最多8个未确认数据包
- **确认机制**: 使用序列号进行数据包确认
- **重传机制**: 超时500ms自动重传
- **拥塞控制**: 根据确认情况动态调整发送速率

#### 4.2.3. BLE 接口规范

**连接状态管理:**

```c
// BLE连接状态定义
typedef enum {
    BLE_STATE_DISCONNECTED = 0,
    BLE_STATE_SCANNING,
    BLE_STATE_CONNECTING,
    BLE_STATE_CONNECTED,
    BLE_STATE_DISCOVERING,
    BLE_STATE_READY
} BLE_ConnectionState;

// 状态转换处理
int BLE_HandleStateTransition(BLE_ConnectionState newState) {
    switch (newState) {
        case BLE_STATE_CONNECTED:
            // 启动服务发现
            return BLE_DiscoverServices();
            
        case BLE_STATE_READY:
            // 启用通知
            return BLE_EnableNotifications();
            
        case BLE_STATE_DISCONNECTED:
            // 清理资源
            return BLE_CleanupResources();
            
        default:
            return 0;
    }
}
```

**错误处理机制:**

| 错误类型 | 错误码 | 处理策略 |
|----------|--------|----------|
| 连接超时 | 0x01 | 重试连接，最多3次 |
| 数据传输失败 | 0x02 | 重传数据包，指数退避 |
| 服务发现失败 | 0x03 | 重新发现服务 |
| MTU协商失败 | 0x04 | 使用默认MTU(23字节) |
| 授权失败 | 0x05 | 提示用户重新配对 |

### 4.3. UART 透传接口 (蓝牙芯片与TK8620)

#### 4.3.1. UART 配置参数

**串口基本配置:**
- **波特率**: 115200 bps (默认)，支持115200/921600
- **数据位**: 8位
- **停止位**: 1位
- **校验位**: 无校验
- **流控制**: 硬件流控 (RTS/CTS)

**硬件连接:**

| 蓝牙芯片引脚 | 连接方向 | TK8620芯片引脚 | 说明 |
|-------------|----------|---------------|------|
| UART_TX | → | UART_RX | 数据发送 |
| UART_RX | ← | UART_TX | 数据接收 |
| RTS | → | CTS | 发送请求 |
| CTS | ← | RTS | 发送清除 |

**连接示意:**
```
蓝牙芯片                    TK8620芯片
┌─────────┐                ┌─────────┐
│ UART_TX │───────────────→│ UART_RX │
│ UART_RX │←───────────────│ UART_TX │
│   RTS   │───────────────→│   CTS   │
│   CTS   │←───────────────│   RTS   │
└─────────┘                └─────────┘
```

**电气特性:**
- **电压电平**: 3.3V TTL
- **最大传输速率**: 115200 bps
- **信号完整性**: 支持200mm PCB走线长度

#### 4.3.2. 数据透传流程

**透传机制:**
蓝牙芯片对UART数据进行纯透传，不进行任何协议解析。

**数据流程:**

```mermaid
sequenceDiagram
    participant App as App应用协议
    participant BLE as 蓝牙芯片
    participant UART as UART接口
    participant TK8620 as TK8620芯片
    
    App->>BLE: BLE数据包
    BLE->>UART: 透传到UART
    UART->>TK8620: 串口发送
    
    TK8620->>UART: 串口接收
    UART->>BLE: 透传到BLE
    BLE->>App: BLE数据包
```

**缓冲管理:**
- **发送缓冲区**: 1KB环形缓冲区
- **接收缓冲区**: 1KB环形缓冲区
- **溢出处理**: 缓冲区满时丢弃最旧数据
- **优先级**: 支持高优先级数据抢占发送

#### 4.3.3. 错误处理机制

**错误检测:**

```c
// UART错误类型定义
typedef enum {
    UART_ERROR_NONE = 0,
    UART_ERROR_OVERRUN,         // 接收溢出错误
    UART_ERROR_FRAMING,         // 帧错误
    UART_ERROR_PARITY,          // 校验错误
    UART_ERROR_BREAK,           // 中断错误
    UART_ERROR_TIMEOUT          // 超时错误
} UART_ErrorType;

// 错误处理函数
void UART_HandleError(UART_ErrorType error) {
    switch (error) {
        case UART_ERROR_OVERRUN:
            // 清空接收缓冲区
            UART_ClearRxBuffer();
            break;
            
        case UART_ERROR_FRAMING:
            // 重新同步波特率
            UART_ResyncBaudRate();
            break;
            
        case UART_ERROR_TIMEOUT:
            // 发送心跳包检测连接
            UART_SendHeartbeat();
            break;
            
        default:
            // 记录错误日志
            LogError("UART Error: %d", error);
            break;
    }
}
```

**故障恢复:**
- **自动重连**: 检测到通信中断时自动重新建立连接
- **波特率自适应**: 支持自动检测和适配波特率
- **硬件重置**: 严重错误时可触发硬件重置
- **状态监控**: 实时监控UART通信质量

### 4.4. AT 指令接口 (App与TK8620控制交互)

#### 4.4.1. AT 指令概述

AT 指令接口是 App 与 TK8620 芯片之间的控制交互协议。App 通过 BLE 和 UART 透传链路，向 TK8620 发送 AT 指令进行设备控制和状态查询。

**通信架构:**

AT 指令通信采用三层架构设计，从上到下依次为：

**App 端指令处理层:**
- **AT指令生成器**: 根据业务需求生成标准AT指令
- **指令队列管理**: 管理指令发送队列和优先级
- **响应解析器**: 解析TK8620返回的指令响应

**透传通信链路层:**
- **BLE接口**: App与蓝牙芯片的数据通道
- **蓝牙芯片透传**: 对AT指令进行透明转发
- **UART接口**: 蓝牙芯片与TK8620的串口通道

**TK8620指令处理层:**
- **AT指令解析器**: 解析收到的AT指令格式
- **功能模块控制**: 执行具体的设备控制操作
- **状态反馈**: 将执行结果反馈给App端

**指令流向**: `App指令生成 → 队列管理 → BLE透传 → 蓝牙芯片 → UART透传 → TK8620执行 → 状态反馈`

#### 4.4.2. 指令分类

**基础控制指令 :**

- 工作模式设置（AT+WORKMODE）
- 时隙配置（AT+FRAMECFG）
- 速率配置（AT+RATE）
- 频率配置（AT+FREQ）

**数据传输指令:**

- 数据发送（AT+SENDB）
- 数据接收（+ID:）

**网络管理指令 :**

- 网络扫描（AT+SCAN）

### 4.5. 数据转换与转义协议 (Data Conversion & Escape Protocol)

#### 4.5.1. 协议概述

在aiTalk系统中，App与TK8620芯片之间的数据传输需要进行格式转换。App发送的16进制字符串数据需要转换为2进制数据，而TK8620返回的2进制数据需要转换为16进制字符串。由于AT指令以"\r\n"作为结束符，当2进制数据中包含这些字符时，需要进行转义处理以避免指令解析错误。

#### 4.5.2. 数据格式转换

**16进制字符串到2进制数据转换:**

```c
/**
 * @brief 将16进制字符串转换为2进制数据
 * @param hexStr 16进制字符串 (如: "1A2B3C")
 * @param binData 输出的2进制数据缓冲区
 * @param hexLen 16进制字符串长度 (必须为偶数)
 * @return 转换后的2进制数据长度
 */
int HexStringToBinary(const char *hexStr, uint8_t *binData, int hexLen) {
    if (hexLen % 2 != 0) return -1; // 长度必须为偶数
    
    int binLen = hexLen / 2;
    for (int i = 0; i < binLen; i++) {
        char highByte = hexStr[i * 2];
        char lowByte = hexStr[i * 2 + 1];
        
        // 转换高4位
        uint8_t high = (highByte >= 'A') ? (highByte - 'A' + 10) : 
                       (highByte >= 'a') ? (highByte - 'a' + 10) : (highByte - '0');
        // 转换低4位  
        uint8_t low = (lowByte >= 'A') ? (lowByte - 'A' + 10) : 
                      (lowByte >= 'a') ? (lowByte - 'a' + 10) : (lowByte - '0');
        
        binData[i] = (high << 4) | low;
    }
    
    return binLen;
}
```

**2进制数据到16进制字符串转换:**

```c
/**
 * @brief 将2进制数据转换为16进制字符串
 * @param binData 2进制数据
 * @param binLen 2进制数据长度
 * @param hexStr 输出的16进制字符串缓冲区
 * @return 转换后的16进制字符串长度
 */
int BinaryToHexString(const uint8_t *binData, int binLen, char *hexStr) {
    const char hexTable[] = "0123456789ABCDEF";
    
    for (int i = 0; i < binLen; i++) {
        hexStr[i * 2] = hexTable[(binData[i] >> 4) & 0x0F];
        hexStr[i * 2 + 1] = hexTable[binData[i] & 0x0F];
    }
    
    hexStr[binLen * 2] = '\0';
    return binLen * 2;
}
```

#### 4.5.3. 转义协议设计

基于SLIP协议的思想，设计适用于AT指令的转义协议，解决2进制数据中包含"\r\n"字符的问题。

**转义字符定义:**

```c
// 转义协议字符定义
#define ESC_CHAR        0xDB    // 转义字符
#define ESC_CR          0xDC    // \r的转义替换
#define ESC_LF          0xDD    // \n的转义替换
#define ESC_ESC         0xDE    // ESC字符本身的转义替换

// 原始字符定义
#define CHAR_CR         0x0D    // \r字符
#define CHAR_LF         0x0A    // \n字符
```

**数据转义算法:**

```c
/**
 * @brief 对2进制数据进行转义处理
 * @param srcData 源数据
 * @param srcLen 源数据长度
 * @param dstData 转义后的数据缓冲区
 * @param dstMaxLen 目标缓冲区最大长度
 * @return 转义后的数据长度，-1表示缓冲区不足
 */
int EscapeData(const uint8_t *srcData, int srcLen, uint8_t *dstData, int dstMaxLen) {
    int dstLen = 0;
    
    for (int i = 0; i < srcLen; i++) {
        uint8_t byte = srcData[i];
        
        if (byte == CHAR_CR) {
            // 转义 \r 字符
            if (dstLen + 2 > dstMaxLen) return -1;
            dstData[dstLen++] = ESC_CHAR;
            dstData[dstLen++] = ESC_CR;
        } else if (byte == CHAR_LF) {
            // 转义 \n 字符
            if (dstLen + 2 > dstMaxLen) return -1;
            dstData[dstLen++] = ESC_CHAR;
            dstData[dstLen++] = ESC_LF;
        } else if (byte == ESC_CHAR) {
            // 转义 ESC 字符本身
            if (dstLen + 2 > dstMaxLen) return -1;
            dstData[dstLen++] = ESC_CHAR;
            dstData[dstLen++] = ESC_ESC;
        } else {
            // 普通字符直接复制
            if (dstLen + 1 > dstMaxLen) return -1;
            dstData[dstLen++] = byte;
        }
    }
    
    return dstLen;
}
```

**数据反转义算法:**

```c
/**
 * @brief 对转义数据进行反转义处理
 * @param srcData 转义后的数据
 * @param srcLen 转义数据长度
 * @param dstData 反转义后的数据缓冲区
 * @param dstMaxLen 目标缓冲区最大长度
 * @return 反转义后的数据长度，-1表示数据格式错误
 */
int UnescapeData(const uint8_t *srcData, int srcLen, uint8_t *dstData, int dstMaxLen) {
    int dstLen = 0;
    int i = 0;
    
    while (i < srcLen) {
        if (srcData[i] == ESC_CHAR) {
            if (i + 1 >= srcLen) return -1; // 转义序列不完整
            
            uint8_t escByte = srcData[i + 1];
            if (escByte == ESC_CR) {
                // 反转义 \r 字符
                if (dstLen + 1 > dstMaxLen) return -1;
                dstData[dstLen++] = CHAR_CR;
            } else if (escByte == ESC_LF) {
                // 反转义 \n 字符
                if (dstLen + 1 > dstMaxLen) return -1;
                dstData[dstLen++] = CHAR_LF;
            } else if (escByte == ESC_ESC) {
                // 反转义 ESC 字符本身
                if (dstLen + 1 > dstMaxLen) return -1;
                dstData[dstLen++] = ESC_CHAR;
            } else {
                return -1; // 无效的转义序列
            }
            i += 2; // 跳过转义序列
        } else {
            // 普通字符直接复制
            if (dstLen + 1 > dstMaxLen) return -1;
            dstData[dstLen++] = srcData[i];
            i++;
        }
    }
    
    return dstLen;
}
```

#### 4.5.4. AT+SENDB指令处理流程

**发送数据处理流程:**

```c
/**
 * @brief 处理AT+SENDB指令的数据发送
 * @param hexData 16进制字符串数据
 * @param hexLen 16进制字符串长度
 * @return 0成功，其他失败
 */
int ProcessATSendCommand(const char *hexData, int hexLen) {
    uint8_t binData[1024];
    uint8_t escData[2048];
    char atCommand[4096];
    
    // 1. 16进制字符串转2进制数据
    int binLen = HexStringToBinary(hexData, binData, hexLen);
    if (binLen < 0) return -1;
    
    // 2. 对2进制数据进行转义
    int escLen = EscapeData(binData, binLen, escData, sizeof(escData));
    if (escLen < 0) return -2;
    
    // 3. 构造AT指令
    snprintf(atCommand, sizeof(atCommand), "AT+SENDB=");
    memcpy(atCommand + strlen(atCommand), escData, escLen);
    strcat(atCommand, "\r\n");
    
    // 4. 发送AT指令到TK8620
    return SendATCommand(atCommand, strlen(atCommand));
}
```

**接收数据处理流程:**

```c
/**
 * @brief 处理TK8620返回的数据
 * @param response 接收到的原始响应数据
 * @param respLen 响应数据长度
 * @param hexOutput 输出的16进制字符串
 * @return 输出的16进制字符串长度，-1表示处理失败
 */
int ProcessATResponse(const uint8_t *response, int respLen, char *hexOutput) {
    uint8_t binData[1024];
    
    // 1. 查找数据部分（跳过"+ID:"等前缀）
    const uint8_t *dataStart = FindDataStart(response, respLen);
    if (!dataStart) return -1;
    
    // 2. 计算数据长度（排除"\r\n"结尾）
    int dataLen = CalculateDataLength(dataStart, respLen - (dataStart - response));
    if (dataLen < 0) return -2;
    
    // 3. 对数据进行反转义
    int binLen = UnescapeData(dataStart, dataLen, binData, sizeof(binData));
    if (binLen < 0) return -3;
    
    // 4. 2进制数据转16进制字符串
    return BinaryToHexString(binData, binLen, hexOutput);
}
```

#### 4.5.5. 转义协议示例

**示例1：普通数据转换**
```
原始16进制字符串: "1A2B3C"
转换为2进制数据: [0x1A, 0x2B, 0x3C]
无需转义: [0x1A, 0x2B, 0x3C]
AT指令: "AT+SENDB=" + [0x1A, 0x2B, 0x3C] + "\r\n"
```

**示例2：包含\r\n的数据转换**
```
原始16进制字符串: "1A0D0A3C"
转换为2进制数据: [0x1A, 0x0D, 0x0A, 0x3C]
转义后数据: [0x1A, 0xDB, 0xDC, 0xDB, 0xDD, 0x3C]
AT指令: "AT+SENDB=" + [0x1A, 0xDB, 0xDC, 0xDB, 0xDD, 0x3C] + "\r\n"
```

**示例3：包含转义字符的数据转换**
```
原始16进制字符串: "1ADB3C"
转换为2进制数据: [0x1A, 0xDB, 0x3C]
转义后数据: [0x1A, 0xDB, 0xDE, 0x3C]
AT指令: "AT+SENDB=" + [0x1A, 0xDB, 0xDE, 0x3C] + "\r\n"
```

### 4.6. 设备内部接口

#### 4.6.1. MCU 与电源管理接口

**接口功能:**
- 电池电量监测和报告
- 充电状态管理
- 低功耗模式控制
- 电源异常检测

**接口规范:**
- **电量查询:** MCU通过ADC读取电池电压，转换为电量百分比
- **充电检测:** 通过GPIO检测充电器连接状态
- **低功耗控制:** 支持多级低功耗模式切换
- **异常保护:** 过充、过放、过温保护机制

#### 4.6.2. MCU 与用户接口模块

**按键接口:**
- **开关/配对按钮:** GPIO输入，支持短按、长按、双击检测
- **PTT按钮:** GPIO输入，支持按下/释放状态检测
- **按键防抖:** 硬件和软件双重防抖机制

**指示灯接口:**
- **双色LED控制:** 通过PWM控制红/绿LED亮度
- **状态指示模式:** 支持常亮、闪烁、呼吸等多种模式
- **低功耗优化:** 自动调节亮度以节省功耗

### 4.7. 协议扩展性设计

#### 4.7.1. 协议版本管理

**版本标识:**
- 协议版本号采用语义化版本控制（Semantic Versioning）
- 格式：MAJOR.MINOR.PATCH（如：1.0.0）
- 支持向前兼容和向后兼容机制

**版本协商:**
- 设备连接时自动进行版本协商
- 选择双方都支持的最高版本进行通信
- 不兼容版本提供降级或升级提示

#### 4.7.2. 功能模块化设计

**模块划分:**
- 核心通信模块（必需）
- 扩展功能模块（可选）
- 自定义应用模块（开放）

**模块接口:**
- 标准化的模块接口定义
- 插件式架构支持动态加载
- 模块间依赖关系管理

#### 4.7.3. 前向兼容性保证

**协议扩展机制:**
- 保留字段用于未来功能扩展
- TLV（Type-Length-Value）格式支持可变长度字段
- 可选字段向前兼容处理

**数据格式兼容:**
- 支持多种数据编码格式
- 自适应数据压缩算法
- 向前兼容的数据结构设计

## 5. 数据设计 (Data Design)

### 5.1. 本地数据存储 (App 端)

- **消息数据:** 存储聊天消息（文字、语音）的详细内容，支持离线查看。
- **群组数据:** 存储群组信息、成员列表，以及 TurMass SubG 组网的配置信息。
- **设备数据:** 存储已配对 aiTalk 设备信息、配置、历史状态。
- **用户设置:** 存储 App 内各项用户偏好设置。
- **离线地图数据:** 存储下载的地图瓦片数据。

### 5.2. 数据同步 (仅与设备进行)

- **App 与 aiTalk 设备数据同步 (BLE):**
  - 设备状态（电量、连接状态）实时同步。
  - App 配置指令（如设备名称、TurMass 频率、射频参数）下发。
  - App 生成的射频数据传输到设备进行发送。
  - 设备接收到的射频数据传输到 App 进行协议栈处理。
- **aiTalk 设备间数据传输 (TurMass SubG):**
  - 射频数据在设备间直接传输，无需中转服务器。设备不解析业务层内容，仅作为射频透传。
  - 消息确认机制由 App 端协议栈实现，确保传输可靠性。
- **无云端同步:** aiTalk 产品设计不进行任何云端数据同步，所有数据均在本地存储和设备间传输。

### 5.3. 核心数据模型 (App 端示例)

- `User`: `ID` (本地生成), `Nickname`, `Avatar` (本地路径)。
- `Device`: `ID`, `BLE_MAC`, `TurMass_ID` (SubG 网络识别 ID), `FirmwareVersion`, `BatteryLevel`, `ConnectStatus`, `SignalStrength`。
- `Group`: `ID`, `Name`, `CreatorID`, `Members[]`, `InvitationType` (如二维码/广播), `Key` (私有群密钥)。
- `Message`: `ID`, `SenderID`, `GroupID`, `Type` (Text/Voice), `Content`, `Timestamp`, `Status` (Sending/Sent/Failed/Read)。
- `VoiceContent`: `Duration`, `VoiceData` (本地文件路径)。
- `OfflineMap`: `MapRegion`, `DownloadedPath`, `Version`。

## 6. 工作模式 (Working Modes)

### 6.1. 设备配对 (Device Pairing)

设备配对是aiTalk系统建立数据通信的第一步，是App与aiTalk设备之间建立蓝牙连接的关键流程。通过设备配对，用户可以将手机与aiTalk设备进行绑定，为后续的无网通信功能提供基础。

#### 6.1.1. 配对流程概述

设备配对的主要目标是建立手机App与aiTalk设备之间的安全、稳定的蓝牙连接，并完成设备身份识别和初始化配置。整个配对过程需要确保设备的唯一性和通信的可靠性。

#### 6.1.2. 配对操作步骤

设备配对包括以下关键步骤：

**1. 设备准备阶段**
- 用户长按aiTalk设备的开关按钮开机
- 设备启动后，蓝牙模块自动进入广播模式
- 设备开始周期性发送BLE广播信号，包含设备名称和基本信息

**2. App搜索阶段**
- 用户打开aiTalk App，进入设备管理界面
- App启动蓝牙扫描功能，搜索附近的aiTalk设备
- App在搜索界面显示发现的设备列表，包括设备名称、信号强度等信息

**3. 配对连接阶段**
- 用户从设备列表中选择目标aiTalk设备
- App发起BLE连接请求，建立与设备的通信链路
- 完成蓝牙安全配对过程，交换加密密钥

**4. 设备认证阶段**
- 配对成功后，App读取设备的TK8620芯片ID
- 基于芯片ID生成全局唯一的设备标识符
- App将设备信息保存到本地数据库，建立设备档案

**5. 初始化配置阶段**
- App向设备下发初始配置参数
- 配置TurMass SubG通信参数（频率、功率等）
- 更新设备连接状态，完成配对流程

#### 6.1.3. 配对安全机制

为确保配对过程的安全性，系统采用以下安全机制：

**1. 蓝牙安全配对**
- 使用标准BLE安全配对协议
- 支持数字键盘输入认证（PIN码）
- 建立AES-128加密通信链路

**2. 设备身份验证**
- 通过TK8620芯片的唯一ID进行设备识别
- 防止设备身份伪造和重复绑定
- 支持设备绑定状态查询和管理

**3. 通信加密**
- BLE层面的链路加密保护
- 应用层面的数据完整性校验
- 防重放攻击保护机制

#### 6.1.4. 配对状态管理

系统维护详细的设备配对状态信息：

**设备状态类型：**
- **未配对 (Unpaired)**: 设备未与任何App建立连接
- **配对中 (Pairing)**: 正在进行配对流程
- **已配对 (Paired)**: 配对成功，设备已绑定
- **连接中 (Connecting)**: 正在建立BLE连接
- **已连接 (Connected)**: BLE连接已建立，可进行通信
- **断开连接 (Disconnected)**: BLE连接中断，但配对关系保持

**状态转换处理：**
- 支持配对状态的自动检测和恢复
- 提供状态变化的实时通知机制
- 记录状态变化日志，便于问题诊断

#### 6.1.5. 配对故障处理

系统提供完善的配对故障检测和恢复机制：

**常见故障类型：**
- 蓝牙扫描失败或设备不可见
- BLE连接建立失败或超时
- 设备认证失败或信息读取错误
- 配对过程中的异常中断

**故障处理策略：**
- 自动重试机制，支持多次尝试配对
- 提供详细的错误信息和用户指导
- 支持手动重置配对状态
- 记录故障日志，便于技术支持

#### 6.1.6. 配对时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant App as aiTalk App
    participant Device as aiTalk设备
    participant TK8620 as TK8620芯片
    
    Note over User,TK8620: 设备配对流程
    
    rect rgb(240, 248, 255)
        Note over User,TK8620: 1. 设备发现与连接
        User->>Device: 长按开关按钮开机
        Device->>Device: 启动BLE广播
        User->>App: 打开App扫描设备
        App->>Device: 发现设备并建立连接
        Device->>App: 连接成功确认
    end
    
    rect rgb(240, 255, 240)
        Note over User,TK8620: 2. 安全配对
        User->>App: 选择目标设备
        App->>Device: 发起安全配对
        Device->>App: 配对成功
    end
    
    rect rgb(255, 248, 240)
        Note over User,TK8620: 3. 设备初始化
        App->>Device: 读取设备信息
        Device->>TK8620: 获取芯片ID
        TK8620->>App: 返回设备标识
        App->>Device: 配置TurMass参数
        Device->>App: 初始化完成
        App->>User: 配对成功
    end
```

### 6.2. 建群模式 (Group Creation Mode)

建立群组是aiTalk产品实现多用户通信的基本操作，支持多种群组类型和建群方式以满足不同场景下的用户需求，提升通信的灵活性与安全性。

#### 6.2.1. 群组类型

aiTalk App 支持多种类型的群组，以满足不同用户和场景下的通信需求。群组类型主要包括以下几类：

- **公共群组：** 3人头像
  - 公共群组为系统预置的开放型群组，所有用户在App开启时均会内置3个公共群组
  - 公共群组独立运行在不同私有信道，每个群组有唯一标识和名称，通常用于全体用户的公告、紧急通知或大范围交流

- **双人群组：** 单人头像
  - 双人群组为点对点私密通信群组，仅包含当前用户与一名指定联系人
  - 每个双人群组与一对用户唯一对应，支持在联系人界面发起私密聊天

- **多人群组：** 双人头像
  - 多人群组为自定义的多成员通信群组，成员数量大于 2 人

#### 6.2.2. 建群方式

aiTalk App 支持多种建群方式，其操作流程如下所述：

##### 6.2.2.1. 指定用户建群

指定用户入群是一种高安全性的建群方式，群主需手动审核申请加入的用户，确保群组成员的精准性与可控性。其操作流程主要包括以下步骤：

- 群主通过App发起建群操作，在建群界面选择"指定用户"方式发起建群
- 群主设备在公共信道上周期性广播**建立群组消息**
- 用户通过App进入搜索群组界面，解析建立群组消息获取有效群组列表，用户自主选择想要加入的群组，并发送**申请入群消息**
- 群主设备在App界面手动审核申请用户信息，确认是否允许入群，并发送**入群响应消息**
- 审核通过后，申请用户正式加入群组后广播**加入会话通知**，解析其他用户的加入会话通知可实时获取当前入群成员列表
- 群主可随时在App中单击"建群完成"按钮结束建群流程并发送**建群完成通知**
- 收到建群完成通知的设备和群主设备自动切换到私有信道进行通信

##### 6.2.2.2. 有验证码建群

有验证码入群是一种半开放的建群方式，群主设备自动验证用户身份以决定是否允许入群。其操作流程主要包括以下步骤：

- 群主通过App发起建群操作，在建群界面选择"验证码"方式发起建群
- 群主在输入框中设置群密码，随后在公共信道上周期性广播**建立群组消息**
- 周围用户通过App进入搜索群组界面，获取当前有效群组列表，用户自主选择想要加入的群组，并在弹出的输入框中输入事先约定的群密码，发送**申请入群消息**
- 群主设备将自动验证申请入群的用户信息，并校验群密码，发送**入群响应消息**
- 验证通过后，申请用户正式加入群组后广播**加入会话通知**
- 群主可随时在App中单击"建群完成"按钮结束建群流程并发送**建群完成通知**
- 收到建群完成通知的设备和群主设备自动切换到私有信道进行通信

##### 6.2.2.3. 无验证码建群

无验证码入群是一种开放式的建群方式，群主设备自动验证申请用户的基本信息，无需额外验证即可加入群组。其操作流程主要包括以下步骤：

- 群主通过App发起建群操作，在建群界面选择"无验证"方式发起建群，随后在公共信道上周期性广播**建立群组消息**
- 周围用户通过App进入搜索群组界面，获取当前有效群组列表，用户自主选择想要加入的群组，发送**申请入群消息**
- 群主设备将自动验证申请入群的用户信息，返回**入群响应消息**
- 验证通过后，申请用户正式加入群组后广播**加入会话通知**
- 群主可随时在App中单击"建群完成"按钮结束建群流程并发送**建群完成通知**
- 收到建群完成通知的设备和群主设备自动切换到私有信道进行通信

##### 6.2.2.4. 离线建群（扫码入群）

用户扫码入群专为中途想要加入群组用户设置，群主设备根据建群方式验证用户信息，验证通过后该用户即可加入群组。其操作流程主要包括以下步骤：

- 用户通过群员或群主获取群二维码，并使用App扫描该二维码
- 扫描后，用户设备自动获取群配置，并发送申请入群消息
- 根据建群方式不同，群主设备验证用户身份分为以下三种情况：
  - 指定用户建群：群主在App界面手动审核申请用户信息，确认是否允许入群
  - 有验证码建群：用户需在弹出的输入框中输入事先约定的群密码，群主设备自动验证密码有效性
  - 无验证码建群：群主设备自动验证用户身份，并自动允许入群
- 验证通过后，申请用户正式加入群组

#### 6.2.3. 建群时序图

<div style="page-break-before: always;"></div>

**指定用户建群时序图:**

```mermaid
sequenceDiagram
    participant 群主App as 群主App
    participant 群主设备 as 群主设备
    participant 公共信道 as 公共信道
    participant 用户设备 as 用户设备
    participant 用户App as 用户App
    participant 私有信道 as 私有信道
    
    Note over 群主App,用户App: 指定用户建群流程
    
    群主App->>群主设备: 发起建群(指定用户模式)
    群主设备->>公共信道: 周期性广播"建立群组消息"
    
    用户App->>用户设备: 进入搜索群组界面
    用户设备->>公共信道: 监听群组广播
    公共信道->>用户设备: 接收建立群组消息
    用户设备->>用户App: 显示可加入群组列表
    
    用户App->>用户设备: 用户选择群组并申请加入
    用户设备->>公共信道: 发送"申请入群消息"
    公共信道->>群主设备: 转发申请入群消息
    群主设备->>群主App: 通知有用户申请入群
    
    群主App->>群主设备: 手动审核并批准入群
    群主设备->>公共信道: 发送"入群响应消息"
    公共信道->>用户设备: 转发入群响应
    用户设备->>用户App: 显示入群成功
    
    用户设备->>公共信道: 广播"加入会话通知"
    公共信道->>群主设备: 转发加入会话通知
    群主设备->>群主App: 更新群组成员列表
    
    群主App->>群主设备: 点击"建群完成"
    群主设备->>公共信道: 发送"建群完成通知"
    公共信道->>用户设备: 转发建群完成通知
    
    群主设备->>私有信道: 切换到私有信道
    用户设备->>私有信道: 切换到私有信道
    
    Note over 群主App,用户App: 建群完成，开始私有信道通信
```

<div style="page-break-before: always;"></div>

**有验证码建群时序图:**

```mermaid
sequenceDiagram
    participant 群主App as 群主App
    participant 群主设备 as 群主设备
    participant 公共信道 as 公共信道
    participant 用户设备 as 用户设备
    participant 用户App as 用户App
    
    Note over 群主App,用户App: 有验证码建群流程
    
    群主App->>群主设备: 发起建群并设置群密码
    群主设备->>公共信道: 广播"建立群组消息"(含验证码要求)
    
    用户App->>用户设备: 搜索群组
    用户设备->>公共信道: 监听群组广播
    公共信道->>用户设备: 接收群组信息
    用户设备->>用户App: 显示需要验证码的群组
    
    用户App->>用户App: 用户输入群密码
    用户App->>用户设备: 发送申请入群(含密码)
    用户设备->>公共信道: 发送"申请入群消息"
    公共信道->>群主设备: 转发申请
    
    群主设备->>群主设备: 自动验证群密码
    群主设备->>公共信道: 发送"入群响应消息"
    公共信道->>用户设备: 转发响应结果
    
    alt 验证成功
        用户设备->>用户App: 显示入群成功
        用户设备->>公共信道: 广播"加入会话通知"
    else 验证失败
        用户设备->>用户App: 显示密码错误
    end
```

<div style="page-break-before: always;"></div>

**扫码入群时序图:**

```mermaid
sequenceDiagram
    participant 用户App as 用户App
    participant 二维码 as 群二维码
    participant 用户设备 as 用户设备
    participant 群主设备 as 群主设备
    participant 群主App as 群主App
    
    Note over 用户App,群主App: 扫码入群流程
    
    用户App->>二维码: 扫描群二维码
    二维码->>用户App: 返回群配置信息
    用户App->>用户设备: 自动获取群配置
    
    用户设备->>群主设备: 发送申请入群消息
    
    alt 指定用户建群
        群主设备->>群主App: 通知有扫码申请
        群主App->>群主设备: 手动审核决定
    else 有验证码建群
        用户App->>用户App: 弹出密码输入框
        用户App->>用户设备: 提交密码
        群主设备->>群主设备: 自动验证密码
    else 无验证码建群
        群主设备->>群主设备: 自动验证并允许入群
    end
    
    群主设备->>用户设备: 发送入群响应
    用户设备->>用户App: 显示入群结果
    
    alt 入群成功
        用户App->>用户App: 加入群组成功
    else 入群失败
        用户App->>用户App: 显示失败原因
    end
```

### 6.3. PTT模式 (PTT Mode)

PTT模式是一种按键式语音通信模式，用户按下按钮即可发起语音通信，松开按钮后结束通信，该模式适用于点对点或小范围的即时语音交流。

#### 6.3.1. 通信机制

- **半双工通信**
  - 同一时间仅允许一个用户发言，其他用户处于接收状态
  - TK8620处于异步工作模式
  - 发言权通过按键触发，设备会在发言前进行信道占用检测，确保信道空闲后开始语音单播（双人PTT）/广播（多人PTT）

- **单播/广播通信**
  - 所有接收设备监听同一私有信道单播/广播数据，确保语音数据能够被所有当前聊天对象设备接收

- **语音压缩与抗干扰**
  - 支持冲突避免和随机退避机制，避免数据冲突，该功能在App实现
  - 支持指定信道跳频策略，在数据发送前通过握手流程协商跳频信道
  - 语音数据采用 TCodec 编码，支持0.5-50kbps下的高语音清晰度

#### 6.3.2. 工作流程

- **用户操作：发起PTT通信**
  - 用户在对讲页面点击"发起对讲"按钮后，获取当前已加入的有效群组列表
  - 用户点击有效群组项的通话按钮后，在该群私有信道周期性发送"建立会话通知（PTT模式）"广播

- **用户操作：加入PTT通信**
  - 群组内其他成员设备收到通话广播后，App 弹出加入对讲提示，用户可选择"加入通话"
  - 用户确认加入后，发送"加入会话通知"广播，并同步加入信息至群组
  - 加入通话的用户信息（如用户 ID、昵称、头像等）实时同步至所有已加入通话的成员端

- **App操作：语音采集和编码**
  - 发送方手机通过麦克风采集用户语音，并通过TCodec算法对语音数据进行压缩和编码，同时源语音数据支持本地缓存
  - 发送方麦克风按钮按住期间，将持续发送编码后的语音数据
  - 接收方解析收到的语音数据协议帧，使用TCodec解码算法还原语音数据，并通过扬声器播放

- **用户操作：结束PTT通信**
  - 用户可以随时选择退出PTT通信界面，结束本次语音通信

#### 6.3.3. PTT模式时序图

<div style="page-break-before: always;"></div>

**PTT对讲完整流程时序图:**

```mermaid
sequenceDiagram
    participant 发起者App as 发起者App
    participant 发起者设备 as 发起者设备
    participant 私有信道 as 私有信道
    participant 成员设备 as 成员设备
    participant 成员App as 成员App
    participant TK8620_A as TK8620芯片A
    participant TK8620_B as TK8620芯片B
    
    Note over 发起者App,成员App: PTT对讲流程
    
    rect rgb(240, 248, 255)
        Note over 发起者App,成员App: 1. 发起PTT通信
        发起者App->>发起者设备: 点击"发起对讲"按钮
        发起者设备->>私有信道: 周期性发送"建立会话通知(PTT模式)"
        私有信道->>成员设备: 广播PTT会话通知
        成员设备->>成员App: 弹出加入对讲提示
        
        成员App->>成员设备: 用户选择"加入通话"
        成员设备->>私有信道: 发送"加入会话通知"
        私有信道->>发起者设备: 转发加入通知
        发起者设备->>发起者App: 更新成员列表
    end
    
    rect rgb(240, 255, 240)
        Note over 发起者App,成员App: 2. 语音通信过程
        发起者App->>发起者App: 用户按住PTT按钮
        发起者App->>发起者App: 开始语音采集
        发起者App->>发起者App: TCodec编码压缩
        发起者App->>发起者设备: 发送编码语音数据
        发起者设备->>TK8620_A: 通过UART透传语音数据
        TK8620_A->>私有信道: 射频广播语音数据
        
        私有信道->>TK8620_B: 接收语音数据
        TK8620_B->>成员设备: 通过UART上报语音数据
        成员设备->>成员App: 透传语音数据
        成员App->>成员App: TCodec解码还原
        成员App->>成员App: 扬声器播放语音
        
        发起者App->>发起者App: 用户松开PTT按钮
        发起者App->>发起者App: 停止语音采集
        发起者App->>发起者设备: 发送语音结束标识
        发起者设备->>私有信道: 广播语音结束信号
        私有信道->>成员设备: 转发结束信号
        成员设备->>成员App: 停止播放语音
    end
    
    rect rgb(255, 240, 240)
        Note over 发起者App,成员App: 3. 结束PTT通信
        发起者App->>发起者设备: 退出PTT通信界面
        发起者设备->>私有信道: 发送"结束PTT会话"通知
        私有信道->>成员设备: 广播会话结束
        成员设备->>成员App: 结束PTT界面
    end
```

<div style="page-break-before: always;"></div>

**PTT信道占用检测时序图:**

```mermaid
sequenceDiagram
    participant 用户A as 用户A App
    participant 设备A as 设备A
    participant 信道 as 私有信道
    participant 设备B as 设备B
    participant 用户B as 用户B App
    
    Note over 用户A,用户B: PTT信道占用检测机制
    
    用户A->>设备A: 按下PTT按钮
    设备A->>信道: 检测信道占用状态
    
    alt 信道空闲
        信道->>设备A: 返回空闲状态
        设备A->>用户A: 开始语音采集
        设备A->>信道: 发送语音数据
        Note over 设备A,信道: 获得发言权
        
        用户B->>设备B: 同时按下PTT按钮
        设备B->>信道: 检测信道状态
        信道->>设备B: 返回忙碌状态
        设备B->>用户B: 提示"信道忙碌，请稍后"
        
    else 信道忙碌
        信道->>设备A: 返回忙碌状态
        设备A->>设备A: 随机退避等待
        设备A->>信道: 重新检测信道
        信道->>设备A: 返回空闲状态
        设备A->>用户A: 开始语音采集
    end
    
    设备A->>信道: 语音传输完成
    设备A->>信道: 释放信道占用
    Note over 设备A,信道: 释放发言权
```

### 6.4. 实时对讲模式 (Real-time Voice Mode)

多人实时对讲模式是一种支持多用户同时语音通信的模式，该模式适用于团队协作和复杂场景下的语音通信需求。

#### 6.4.1. 通信机制

- **全双工通信**
  - 同一时间支持多个用户同时发言和接收语音数据
  - TK8620处于同步工作模式
  - 不同用户分配不同的发送时隙资源，避免相互干扰

- **语音优先级管理**
  - 支持抢占式对讲和语音优先级管理：
    - **抢占式对讲**：先发起语音消息用户将更早获得发送时隙
    - **队列管理**：低优先级用户的语音会被暂时缓存，待高优先级语音播放完成后再播放

- **语音压缩与抗干扰**
  - 支持基于BCNBits的信道控制策略，在多个信道间快速切换，避免持续干扰
  - 语音数据采用 TCodec 编码，支持0.5-50kbps下的高语音清晰度

#### 6.4.2. 工作流程

- **用户操作：加入实时对讲**
  - 发起者点击界面内的语音通话按钮发起通话，成功后根据组内成员数量划分信道时隙，并广播通知群组成员
  - 其他组内成员若试图加入实时对讲，需根据发起者广播的时隙配置设置自身时隙，加入后所有时隙默认为接收时隙

- **时隙分配：支持最多4人同时讲话的多人实时对讲**
  - 加入语音通话的用户，在发送语音数据前需向发起者申请固定发送时隙，发起者维护系统当前的时隙使用情况，为用户分配一个空闲的发送时隙
  - 已分配发送时隙的用户，若在超时时间内没有语音数据发送，系统将自动回收该用户已分配的发送时隙
  - 若当前可用时隙资源不足，即申请发送时隙失败，系统应提示用户"当前发言人数较多，请稍后再试"

- **App操作：语音采集和编码**
  - 发送方手机通过麦克风采集用户语音
  - 发送方使用TCodec算法对语音数据进行压缩和编码，生成语音数据
  - 接收方使用TCodec解码算法还原语音数据，并通过扬声器播放

- **用户操作：退出实时对讲**
  - 用户可以随时选择退出通话界面，释放已分配的发送时隙

#### 6.4.3. 实时对讲模式时序图

<div style="page-break-before: always;"></div>

**实时对讲建立和时隙分配时序图:**

```mermaid
sequenceDiagram
    participant 发起者 as 发起者
    participant 信道 as 私有信道
    participant 成员A as 成员A
    participant 成员B as 成员B
    
    Note over 发起者,成员B: 实时对讲流程
    
    rect rgb(240, 248, 255)
        Note over 发起者,成员B: 1. 建立会话
        发起者->>信道: 发起实时对讲
        信道->>成员A: 广播邀请
        信道->>成员B: 广播邀请
        成员A->>信道: 加入对讲
        成员B->>信道: 加入对讲
    end
    
    rect rgb(240, 255, 240)
        Note over 发起者,成员B: 2. 时隙分配
        成员A->>发起者: 申请时隙
        发起者->>成员A: 分配时隙1
        成员B->>发起者: 申请时隙
        发起者->>成员B: 分配时隙2
    end
    
    rect rgb(255, 248, 240)
        Note over 发起者,成员B: 3. 语音通信
        par 并行传输
            成员A->>信道: 时隙1语音
            成员B->>信道: 时隙2语音
            信道->>发起者: 混音播放
        end
    end
    
    rect rgb(255, 240, 240)
        Note over 发起者,成员B: 4. 结束会话
        成员A->>发起者: 释放时隙
        成员B->>发起者: 退出对讲
    end
```

<div style="page-break-before: always;"></div>

**时隙同步和管理时序图:**

```mermaid
sequenceDiagram
    participant 发起者 as 发起者
    participant 信道 as TurMass信道
    participant 用户A as 用户A
    participant 用户B as 用户B
    participant 用户C as 用户C
    
    Note over 发起者,用户C: 多人实时对讲时隙管理
    
    rect rgb(240, 248, 255)
        Note over 发起者,用户C: 时隙同步
        发起者->>信道: 广播时隙同步信号
        信道->>用户A: 分配时隙1 (0-333ms)
        信道->>用户B: 分配时隙2 (333-666ms)
        信道->>用户C: 分配时隙3 (666-1000ms)
    end
    
    rect rgb(240, 255, 240)
        Note over 发起者,用户C: 正常通信
        par 时隙轮转
            用户A->>信道: 时隙1发送语音
            用户B->>信道: 时隙2发送语音
            用户C->>信道: 时隙3发送语音
        and 语音接收
            信道->>发起者: 混音播放
        end
    end
    
    rect rgb(255, 240, 240)
        Note over 发起者,用户C: 冲突处理
        用户A->>信道: 错误时隙发送
        用户B->>信道: 正确时隙发送
        信道->>发起者: 检测冲突并重新同步
        发起者->>信道: 广播新同步信号
    end
```

## 7. 工作机制 (Working Mechanisms)

aiTalk 系统的工作机制是保证系统稳定运行和优化通信效果的核心技术。本章详细介绍了系统的自动通信速率调整机制和语音丢包处理机制，这些机制通过智能算法和自适应技术，确保在各种网络环境下都能提供稳定、高质量的通信服务。

### 7.1. 自动通信速率调整机制

自动通信速率调整机制是 aiTalk 系统的核心智能功能之一，能够根据实时的网络环境和信号质量，动态调整通信速率以获得最佳的通信效果。该机制通过持续监测链路质量指标，实现通信距离与数据传输率的最优平衡。

#### 7.1.1. 机制概述

**核心功能:**
- 实时监测信号强度(RSSI)、信噪比(SNR)、丢包率等关键指标
- 基于多维度评分算法自动选择最适合的通信速率
- 支持5级速率动态切换，覆盖1200bps-19200bps范围
- 具备快速响应能力，适应网络环境变化

**工作原理:**
系统通过采集链路质量数据，运用加权评分算法计算网络状态综合得分，然后根据预设阈值自动选择对应的通信速率等级。

#### 7.1.2. 速率调整时序图

```mermaid
sequenceDiagram
    participant App as App应用
    participant Monitor as 链路监测模块
    participant Algorithm as 评分算法
    participant Controller as 速率控制器
    participant TK8620 as TK8620芯片
    
    Note over App,TK8620: 自动通信速率调整流程
    
    rect rgb(240, 248, 255)
        Note over App,TK8620: 1. 初始化和监测启动
        App->>Monitor: 启动链路监测
        Monitor->>TK8620: 请求信号质量数据
        TK8620->>Monitor: 返回RSSI/SNR/丢包率
        Monitor->>Algorithm: 提交质量数据
    end
    
    rect rgb(240, 255, 240)
        Note over App,TK8620: 2. 评分计算和速率决策
        Algorithm->>Algorithm: 计算RSSI权重(30%)
        Algorithm->>Algorithm: 计算SNR权重(25%)
        Algorithm->>Algorithm: 计算丢包率权重(25%)
        Algorithm->>Algorithm: 计算误码率权重(20%)
        Algorithm->>Algorithm: 综合评分
        Algorithm->>Controller: 推荐速率等级
    end
    
    rect rgb(255, 248, 240)
        Note over App,TK8620: 3. 速率应用和验证
        Controller->>TK8620: 设置新通信速率
        TK8620->>Controller: 确认速率更改
        Controller->>App: 通知速率变更
        App->>Monitor: 验证通信效果
    end
    
    rect rgb(255, 240, 240)
        Note over App,TK8620: 4. 持续监测
        Monitor->>TK8620: 定期获取质量数据
        TK8620->>Monitor: 实时质量反馈
        Monitor->>Algorithm: 触发重新评估
        Note over Monitor,Algorithm: 循环监测和调整
    end
```

**速率自适应算法:**

```c
// 通信速率等级
typedef enum {
    RATE_LEVEL_1 = 0,   // 1200 bps - 最远距离
    RATE_LEVEL_2,       // 2400 bps - 远距离
    RATE_LEVEL_3,       // 4800 bps - 中距离
    RATE_LEVEL_4,       // 9600 bps - 近距离
    RATE_LEVEL_5        // 19200 bps - 很近距离
} CommRateLevel;

// 链路质量评估
typedef struct {
    uint8_t  rssi;              // 信号强度
    uint8_t  snr;               // 信噪比
    uint8_t  packetLossRate;    // 丢包率
    uint8_t  bitErrorRate;      // 误码率
    uint16_t roundTripTime;     // 往返时间
} __attribute__((packed)) LinkQuality;

// 自动速率调整
CommRateLevel AiTalkAutoRateAdjustment(const LinkQuality *quality) {
    // 综合评分算法
    uint8_t score = 0;
    
    // RSSI权重 (30%)
    if (quality->rssi > -70) score += 30;
    else if (quality->rssi > -80) score += 20;
    else if (quality->rssi > -90) score += 10;
    
    // SNR权重 (25%)
    if (quality->snr > 20) score += 25;
    else if (quality->snr > 15) score += 20;
    else if (quality->snr > 10) score += 15;
    else if (quality->snr > 5) score += 10;
    
    // 丢包率权重 (25%)
    if (quality->packetLossRate < 1) score += 25;
    else if (quality->packetLossRate < 5) score += 20;
    else if (quality->packetLossRate < 10) score += 15;
    else if (quality->packetLossRate < 20) score += 10;
    
    // 误码率权重 (20%)
    if (quality->bitErrorRate < 0.01) score += 20;
    else if (quality->bitErrorRate < 0.1) score += 15;
    else if (quality->bitErrorRate < 1) score += 10;
    else if (quality->bitErrorRate < 5) score += 5;
    
    // 根据评分选择速率
    if (score >= 80) return RATE_LEVEL_5;
    else if (score >= 60) return RATE_LEVEL_4;
    else if (score >= 40) return RATE_LEVEL_3;
    else if (score >= 20) return RATE_LEVEL_2;
    else return RATE_LEVEL_1;
}
```

### 7.2. 语音丢包处理机制

语音丢包处理机制是确保 aiTalk 系统在不稳定网络环境下仍能提供高质量语音通信的关键技术。该机制通过先进的丢包检测、恢复和补偿算法，最大程度地减少网络丢包对语音质量的影响，保证用户获得连续、清晰的语音体验。

#### 7.2.1. 机制概述

**核心技术:**
- 基于序列号的实时丢包检测
- 多级丢包恢复策略（FEC、插值、静音填充）
- 语音包重排序和缓冲管理
- 自适应缓冲区大小调整

**设计目标:**
- 单包丢失恢复率 > 95%
- 连续3包丢失可接受恢复
- 总体语音连续性 > 90%
- 延迟增加 < 50ms

#### 7.2.2. 丢包处理时序图

```mermaid
sequenceDiagram
    participant Sender as 发送端
    participant Network as 网络传输
    participant Receiver as 接收端
    participant Recovery as 恢复模块
    participant Player as 播放器
    
    Note over Sender,Player: 语音丢包处理流程
    
    rect rgb(240, 248, 255)
        Note over Sender,Player: 1. 正常传输
        Sender->>Network: 发送语音包Seq=1
        Network->>Receiver: 传输成功
        Receiver->>Player: 正常播放Seq=1
        
        Sender->>Network: 发送语音包Seq=2
        Network->>X: 传输丢失
        Note over Network: 包丢失
        
        Sender->>Network: 发送语音包Seq=3
        Network->>Receiver: 传输成功
    end
    
    rect rgb(240, 255, 240)
        Note over Sender,Player: 2. 丢包检测
        Receiver->>Receiver: 检测序列号跳跃
        Receiver->>Recovery: 触发丢包恢复(Seq=2缺失)
        Recovery->>Recovery: 分析丢包类型(单包丢失)
    end
    
    rect rgb(255, 248, 240)
        Note over Sender,Player: 3. 恢复处理
        Recovery->>Recovery: 使用FEC前向错误更正
        Recovery->>Recovery: 基于Seq=1和Seq=3插值
        Recovery->>Receiver: 生成恢复的Seq=2包
        Receiver->>Player: 播放恢复的语音
    end
    
    rect rgb(255, 240, 240)
        Note over Sender,Player: 4. 质量评估
        Player->>Recovery: 反馈播放质量
        Recovery->>Recovery: 更新恢复算法参数
        Recovery->>Receiver: 调整缓冲区策略
    end
```

**丢包检测与恢复:**

```c
// 语音包头结构
typedef struct {
    uint32_t sequenceNumber;    // 序列号
    uint32_t timestamp;         // 时间戳
    uint8_t  codecType;         // 编码类型
    uint8_t  frameType;         // 帧类型
    uint16_t frameSize;         // 帧大小
    uint8_t  redundancy;        // 冗余等级
} __attribute__((packed)) VoicePacketHeader;

// 丢包检测缓冲区
typedef struct {
    uint32_t expectedSeq;       // 期望序列号
    uint32_t lastReceivedSeq;   // 最后接收序列号
    uint8_t  lossCount;         // 丢包计数
    uint8_t  totalCount;        // 总包计数
    uint8_t  buffer[VOICE_BUFFER_SIZE]; // 重排序缓冲区
} __attribute__((packed)) VoicePacketBuffer;

// 丢包处理算法
int AiTalkVoicePacketLossHandling(const VoicePacketHeader *header, 
                                 const uint8_t *payload,
                                 VoicePacketBuffer *buffer) {
    // 1. 检测丢包
    if (header->sequenceNumber != buffer->expectedSeq) {
        if (header->sequenceNumber > buffer->expectedSeq) {
            // 检测到丢包
            uint32_t lostCount = header->sequenceNumber - buffer->expectedSeq;
            buffer->lossCount += lostCount;
            
            // 2. 丢包恢复策略
            if (lostCount == 1) {
                // 单包丢失 - 使用前向错误更正
                AiTalkVoiceFECRecover(buffer, header->sequenceNumber - 1);
            } else if (lostCount <= 3) {
                // 少量丢包 - 使用插值恢复
                AiTalkVoiceInterpolation(buffer, 
                                        buffer->expectedSeq, 
                                        header->sequenceNumber - 1);
            } else {
                // 大量丢包 - 静音填充
                AiTalkVoiceSilenceFill(buffer, 
                                      buffer->expectedSeq, 
                                      header->sequenceNumber - 1);
            }
        }
        // 处理乱序包
        else {
            AiTalkVoiceReorderPacket(buffer, header, payload);
            return 0;
        }
    }
    
    // 3. 存储当前包
    buffer->expectedSeq = header->sequenceNumber + 1;
    buffer->lastReceivedSeq = header->sequenceNumber;
    buffer->totalCount++;
    
    return 0;
}
```

### 7.3. 低功耗管理机制 (Power-Saving Mechanism)

- **目标**：保证实时通信情况下尽可能降低功耗，延长整机续航  
- **范围**：TK8620 MCU & Sub-G 射频；BLE 维持心跳，不进入深度休眠  
- **唤醒源**  
  1. **GPIO**：外部按键或 App 侧 BLE 命令拉高 WAKE 引脚  
  2. **无线唤醒**：TK8620 低功耗监听专用 Sub-G 唤醒帧  

#### 7.3.1 机制概述

| 项目 | 描述 |
| ---- | ---- |
| **核心功能** | 自动休眠/唤醒 |
| **休眠阈值** | 连续空闲 ≥ 15 s |
| **唤醒条件** | GPIO、无线唤醒帧、BLE 指令 |
| **唤醒动作** | 接收唤醒信号 → 恢复时钟 → 重启 RF |

> 统一的休眠 / 唤醒接口 + 自适应速率算法，可实现全天候 ≥ 24 h 续航。

#### 7.3.2. 低功耗时序图

```mermaid
sequenceDiagram
    participant App as App应用
    participant Ctrl as 低功耗控制器
    participant TK8620 as TK8620芯片
    participant Remote as 远端设备(唤醒源)

    Note over App,TK8620: 低功耗进入与无线唤醒流程

    rect rgb(240, 248, 255)
        Note over App,TK8620: 1. 空闲判定与休眠入口
        App->>Ctrl: 业务空闲事件
        Ctrl->>TK8620: AT+WAKEUPCFG (配置休眠/唤醒参数)
        TK8620-->>Ctrl: CFG_OK
        Ctrl->>TK8620: AT+ENTERSLEEP (进入超低功耗监听)
        TK8620-->>Ctrl: SLEEP_OK
    end

    rect rgb(240, 255, 240)
        Note over App,TK8620: 2. 休眠期间（保持 BLE 心跳）
        loop Keep-Alive
            App-->>TK8620: BLE KeepAlive
            TK8620-->>App: ACK
        end
    end

    rect rgb(255, 248, 220)
        Note over App,TK8620: 3. 主动唤醒（App侧需发送业务数据）
        App->>Ctrl: 业务发送请求
        Ctrl->>TK8620: BLE Cmd 拉高 GPIO
        TK8620-->>Ctrl: WAKE_INT
        Note over Ctrl,App: 等待唤醒窗口 (≈500-1000 ms)
        App->>TK8620: 正常业务数据
    end

    rect rgb(255, 248, 240)
        Note over App,TK8620: 4. 无线唤醒
        Remote->>TK8620: SubG Wake Frame
        TK8620-->>Ctrl: WAKE_INT
        Ctrl->>TK8620: 恢复时钟 & 射频
    end
 
    rect rgb(255, 240, 240)
        Note over App,TK8620: 5. 恢复正常工作
        App->>TK8620: 正常业务数据
    end
```

**低功耗控制算法:**

```c
// 低功耗速率等级
typedef enum {
    LOW_POWER_RATE_1 = 0,   // 1200 bps - 最低功耗
    LOW_POWER_RATE_2,       // 2400 bps - 较低功耗
    LOW_POWER_RATE_3,       // 4800 bps - 中等功耗
    LOW_POWER_RATE_4,       // 9600 bps - 较高功耗
    LOW_POWER_RATE_5        // 19200 bps - 最高功耗
} LowPowerRateLevel;

// 链路质量评估
typedef struct {
    uint8_t  rssi;              // 信号强度
    uint8_t  snr;               // 信噪比
    uint8_t  packetLossRate;    // 丢包率
    uint8_t  bitErrorRate;      // 误码率
    uint16_t roundTripTime;     // 往返时间
} __attribute__((packed)) LinkQuality;

// 低功耗速率选择
LowPowerRateLevel AiTalkLowPowerRateSelection(const LinkQuality *quality) {
    // 综合评分算法
    uint8_t score = 0;
    
    // RSSI权重 (30%)
    if (quality->rssi > -70) score += 30;
    else if (quality->rssi > -80) score += 20;
    else if (quality->rssi > -90) score += 10;
    
    // SNR权重 (25%)
    if (quality->snr > 20) score += 25;
    else if (quality->snr > 15) score += 20;
    else if (quality->snr > 10) score += 15;
    else if (quality->snr > 5) score += 10;
    
    // 丢包率权重 (25%)
    if (quality->packetLossRate < 1) score += 25;
    else if (quality->packetLossRate < 5) score += 20;
    else if (quality->packetLossRate < 10) score += 15;
    else if (quality->packetLossRate < 20) score += 10;
    
    // 误码率权重 (20%)
    if (quality->bitErrorRate < 0.01) score += 20;
    else if (quality->bitErrorRate < 0.1) score += 15;
    else if (quality->bitErrorRate < 1) score += 10;
    else if (quality->bitErrorRate < 5) score += 5;
    
    // 根据评分选择速率
    if (score >= 80) return LOW_POWER_RATE_5;
    else if (score >= 60) return LOW_POWER_RATE_4;
    else if (score >= 40) return LOW_POWER_RATE_3;
    else if (score >= 20) return LOW_POWER_RATE_2;
    else return LOW_POWER_RATE_1;
}
```

#### 7.3.3. 设备休眠与唤醒流程 (Sleep & Wake Workflow)

1. **8620 休眠配置**：App 通过 AT 指令 `AT+WAKEUPCFG` 下发 GPIO 及无线唤醒参数（包含唤醒引脚、电平、无线唤醒帧标识等）。
2. **进入休眠**：App 发送 `AT+ENTERSLEEP` 指令，使 8620 MCU 与射频收发器进入深度休眠，保留唤醒源。
3. **无线被动唤醒**：8620 在 SubG 通道待机监听极低功耗接收机；当捕获到带有唤醒标识的无线信标包时，自动唤醒并切换到工作模式。
4. **主动数据发送流程**：
   1) App 通过 BLE GPIO 信号拉高唤醒引脚，唤醒本机 8620；
   2) App 向私有信道广播"无线唤醒指令"，唤醒处于休眠的其他设备；
   3) 等待固定唤醒窗口（例如 500 - 1000 ms）后，按正常流程发送文字、语音等业务数据。

## 8. 系统状态机设计 (System State Machine Design)

### 8.1. 设备状态机

```mermaid
stateDiagram-v2
    [*] --> PowerOff
    PowerOff --> Booting : 开机
    Booting --> Pairing : 进入配对模式
    Booting --> Idle : 正常启动
    
    Pairing --> Idle : 配对成功
    Pairing --> PowerOff : 配对失败/关机
    
    Idle --> BLE_Connected : BLE连接成功
    BLE_Connected --> TurMass_Active : 启动TurMass
    TurMass_Active --> Network_Joined : 加入网络
    
    Network_Joined --> Voice_TX : 发送语音
    Network_Joined --> Voice_RX : 接收语音
    Network_Joined --> Data_TX : 发送数据
    Network_Joined --> Data_RX : 接收数据
    
    Voice_TX --> Network_Joined : 语音结束
    Voice_RX --> Network_Joined : 播放结束
    Data_TX --> Network_Joined : 数据发送完成
    Data_RX --> Network_Joined : 数据接收完成
    
    Network_Joined --> TurMass_Active : 网络断开
    TurMass_Active --> BLE_Connected : 关闭TurMass
    BLE_Connected --> Idle : BLE断开
    
    Idle --> LowPower : 低功耗模式
    LowPower --> Idle : 唤醒
    
    Idle --> PowerOff : 关机
    BLE_Connected --> PowerOff : 关机
    TurMass_Active --> PowerOff : 关机
    Network_Joined --> PowerOff : 关机
```

### 8.2. App 状态机

```mermaid
stateDiagram-v2
    [*] --> AppStartup
    AppStartup --> DeviceScanning : 自动扫描设备
    AppStartup --> MainScreen : 已有连接设备
    
    DeviceScanning --> DeviceConnecting : 找到设备
    DeviceConnecting --> DeviceConnected : 连接成功
    DeviceConnecting --> DeviceScanning : 连接失败
    
    DeviceConnected --> NetworkActive : TurMass启动
    NetworkActive --> InGroup : 加入群组
    
    InGroup --> PTT_Speaking : PTT通话
    InGroup --> PTT_Listening : 接收PTT
    InGroup --> RealTime_Calling : 实时通话
    InGroup --> Messaging : 发送消息
    
    PTT_Speaking --> InGroup : 结束通话
    PTT_Listening --> InGroup : 通话结束
    RealTime_Calling --> InGroup : 结束通话
    Messaging --> InGroup : 消息发送完成
    
    InGroup --> NetworkActive : 退出群组
    NetworkActive --> DeviceConnected : 关闭网络
    DeviceConnected --> DeviceScanning : 设备断开
    
    MainScreen --> DeviceScanning : 手动连接设备
    DeviceScanning --> MainScreen : 取消连接
```

## 9. 错误处理与异常恢复 (Error Handling & Recovery)

### 9.1. 错误分类与处理策略

```mermaid
graph TB
    subgraph "Error Categories"
        CONN_ERR["连接错误"]
        PROTO_ERR["协议错误"]
        DATA_ERR["数据错误"]
        HW_ERR["硬件错误"]
        APP_ERR["应用错误"]
    end
    
    subgraph "Connection Errors"
        BLE_DISC["BLE断连"]
        NET_FAIL["网络故障"]
        TIMEOUT["超时错误"]
    end
    
    subgraph "Protocol Errors"
        PKT_CORRUPT["数据包损坏"]
        VERSION_MISMATCH["版本不匹配"]
        AUTH_FAIL["认证失败"]
    end
    
    subgraph "Recovery Strategies"
        RETRY["重试机制"]
        FALLBACK["降级服务"]
        RESET["重置连接"]
        NOTIFY["用户通知"]
    end
    
    CONN_ERR --> BLE_DISC
    CONN_ERR --> NET_FAIL
    CONN_ERR --> TIMEOUT
    
    PROTO_ERR --> PKT_CORRUPT
    PROTO_ERR --> VERSION_MISMATCH
    PROTO_ERR --> AUTH_FAIL
    
    BLE_DISC --> RETRY
    NET_FAIL --> FALLBACK
    TIMEOUT --> RESET
    PKT_CORRUPT --> RETRY
    VERSION_MISMATCH --> NOTIFY
    AUTH_FAIL --> RESET
```

### 9.2. 异常恢复流程

```mermaid
flowchart TD
    ERROR_DETECT["检测到错误"]
    CLASSIFY["错误分类"]
    RECOVERABLE{"是否可恢复"}
    
    RETRY_COUNT["检查重试次数"]
    MAX_RETRY{"达到最大重试次数"}
    EXECUTE_RECOVERY["执行恢复策略"]
    
    FALLBACK["降级服务"]
    USER_NOTIFY["通知用户"]
    LOG_ERROR["记录错误日志"]
    
    ERROR_DETECT --> CLASSIFY
    CLASSIFY --> RECOVERABLE
    RECOVERABLE -->|是| RETRY_COUNT
    RECOVERABLE -->|否| USER_NOTIFY
    
    RETRY_COUNT --> MAX_RETRY
    MAX_RETRY -->|否| EXECUTE_RECOVERY
    MAX_RETRY -->|是| FALLBACK
    
    EXECUTE_RECOVERY --> LOG_ERROR
    FALLBACK --> USER_NOTIFY
    USER_NOTIFY --> LOG_ERROR
```

## 10. 非功能性考虑 (Non-Functional Considerations)

### 10.1. 性能

- **通信吞吐量:** TurMass SubG 协议需确保足够的带宽支持多路语音和数据传输，尤其是在 Mesh 网络中。
- **低延迟:** PTT 语音传输应保证毫秒级延迟，实时通话接近实时。
- **功耗优化:** 设备固件和 App 需在通信效率和功耗之间取得平衡，延长设备续航和手机待机时间。

### 10.2. 可靠性

- **TurMass 网络鲁棒性:** Mesh 组网应能自愈，应对节点增减、移动、失效等情况。
- **数据重传机制:** BLE 和 TurMass SubG 层均应包含有效的数据重传和确认机制，降低丢包率。
- **App 稳定性:** 严谨的异常处理和崩溃日志收集（本地），确保 App 稳定运行。

### 10.3. 安全性

- **数据加密:** 从 App 到设备，再到 TurMass 网络，所有数据传输都应采用强大的端到端加密机制。
- **连接认证:** BLE 配对和 TurMass 网络加入应有严格的认证流程，防止非法设备接入。
- **隐私保护:** 由于无需云服务器，用户数据仅存在于本地和设备间传输，进一步强化隐私保护。

### 10.4. 可扩展性

- **模块化设计:** 硬件、固件、App 各模块之间接口清晰，便于未来功能扩展和技术升级（如 TurMass 协议版本升级、支持更多传感器）。
- **协议兼容性:** TurMass 协议应预留扩展性，以便未来支持更多数据类型或应用场景。

### 10.5. 全球合规性

- **射频模块可替换:** 硬件设计应考虑 TurMass SubG 射频模块的可替换性，以便针对不同国家和地区的频段要求进行定制。
- **固件可配置:** 固件应支持通过配置方式适配不同区域的频点、功率限制和信道管理策略。

## 11. 风险与挑战 (Risks & Challenges)

- **TurMass SubG 技术成熟度与稳定性:** 作为独创技术，其在实际复杂环境（如建筑物、山地）下的性能、抗干扰能力、网络吞吐量和稳定性需充分验证。
- **Mesh 组网复杂性:** Mesh 网络的自组织、路由发现、数据转发、尤其是在大量节点和高移动性场景下的性能和稳定性。
- **跨平台 (Flutter) 与原生 BLE/地图 SDK 集成深度:** Flutter 在与底层原生 BLE 模块和离线地图 SDK 进行复杂、高性能交互时，可能面临性能瓶颈或兼容性问题。
- **低功耗与长距离传输平衡:** 在实现长距离传输的同时保持低功耗，是硬件和固件设计上的巨大挑战。
- **全球法规合规性:** 不同地区 SubG 频段和认证要求差异巨大，可能导致产品线复杂化和认证周期延长。
- **无云端模式下的数据同步与备份:** 虽然保证了隐私，但也意味着没有中央服务器进行数据同步和备份，若手机丢失或 App 卸载，数据可能丢失。

## 12. 总结与展望 (Summary & Outlook)

### 12.1. 系统设计总结

aiTalk 系统设计方案全面涵盖了从硬件设备到移动应用的完整技术栈，主要成果包括：

1. **清晰的系统架构:** 明确定义了 aiTalk 设备、aiTalk App 以及 TurMass SubG 网络的整体架构和交互关系。

2. **详细的协议设计:** 基于 MeshWalkie 协议经验，设计了完整的 BLE 通信协议和 TurMass SubG Mesh 协议，采用 Protobuf 消息格式确保高效性和可扩展性。

3. **完善的工作流程:** 设计了设备连接、PTT 通信、群组管理、Mesh 网络自愈等关键工作流程，确保系统功能的完整性和可靠性。

4. **强大的错误处理:** 建立了完善的错误分类、检测和恢复机制，提高系统的健壮性和用户体验。

5. **全面的性能指标:** 定义了详细的性能指标和测试方案，为产品质量保障提供了量化标准。

### 12.2. 技术创新点

1. **TurMass SubG 技术整合:** 将独创的 TurMass SubG 技术与移动设备深度整合，实现了真正的无网长距离通信。

2. **Mesh 网络自愈:** 基于 MeshWalkie 协议设计了智能的 Mesh 网络自愈机制，提高了网络的可靠性和覆盖范围。

3. **端到端加密:** 设计了从应用层到物理层的全链路加密方案，确保通信安全和用户隐私。

4. **低功耗优化:** 在硬件和固件层面进行了深度的低功耗优化，平衡了性能与续航需求。

### 12.3. 未来发展方向

1. **AI 集成:** 集成 AI 语音识别和自然语言处理技术，提升语音通信的智能化水平。

2. **物联网扩展:** 支持更多物联网设备的接入，构建更大规模的 Mesh 网络生态。

3. **增强现实 (AR) 支持:** 结合 AR 技术，提供更直观的地理位置和导航服务。

4. **卫星通信备份:** 在极端环境下提供卫星通信作为备份通信手段。

5. **边缘计算:** 在 Mesh 网络中引入边缘计算能力，支持更复杂的分布式应用。

### 12.4. 市场前景

aiTalk 系统设计针对无网通信市场的巨大需求，具有广阔的应用前景：

- **户外运动市场:** 满足登山、徒步、探险等户外活动的通信需求
- **应急救援领域:** 为灾难救援、搜救行动提供可靠的通信保障
- **工业应用:** 支持建筑工地、矿山、油田等工业环境的作业通信
- **智慧城市:** 作为城市应急通信网络的重要组成部分

通过持续的技术创新和市场推广，aiTalk 有望成为无网通信领域的领先解决方案，为用户提供无处不在的通信服务。

## 13. 附录 (Appendix)

### 13.1. 术语表 (Glossary)

- **SDD:** System Design Document，系统设计文档。
- **BLE:** Bluetooth Low Energy，低功耗蓝牙。
- **TurMass SubG:** aiTalk 独创的 Sub-Gigahertz (低于 1GHz) 无线通信技术。
- **PTT:** Push To Talk，即时语音对讲方式。
- **Mesh 组网:** 网状网络拓扑结构。
- **OTA:** Over-The-Air，远程空中下载。
- **MCU:** Microcontroller Unit，微控制器单元。
- **PMU:** Power Management Unit，电源管理单元。
- **Flutter:** Google 开发的开源 UI 软件开发工具包。
- **Platform Channels:** Flutter 用于与原生平台代码通信的机制。
- **Protobuf:** Protocol Buffers，谷歌开发的数据序列化格式。
- **GATT:** Generic Attribute Profile，蓝牙通用属性协议。
- **TTL:** Time To Live，数据包生存时间。
- **Mesh:** 网状网络拓扑，节点之间可以多路径通信。
- **MeshWalkie:** 基于 Meshtastic 的开源 Mesh 通信协议。

### 13.2. 协议状态码定义

| 状态码 | 名称 | 描述 |
|--------|------|------|
| 0x00 | SUCCESS | 操作成功 |
| 0x01 | TIMEOUT | 操作超时 |
| 0x02 | CONNECTION_FAILED | 连接失败 |
| 0x03 | AUTHENTICATION_FAILED | 认证失败 |
| 0x04 | INVALID_PARAMETER | 参数无效 |
| 0x05 | RESOURCE_UNAVAILABLE | 资源不可用 |
| 0x06 | PROTOCOL_ERROR | 协议错误 |
| 0x07 | HARDWARE_ERROR | 硬件错误 |
| 0x08 | NETWORK_ERROR | 网络错误 |
| 0x09 | BUFFER_OVERFLOW | 缓冲区溢出 |
| 0x0A | PERMISSION_DENIED | 权限拒绝 |
| 0x0B | DATA_CORRUPTION | 数据损坏 |

### 13.3. 参考资料 (References)

- aiTalk 产品需求文档 (PRD) [aiTalk™ 产品需求文档 - v0.5]
- aiTalk App 设计方案 (ADD) [aiTalk App 设计方案 (ADD) -v0.1]
- AI Talk应用软件通信协议 [AI Talk应用软件通信协议 - v0.3]
- 蓝牙 BLE 核心规范：[https://www.bluetooth.com/specifications/specs/]
- Flutter 官方文档：[https://flutter.dev/docs]
- Protocol Buffers 文档：[https://developers.google.com/protocol-buffers]
- Meshtastic 协议文档：[https://meshtastic.org/docs/overview/]

### 13.4. 接口规范汇总

#### 13.4.1. BLE GATT 服务 UUID

| 服务名称 | UUID | 描述 |
|----------|------|------|
| Device Information Service | 0x180A | 标准设备信息服务 |
| Control Service | 0x1800 | aiTalk 控制服务 |
| Data Transfer Service | 0x1801 | aiTalk 数据传输服务 |
| Status Notification Service | 0x1802 | aiTalk 状态通知服务 |

#### 13.4.2. TurMass SubG 信道配置

| 信道号 | 频率 (MHz) | 用途 | 备注 |
|--------|------------|------|------|
| 1-16 | 470.0-479.5 | 公共信道 | 固定密钥 |
| 17-32 | 480.0-489.5 | 私有信道 | 可变密钥 |

#### 13.4.3. 性能基准参数

| 参数 | 目标值 | 测试条件 |
|------|--------|----------|
| BLE 连接延迟 | < 3秒 | 理想环境 |
| PTT 语音延迟 | < 1秒 | 单跳传输 |
| 通信距离 | > 5km | 开阔地带 |
| 设备续航 | > 24小时 | 正常使用 |
| 待机时间 | > 72小时 | 低功耗模式 |
