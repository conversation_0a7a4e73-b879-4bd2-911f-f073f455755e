#!/usr/bin/env python3
"""challenge.py – Host-side helper script for TK8620 AT +CHALLENGE command.

Usage
-----
python challenge.py COM3              # Windows (replace with correct COMx)
python challenge.py /dev/ttyUSB0 -b 1 # Linux (specify port device)

What it does
------------
1. Generates a 32-byte challenge message consisting of:
   • 24 random bytes (cryptographically secure).
   • 8-byte timestamp (TS).  The TS rule is:
       - Little-endian, unsigned 64-bit integer.
       - Number of *seconds* since Unix epoch (1970-01-01 00:00:00 UTC).
       - Guarantees uniqueness & freshness while fitting micro-controller constraints.
2. Converts message to HEX and sends:
       AT+CHALLENGE=<hex_string>\r\n
3. Waits for modem reply and prints received lines (+CHALLENGE:<sig>, +PRIVKEY: etc.).

Dependencies:  pip install pyserial
"""
import argparse
import binascii
import os
import struct
import sys
import time
from typing import List

import serial  # type: ignore
# from ecdsa
from ecdsa import Verifying<PERSON><PERSON>, NIST256p, util  # type: ignore
# from ecdsa.errors import MalformedPointError
from ecdsa.errors import MalformedPointError  # type: ignore

BAUDRATE = 115200
TIMEOUT = 3  # seconds

# ---- fixed public key provided by client ----
PK_FIXED_HEX = (
    "ACB211BD84A8C15C7399B581AA772F4F290589F26AB8FADDFD2BC96DDA101E42"
    "B64C000BA41953225BB3AF67A72A38E7BC98DC1BD2DE6DE5C60B25C893CF9AC6"
)

PK_FIXED_BYTES = bytes.fromhex(PK_FIXED_HEX)

# Default timezone offset seconds (UTC+8). Adjust if needed.
TZ_OFFSET = 8 * 3600


def build_challenge(offset: int = TZ_OFFSET) -> bytes:
    """Return 32-byte challenge: (rnd XOR pubkey[0:24]) | 8-byte ts.

    Timestamp = current Unix time + offset (defaults to UTC+8) so that
    it is guaranteed to be >= firmware compile time compiled in local TZ.
    """
    rnd = os.urandom(24)
    xor_rnd = bytes([rnd[i] ^ PK_FIXED_BYTES[i] for i in range(24)])
    ts_bytes = struct.pack("<Q", int(time.time()) + offset)
    return xor_rnd + ts_bytes


def send_command(ser: serial.Serial, cmd: str) -> dict:
    ser.write(cmd.encode())
    ser.flush()
    deadline = time.time() + TIMEOUT
    lines: List[str] = []
    result = {"lines": lines, "sig": None, "chipinfo": None}
    while time.time() < deadline:
        if ser.in_waiting:
            line = ser.readline().decode(errors="ignore").strip()
            if line:
                print(line)
                lines.append(line)
                if line.startswith("+CHALLENGE:"):
                    sig_hex = line.split(":", 1)[1].strip()
                    result["sig"] = bytes.fromhex(sig_hex)
                elif line.startswith("+CHIPINFO:"):
                    chipinfo_hex = line.split(":", 1)[1].strip()
                    result["chipinfo"] = bytes.fromhex(chipinfo_hex)
            deadline = time.time() + TIMEOUT
    return result


def verify_signature(challenge: bytes, sig: bytes) -> bool:
    """Verify signature with fixed public key. Returns True if OK."""
    # Build 32-byte hash buffer same as firmware
    if len(challenge) > 32:
        raise ValueError("Challenge length >32 not supported")
    hash_buf = challenge.ljust(32, b"\x00")
    try:
        vk = VerifyingKey.from_string(PK_FIXED_BYTES, curve=NIST256p)
    except MalformedPointError as exc:
        print(f"[ERROR] Public key invalid: {exc}")
        return False

    try:
        return vk.verify_digest(sig, hash_buf, sigdecode=util.sigdecode_string)
    except Exception as exc:
        print(f"[ERROR] Signature verify failed: {exc}")
        return False


def main():
    parser = argparse.ArgumentParser(description="TK8620 +CHALLENGE tool")
    parser.add_argument("port", help="Serial port, e.g. COM3 or /dev/ttyUSB0")
    parser.add_argument("-b", "--baud", type=int, default=BAUDRATE, help="Baudrate (default 115200)")
    args = parser.parse_args()

    try:
        ser = serial.Serial(args.port, args.baud, timeout=0.1)
    except serial.SerialException as e:
        print(f"Cannot open serial port {args.port}: {e}")
        sys.exit(1)

    challenge = build_challenge()
    hex_str = binascii.hexlify(challenge).decode().upper()
    cmd = f"AT+CHALLENGE={hex_str}\r\n"
    print(f"Sending: {cmd.strip()}")

    result = send_command(ser, cmd)
    sig_bytes = result["sig"]
    chipinfo_bytes = result["chipinfo"]

    if sig_bytes is not None:
        ok = verify_signature(challenge, sig_bytes)
        print("Signature verify:", "OK" if ok else "FAIL")
    else:
        print("Signature line not received; cannot verify")

    if chipinfo_bytes is not None:
        print("CHIPINFO ({} bytes): {}".format(len(chipinfo_bytes), chipinfo_bytes.hex().upper()))
    else:
        print("CHIPINFO line not received")

    ser.close()


if __name__ == "__main__":
    main() 