---
type: "always_apply"
---

# aiTalk编码规范

## 命名规范
- **类名**：使用大驼峰命名法（PascalCase），如：`BluetoothService`
- **变量与函数**：使用小驼峰命名法（camelCase），如：`connectDevice()`
- **常量**：使用大写蛇形命名法（UPPER_SNAKE_CASE），如：`MAX_RETRY_COUNT`
- **私有成员**：使用下划线前缀，如：`_privateVariable`

## 代码格式
- 缩进使用2个空格
- 行宽限制在80个字符以内
- 导入语句按以下顺序排列：
  1. Dart SDK库
  2. Flutter库
  3. 第三方库
  4. 相对导入（项目内文件）
- 每个导入组之间有一个空行
- 使用Dart的格式化工具：`dart format`

## 注释规则
- 所有公开API必须有文档注释（///）
- 复杂逻辑需添加行内注释（//）解释
- TODO注释格式：`// TODO(用户名): 内容`

## 异步编程
- 优先使用`async/await`而非`.then()`链式调用
- 确保所有异步操作有适当的错误处理

## 状态管理
- 使用Provider包进行状态管理
- ViewModel负责业务逻辑和状态管理
- UI组件不直接操作数据模型

## 本地存储
- 使用Hive或SQLite进行本地数据存储
- 所有数据库操作封装在专门的服务类中

## 蓝牙通信
- 蓝牙操作必须处理连接异常情况
- 蓝牙通信协议统一封装，不在UI层直接调用底层API
# aiTalk编码规范

## 命名规范
- **类名**：使用大驼峰命名法（PascalCase），如：`BluetoothService`
- **变量与函数**：使用小驼峰命名法（camelCase），如：`connectDevice()`
- **常量**：使用大写蛇形命名法（UPPER_SNAKE_CASE），如：`MAX_RETRY_COUNT`
- **私有成员**：使用下划线前缀，如：`_privateVariable`

## 代码格式
- 缩进使用2个空格
- 行宽限制在80个字符以内
- 导入语句按以下顺序排列：
  1. Dart SDK库
  2. Flutter库
  3. 第三方库
  4. 相对导入（项目内文件）
- 每个导入组之间有一个空行
- 使用Dart的格式化工具：`dart format`

## 注释规则
- 所有公开API必须有文档注释（///）
- 复杂逻辑需添加行内注释（//）解释
- TODO注释格式：`// TODO(用户名): 内容`

## 异步编程
- 优先使用`async/await`而非`.then()`链式调用
- 确保所有异步操作有适当的错误处理

## 状态管理
- 使用Provider包进行状态管理
- ViewModel负责业务逻辑和状态管理
- UI组件不直接操作数据模型

## 本地存储
- 使用Hive或SQLite进行本地数据存储
- 所有数据库操作封装在专门的服务类中

## 蓝牙通信
- 蓝牙操作必须处理连接异常情况
- 蓝牙通信协议统一封装，不在UI层直接调用底层API
